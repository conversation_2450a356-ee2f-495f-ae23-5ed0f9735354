'use client';

import { invoke } from '@tauri-apps/api/core';
import { useState } from 'react';
// import { TrayIcon } from '@tauri-apps/api/tray';
// import { Menu } from '@tauri-apps/api/menu';
// import { defaultWindowIcon } from '@tauri-apps/api/app';

// async function initSystemTray() {
//   const menu = await Menu.new({
//     items: [
//       {
//         id: 'quit2',
//         text: 'Quit2',
//         action: () => {
//           console.log('quit pressed');
//         },
//       },
//     ],
//   });

//   const options = {
//     menu,
//     menuOnLeftClick: true,
//     icon: 'S', // (await defaultWindowIcon())!,
//   };

//   const tray = await TrayIcon.new(options);
// }
// initSystemTray();
export default function Home() {
  const [greetMsg, setGreetMsg] = useState('');
  const [name, setName] = useState('');

  async function greet() {
    setGreetMsg(await invoke('greet', { name }));
  }

  return (
    <main className="container">
      <h1>Welcome to Tauri + Next.js</h1>

      <div className="row">
        <a href="https://nextjs.org" target="_blank" rel="noopener">
          <img src="/next.svg" className="logo next" alt="Next.js logo" />
        </a>
        <a href="https://tauri.app" target="_blank" rel="noopener">
          <img src="/tauri.svg" className="logo tauri" alt="Tauri logo" />
        </a>
        <a href="https://react.dev" target="_blank" rel="noopener">
          <img src="/react.svg" className="logo react" alt="React logo" />
        </a>
      </div>
      <p>Click on the Tauri, Next.js, and React logos to learn more.</p>

      <form
        className="row"
        onSubmit={(e) => {
          e.preventDefault();
          greet();
        }}
      >
        <input
          id="greet-input"
          onChange={(e) => setName(e.currentTarget.value)}
          placeholder="Enter a name..."
        />
        <button type="submit">Greet</button>
      </form>
      <p>{greetMsg}</p>
    </main>
  );
}
