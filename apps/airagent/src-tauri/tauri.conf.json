{"$schema": "https://schema.tauri.app/config/2", "productName": "airagent", "version": "0.1.0", "identifier": "ai.airagent", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "devUrl": "http://localhost:3005", "frontendDist": "../out"}, "app": {"windows": [{"title": "airagent", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}