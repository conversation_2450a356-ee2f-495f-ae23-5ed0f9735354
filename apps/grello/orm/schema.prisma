// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  output        = "./prisma-client"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-musl-openssl-3.0.x", "debian-openssl-1.1.x", "linux-musl"]
}

datasource db {
  provider = "postgresql"
  url      = env("PG_DATABASE_URL")
}

model Project {
  id          String  @id
  name        Json
  description Json?
  starred     <PERSON><PERSON><PERSON> @default(false)

  // createdBy String?
  // updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// init 和 done，是强制系统 state
model ProjectState {
  id String @id

  projectId String
  name      Json

  // createdBy String?
  // updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Extra field for task table
model ProjectField {
  id String @id
}

enum TaskState {
  INIT
  RUNNING
  DONE
}

model Task {
  id        String @id
  projectId String

  name    Json
  content String
  outcome Json? // 运行结果，可选，区别于 tool 和 artifact

  // ID for state, if undefined / null then init
  systemState TaskState
  userStateId String?

  // createdBy String?
  // updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Computer {
  id        String @id
  projectId String
}
