# Grello

**G<PERSON><PERSON> — Generative Trello with AI**  
*Plan by Human, Execute by AI.*

---

## What is <PERSON><PERSON><PERSON>?

**<PERSON>rell<PERSON>** is the world’s first **AI-powered project manager** that goes beyond organizing tasks — it actually **executes them**.  
Think of it as a Trello-like board, but every task card can be assigned not just to humans, but also to **AI models, skillsets, and agents**.

When you assign a task:  
- Humans treat it like a normal to-do list.  
- AI agents execute it automatically (text, code, images, workflows, etc.).  

Every Grello project runs inside its **own isolated virtual computer**, which stores files, logs, and artifacts generated by tasks.

---

## Key Features

- 🗂️ **Trello-like UI** — intuitive Kanban board for task visualization.  
- 🤖 **AI Execution** — assign tasks directly to AI models, skillsets, or agents.  
- 💻 **Per-Project Virtual Computer** — each project has its own file system to store results.  
- 💬 **Chat Copilot** — built-in AI assistant helps refine task details and prompts.  
- 🔌 **API-first Design** — projects and tasks can be created and executed programmatically.  

---

## Quick Start

> ⚠️ All Grello project data is stored under `/apps/grello`.  
> Files outside this directory are not linked to Grello projects.

1. Go to **app.grello.ai/** to view your workspace and projects.  
2. Create a new project.  
3. Add tasks — choose whether to assign them to **Humans** or **AI Executors**.  
4. Switch to the **Computer tab** to see generated outputs in a VSCode-like interface.

---

## UI Overview

### 1. Main Space
- URL: `app.grello.ai/`  
- Displays all spaces and projects (like Trello boards overview).  

### 2. Project Board
- URL: `app.grello.ai/{project_id}`  
- Shows Kanban view of tasks.  
- Tab switcher:  
  - **Tasks** → Kanban task board  
  - **Files** → Virtual Computer (VSCode-like UI for outputs & artifacts)  

### 3. Task Card
- URL: `app.grello.ai/{task_id}`  
- Left panel:  
  - Task Name  
  - Content / Prompt  
  - Executor Selection:  
    - **Human** → Normal to-do task  
    - **AI Agent** → Executes task with:  
      - AI Models (text-to-text, text-to-image, code, etc.)  
      - AI Skillsets (custom workflows)  
- Right panel:  
  - **Chat Copilot** for brainstorming and refining task instructions  

### 4. Virtual Computer
- Each project has an isolated environment (mini VM).  
- Task executions generate files, logs, and artifacts here.  
- Switch to **Computer tab** → see a VSCode-Server-like UI.  

---

## API Endpoints

- `project.new` — create a new project  
- `project.list` — list all projects  
- `task.new` — create a new task  
- `task.run` — execute a task (via AI or human assignment)  

---

## ORM / Database Schema

- ORM: **Prisma** with PostgreSQL backend.  
- Schema defined in [`./orm/schema.prisma`](./orm/schema.prisma).  

Entities include:  
- **Project** → contains tasks + computer environment  
- **Task** → stores prompt, executor, status, and outputs  
- **File** → artifacts stored per project VM  

---

## Tech Stack

- **Frontend**: Next.js  
  - TRPC  
  - Hono Server within NextJS
  - TailwindCSS  
  - `@hello-pangea/dnd` (drag-and-drop Kanban)  
- **Backend**: Prisma + PostgreSQL  
- **Infra / Execution**: [E2B](https://e2b.dev/) (AI sandbox environments)  
- **Testing & Linting**: Vitest, ESLint 9  

---

## Roadmap (Planned)

- [ ] AI multi-agent orchestration  
- [ ] Task dependency management (Gantt-like view)  
- [ ] Marketplace for AI skillsets  
- [ ] GitHub / Notion / Slack / 10k+ MCP integrations  
- [ ] Team collaboration (multi-user projects)  

---

👉 With **Grello**, project management shifts from “tracking tasks” → to actually **getting them done by AI**.
