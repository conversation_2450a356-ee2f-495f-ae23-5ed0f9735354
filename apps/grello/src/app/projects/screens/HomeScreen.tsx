'use client';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import { useState } from 'react';
import { CreateProjectModal } from '../../../components/projects/CreateProjectModal';
import { ProjectGrid } from '../../../components/projects/ProjectGrid';
import { Section } from '../../../components/projects/Section';
import { ThemeSwitcher } from '../../../components/ThemeSwitcher';
import { trpcClient } from '../../../trpc-client/react';
import type { ProjectCardVO } from '../../../types/project';

export function HomeScreen() {
  const { data: projectsData, isLoading } = trpcClient.project.list.useQuery();
  const utils = trpcClient.useUtils();
  const create = trpcClient.project.new.useMutation({
    onSuccess: () => utils.project.list.invalidate(),
  });
  const toggleStar = trpcClient.project.toggleStar.useMutation({
    onSuccess: () => utils.project.list.invalidate(),
  });
  const [showCreate, setShowCreate] = useState(false);

  const projects = (projectsData as ProjectCardVO[] | undefined) || [];
  const starred = projects.filter((p) => p.starred);
  const recent = projects.filter((p) => !p.starred).slice(0, 8);

  return (
    <Box component="main" sx={{ p: 4, minHeight: '100vh', bgcolor: 'background.body' }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={4}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Typography level="h3" sx={{ fontWeight: 600 }}>
            Grello 工作区
          </Typography>
          <ThemeSwitcher />
        </Stack>
        <Button size="md" color="primary" variant="solid" onClick={() => setShowCreate(true)}>
          新建项目
        </Button>
      </Stack>

      {isLoading && <Typography level="body-sm">加载中...</Typography>}

      {starred.length > 0 && (
        <Section title="已加星标">
          <ProjectGrid
            projects={starred}
            onToggleStarAction={(id) => toggleStar.mutate({ projectId: id })}
          />
        </Section>
      )}
      <Section title="最近项目">
        <ProjectGrid
          projects={recent}
          onToggleStarAction={(id) => toggleStar.mutate({ projectId: id })}
        />
      </Section>
      <Section title="全部项目">
        <ProjectGrid
          projects={projects}
          onToggleStarAction={(id) => toggleStar.mutate({ projectId: id })}
        />
      </Section>

      <CreateProjectModal
        open={showCreate}
        closeAction={() => setShowCreate(false)}
        createAction={(name, done) =>
          create.mutate(
            { name },
            {
              onSettled: () => {
                utils.project.list.invalidate();
                done();
              },
            },
          )
        }
      />
    </Box>
  );
}
