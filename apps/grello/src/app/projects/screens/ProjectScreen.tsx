'use client';
import <PERSON>K<PERSON>banIcon from '@mui/icons-material/ViewKanban';
import ViewListIcon from '@mui/icons-material/ViewList';
import Box from '@mui/joy/Box';
import Option from '@mui/joy/Option';
import Select from '@mui/joy/Select';
import Tab from '@mui/joy/Tab';
import TabList from '@mui/joy/TabList';
import TabPanel from '@mui/joy/TabPanel';
import Tabs from '@mui/joy/Tabs';
import { useState } from 'react';
import { KanbanBoard } from '../../../components/kanban/KanbanBoard';
import { type TaskDetailData, TaskDetailModal } from '../../../components/TaskDetailModal';
import { useTaskModal } from '../../../hooks/useTaskModal';
import { trpcClient } from '../../../trpc-client/react';
import { ProjectCodeWorkspace } from '../components/ProjectCodeWorkspace';
import { ProjectHeader } from '../components/ProjectHeader';
import { TaskCreateModal } from '../components/TaskCreateModal';
import { TaskListView } from '../components/TaskListView';

interface TaskItem {
  id: string;
  name: unknown;
  systemState?: string;
}

export function ProjectClient({ projectId }: { projectId: string }) {
  const tasksQuery = trpcClient.task.list.useQuery({ projectId });
  const utils = trpcClient.useUtils();
  const [openCreate, setOpenCreate] = useState(false);
  const createTask = trpcClient.task.new.useMutation({
    onSuccess: () => {
      utils.task.list.invalidate({ projectId });
      setOpenCreate(false);
    },
  });
  const runTask = trpcClient.task.run.useMutation({
    onSuccess: () => utils.task.list.invalidate({ projectId }),
  });
  const projectTasks: TaskItem[] = (tasksQuery.data as TaskItem[] | undefined) || [];
  const [tabValue, setTabValue] = useState(0);
  const [viewMode, setViewMode] = useState<'kanban' | 'list'>('kanban');

  const { activeTaskId, closeTask } = useTaskModal();
  const activeTask = projectTasks.find((t) => t.id === activeTaskId) as TaskDetailData | undefined;

  return (
    <Box component="main" sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      <ProjectHeader
        projectId={projectId}
        taskCount={projectTasks.length}
        openCreateAction={() => setOpenCreate(true)}
      />

      <Tabs value={tabValue} onChange={(_e, v) => typeof v === 'number' && setTabValue(v)}>
        <TabList sx={{ mb: 2, gap: 1 }}>
          <Tab
            variant={tabValue === 0 ? 'soft' : 'plain'}
            value={0}
            sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
          >
            任务
            <Select
              size="sm"
              variant="plain"
              value={viewMode}
              onChange={(_, v) => v && setViewMode(v as 'kanban' | 'list')}
              sx={{ '--Select-minHeight': 'unset', minWidth: 48, p: 0 }}
              slotProps={{
                listbox: { sx: { backgroundColor: 'var(--bg-popup)' } },
                button: {
                  sx: {
                    px: 0.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    '& span': { display: 'none' }, // hide default text
                  },
                },
              }}
              renderValue={() =>
                viewMode === 'kanban' ? (
                  <ViewKanbanIcon fontSize="small" sx={{ color: 'var(--text-disabled)' }} />
                ) : (
                  <ViewListIcon fontSize="small" sx={{ color: 'var(--text-disabled)' }} />
                )
              }
            >
              <Option value="kanban" sx={{ display: 'flex', gap: 0.75, alignItems: 'center' }}>
                <ViewKanbanIcon fontSize="small" /> 看板视图
              </Option>
              <Option value="list" sx={{ display: 'flex', gap: 0.75, alignItems: 'center' }}>
                <ViewListIcon fontSize="small" /> 列表视图
              </Option>
            </Select>
          </Tab>
          <Tab variant={tabValue === 1 ? 'soft' : 'plain'} value={1}>
            Files
          </Tab>
        </TabList>
        <TabPanel value={0} sx={{ p: 0 }}>
          {viewMode === 'kanban' && <KanbanBoard projectId={projectId} />}
          {viewMode === 'list' && (
            <TaskListView
              tasks={projectTasks}
              runTaskAction={(id) => runTask.mutate({ taskId: id })}
              loadingIds={
                runTask.isPending
                  ? projectTasks.filter((t) => t.systemState === 'INIT').map((t) => t.id)
                  : []
              }
            />
          )}
        </TabPanel>
        <TabPanel value={1} sx={{ p: 0 }}>
          <ProjectCodeWorkspace projectId={projectId} tasks={projectTasks as TaskItem[]} />
        </TabPanel>
      </Tabs>

      <TaskCreateModal
        open={openCreate}
        closeAction={() => setOpenCreate(false)}
        createAction={({ name, content }) => createTask.mutate({ projectId, name, content })}
        loading={createTask.isPending}
      />

      <TaskDetailModal task={activeTask} open={!!activeTask} onClose={closeTask} />
    </Box>
  );
}
