'use client';
import { Suspense } from 'react';
import { BrowserRouter, Link, Navigate, Route, Routes, useParams } from 'react-router-dom';
import type { TaskData } from 'types/task';
import { trpcClient } from '../../../trpc-client/react';
import { HomeScreen } from '../screens/HomeScreen';
import { ProjectClient } from '../screens/ProjectScreen';

function RouterEntrypoint() {
  return (
    <BrowserRouter basename="/projects">
      <Suspense fallback={<div style={{ padding: 32 }}>Loading...</div>}>
        <AppRoutes />
      </Suspense>
    </BrowserRouter>
  );
}

function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<HomeScreen />} />
      <Route path="task/:taskId" element={<TaskRoute />} />
      <Route path=":projectId" element={<ProjectRoute />} />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

function ProjectRoute() {
  const { projectId } = useParams();
  if (!projectId) return null;
  return <ProjectClient projectId={projectId} />;
}

function TaskRoute() {
  const { taskId } = useParams();
  if (!taskId) return null;
  return <TaskClient taskId={taskId} />;
}

function TaskClient({ taskId }: { taskId: string }) {
  const taskQuery = trpcClient.task.get.useQuery({ taskId });
  const task = taskQuery.data as TaskData | undefined;
  if (!task) return <main style={{ fontFamily: 'sans-serif', padding: 24 }}>Loading...</main>;
  return (
    <main style={{ fontFamily: 'sans-serif', padding: 24 }}>
      <p>
        <Link to={`/${task.projectId}`}>← Back</Link>
      </p>
      <h1>{typeof task.name === 'string' ? task.name : JSON.stringify(task.name)}</h1>
      <p>
        <b>Content:</b> {task.content}
      </p>
      <p>
        <b>Outcome:</b> {task.outcome ? JSON.stringify(task.outcome) : '(none)'}
      </p>
      <p>
        <b>State:</b> {task.systemState}
      </p>
      <p>
        <b>Created:</b> {task.createdAt?.toString()}
      </p>
    </main>
  );
}

export default function CatchAllAppPage() {
  return <RouterEntrypoint />;
}
