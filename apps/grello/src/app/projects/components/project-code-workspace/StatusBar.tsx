import Box from '@mui/joy/Box';
import type { VirtualFile } from './types';

interface StatusBarProps {
  filesCount: number;
  activeVF: VirtualFile | null;
  isModified: (vf: VirtualFile) => boolean;
}

export function StatusBar({ filesCount, activeVF, isModified }: StatusBarProps) {
  return (
    <Box
      sx={{
        height: 26,
        display: 'flex',
        alignItems: 'center',
        px: 1,
        fontSize: 11,
        gap: 2,
        background: 'var(--joy-palette-neutral-800, #1a1a1a)',
      }}
    >
      <span>文件数: {filesCount}</span>
      {activeVF && <span>长度: {activeVF.content.length}</span>}
      {activeVF && isModified(activeVF) && (
        <span style={{ color: 'var(--joy-palette-warning-400)' }}>未保存修改</span>
      )}
    </Box>
  );
}
