import AddOutlined from '@mui/icons-material/AddOutlined';
import CancelOutlined from '@mui/icons-material/CancelOutlined';
import DeleteOutline from '@mui/icons-material/DeleteOutline';
import DescriptionOutlined from '@mui/icons-material/DescriptionOutlined';
import DoneOutlined from '@mui/icons-material/DoneOutlined';
import FolderOpenOutlined from '@mui/icons-material/FolderOpenOutlined';
import FolderOutlined from '@mui/icons-material/FolderOutlined';
import Box from '@mui/joy/Box';
import Chip from '@mui/joy/Chip';
import IconButton from '@mui/joy/IconButton';
import Input from '@mui/joy/Input';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import { useState } from 'react';
import type { FileTreeNode } from './types';

interface FileTreeProps {
  tree: FileTreeNode[];
  activeFile: string | null;
  isModified: (path: string) => boolean;
  creatingFileDir: string | null;
  newFileName: string;
  setNewFileName: (v: string) => void;
  setCreatingFileDir: (dir: string | null) => void;
  openFile: (path: string) => void;
  createFile: (dir: string, name: string) => void;
  deleteFile: (path: string) => void;
  files: Record<string, { content: string; original: string }>;
}

export function FileTree(props: FileTreeProps) {
  const {
    tree,
    activeFile,
    isModified,
    creatingFileDir,
    newFileName,
    setNewFileName,
    setCreatingFileDir,
    openFile,
    createFile,
    deleteFile,
    files,
  } = props;

  // local tick forces re-render when mutating folder open flag
  const [, setTick] = useState(0);

  const renderNodes = (nodes: FileTreeNode[], depth = 0) => (
    <Box>
      {nodes.map((n) => {
        if (n.type === 'dir') {
          return (
            <Box key={n.path} sx={{ pl: depth * 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  py: 0.25,
                  cursor: 'pointer',
                  '&:hover': {
                    background: 'var(--joy-palette-neutral-softBg, #1f1f1f)',
                    borderRadius: 4,
                  },
                }}
                onClick={() => {
                  n.open = !n.open;
                  setTick((t) => t + 1);
                }}
              >
                {n.open ? (
                  <FolderOpenOutlined fontSize="small" />
                ) : (
                  <FolderOutlined fontSize="small" />
                )}
                <Typography level="body-xs">{n.name || 'root'}</Typography>
                <Tooltip title="新建文件">
                  <IconButton
                    size="sm"
                    variant="plain"
                    onClick={(e) => {
                      e.stopPropagation();
                      setCreatingFileDir(n.path);
                      setNewFileName('new-file.md');
                    }}
                  >
                    <AddOutlined fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              {creatingFileDir === n.path && (
                <Box sx={{ pl: 2, py: 0.5, display: 'flex', gap: 0.5 }}>
                  <Input
                    size="sm"
                    value={newFileName}
                    onChange={(e) => setNewFileName(e.target.value)}
                    sx={{ flex: 1 }}
                  />
                  <IconButton
                    size="sm"
                    variant="soft"
                    onClick={() => createFile(n.path, newFileName)}
                  >
                    <DoneOutlined />
                  </IconButton>
                  <IconButton size="sm" variant="plain" onClick={() => setCreatingFileDir(null)}>
                    <CancelOutlined />
                  </IconButton>
                </Box>
              )}
              {n.open && <Box>{renderNodes(n.children, depth + 1)}</Box>}
            </Box>
          );
        }
        return (
          <Box
            key={n.path}
            sx={{
              pl: depth * 1 + 2,
              py: 0.25,
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              cursor: 'pointer',
              fontFamily: 'monospace',
              background:
                activeFile === n.path ? 'var(--joy-palette-neutral-softBg, #1f1f1f)' : undefined,
              borderRadius: 4,
              '&:hover': { background: 'var(--joy-palette-neutral-softHoverBg, #262626)' },
            }}
            onClick={() => openFile(n.path)}
          >
            <DescriptionOutlined fontSize="small" />
            <Typography level="body-xs" sx={{ flex: 1, whiteSpace: 'nowrap' }}>
              {n.name}
            </Typography>
            {files[n.path] && isModified(n.path) && (
              <Chip size="sm" variant="soft" color="warning">
                *
              </Chip>
            )}
            <Tooltip title="删除">
              <IconButton
                size="sm"
                variant="plain"
                onClick={(e) => {
                  e.stopPropagation();
                  deleteFile(n.path);
                }}
              >
                <DeleteOutline fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        );
      })}
    </Box>
  );

  return <Box sx={{ pr: 1 }}>{renderNodes(tree)}</Box>;
}
