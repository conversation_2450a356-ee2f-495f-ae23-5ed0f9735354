import AddOutlined from '@mui/icons-material/AddOutlined';
import CancelOutlined from '@mui/icons-material/CancelOutlined';
import DoneOutlined from '@mui/icons-material/DoneOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import IconButton from '@mui/joy/IconButton';
import Input from '@mui/joy/Input';
import type { RefObject } from 'react';

interface SidebarHeaderProps {
  activeExists: boolean;
  editorRef: RefObject<HTMLTextAreaElement>;
  creatingRoot: boolean;
  newFileName: string;
  setNewFileName: (v: string) => void;
  setCreatingFileDir: (dir: string | null) => void;
  createFile: (dir: string, name: string) => void;
}

export function SidebarHeader({
  activeExists,
  editorRef,
  creatingRoot,
  newFileName,
  setNewFileName,
  setCreatingFileDir,
  createFile,
}: SidebarHeaderProps) {
  return (
    <>
      <Box sx={{ display: 'flex', gap: 0.5, mb: 1 }}>
        <Button
          size="sm"
          variant="soft"
          startDecorator={<AddOutlined />}
          onClick={() => {
            setCreatingFileDir('');
            setNewFileName('new-file.md');
          }}
        >
          新文件
        </Button>
        <Button
          size="sm"
          variant="plain"
          startDecorator={<EditOutlined />}
          disabled={!activeExists}
          onClick={() => editorRef.current?.focus()}
        >
          编辑
        </Button>
      </Box>
      {creatingRoot && (
        <Box sx={{ display: 'flex', gap: 0.5, mb: 1 }}>
          <Input size="sm" value={newFileName} onChange={(e) => setNewFileName(e.target.value)} />
          <IconButton
            size="sm"
            variant="soft"
            onClick={() => {
              createFile('', newFileName);
              setCreatingFileDir(null);
            }}
          >
            <DoneOutlined />
          </IconButton>
          <IconButton size="sm" variant="plain" onClick={() => setCreatingFileDir(null)}>
            <CancelOutlined />
          </IconButton>
        </Box>
      )}
    </>
  );
}
