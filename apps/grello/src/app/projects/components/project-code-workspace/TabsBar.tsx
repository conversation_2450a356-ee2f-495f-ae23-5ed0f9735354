import CloseRounded from '@mui/icons-material/CloseRounded';
import DescriptionOutlined from '@mui/icons-material/DescriptionOutlined';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';
import type { VirtualFile } from './types';

interface TabsBarProps {
  files: Record<string, VirtualFile>;
  openFiles: string[];
  activeFile: string | null;
  isModified: (vf: VirtualFile) => boolean;
  setActiveFile: (p: string) => void;
  closeFile: (p: string) => void;
}

export function TabsBar({
  files,
  openFiles,
  activeFile,
  isModified,
  setActiveFile,
  closeFile,
}: TabsBarProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        borderBottom: '1px solid var(--joy-palette-neutral-outlinedBorder)',
        overflowX: 'auto',
      }}
    >
      {openFiles.length === 0 && (
        <Box sx={{ px: 1, py: 0.5, fontSize: 12, color: 'var(--text-disabled)' }}>未打开文件</Box>
      )}
      {openFiles.map((p) => {
        const f = files[p];
        if (!f) return null;
        const baseName = p.split('/').pop() || p;
        return (
          <Box
            key={p}
            onClick={() => setActiveFile(p)}
            sx={{
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              px: 1,
              py: 0.5,
              fontSize: 12,
              borderRight: '1px solid var(--joy-palette-neutral-outlinedBorder)',
              background: activeFile === p ? 'var(--joy-palette-neutral-softBg)' : undefined,
              '&:hover': { background: 'var(--joy-palette-neutral-softHoverBg)' },
            }}
          >
            <DescriptionOutlined fontSize="inherit" />
            <span style={{ whiteSpace: 'nowrap' }}>
              {baseName}
              {isModified(f) && '*'}
            </span>
            <IconButton
              size="sm"
              variant="plain"
              onClick={(e) => {
                e.stopPropagation();
                closeFile(p);
              }}
              sx={{ ml: 0.5 }}
            >
              <CloseRounded fontSize="inherit" />
            </IconButton>
          </Box>
        );
      })}
    </Box>
  );
}
