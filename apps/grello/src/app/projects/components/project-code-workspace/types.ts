export interface TaskLike {
  id: string;
  name?: unknown;
  content?: string | null;
}

export interface VirtualFile {
  path: string; // full path
  content: string;
  original: string; // last saved/origin content
}

export interface FileNodeDir {
  type: 'dir';
  name: string;
  path: string; // directory path without trailing slash
  children: FileTreeNode[];
  open: boolean;
}
export interface FileNodeFile {
  type: 'file';
  name: string;
  path: string; // complete file path
}
export type FileTreeNode = FileNodeDir | FileNodeFile;

export interface WorkspaceState {
  files: Record<string, VirtualFile>;
  openFiles: string[];
  activeFile: string | null;
  showUnsavedOnly: boolean;
  creatingFileDir: string | null;
  newFileName: string;
  taskVersion: number;
}
