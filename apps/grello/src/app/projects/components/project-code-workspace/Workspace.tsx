import RefreshOutlined from '@mui/icons-material/RefreshOutlined';
import WarningAmberOutlined from '@mui/icons-material/WarningAmberOutlined';
import Box from '@mui/joy/Box';
import Checkbox from '@mui/joy/Checkbox';
import Chip from '@mui/joy/Chip';
import IconButton from '@mui/joy/IconButton';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import { useRef } from 'react';
import { buildTree } from './buildTree';
import { EditorArea } from './EditorArea';
import { EditorToolbar } from './EditorToolbar';
import { FileTree } from './FileTree';
import { SidebarHeader } from './SidebarHeader';
import { StatusBar } from './StatusBar';
import { TabsBar } from './TabsBar';
import type { TaskLike } from './types';
import { useWorkspaceState } from './useWorkspaceState';

export interface WorkspaceProps {
  tasks: TaskLike[];
  projectId: string;
}

export function Workspace({ tasks }: WorkspaceProps) {
  const { state, actions, derived } = useWorkspaceState({ tasks });
  const {
    files,
    openFiles,
    activeFile,
    creatingFileDir,
    newFileName,
    showUnsavedOnly,
    taskVersion,
  } = state;
  const { isModified, activeVF, filteredOpenFiles } = derived;
  const editorRef = useRef<HTMLTextAreaElement | null>(null);
  const tree = buildTree(Object.values(files));

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, height: 'calc(100vh - 240px)' }}>
      <Stack direction="row" spacing={1} sx={{ alignItems: 'center' }}>
        <Typography level="title-sm">代码工作区 (Mock VSCode)</Typography>
        <Chip size="sm" variant="soft">
          内存态
        </Chip>
        <Tooltip title="刷新任务生成文件 (不覆盖已编辑内容的当前状态，但会更新 original)">
          <IconButton size="sm" variant="outlined" onClick={actions.refreshFromTasks}>
            <RefreshOutlined fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="显示仅未保存">
          <Checkbox
            size="sm"
            label="未保存"
            checked={showUnsavedOnly}
            onChange={(e) => actions.setShowUnsavedOnly(e.target.checked)}
            variant="outlined"
          />
        </Tooltip>
        <Chip size="sm" variant="plain" startDecorator={<WarningAmberOutlined fontSize="small" />}>
          {taskVersion}
        </Chip>
      </Stack>
      <Sheet
        sx={{ flex: 1, minHeight: 300, display: 'flex', overflow: 'hidden' }}
        variant="outlined"
      >
        <Box
          sx={{
            width: 240,
            borderRight: '1px solid var(--joy-palette-neutral-outlinedBorder)',
            p: 1,
            overflow: 'auto',
          }}
        >
          <SidebarHeader
            activeExists={!!activeVF}
            editorRef={editorRef}
            creatingRoot={creatingFileDir === ''}
            newFileName={newFileName}
            setNewFileName={actions.setNewFileName}
            setCreatingFileDir={actions.setCreatingFileDir}
            createFile={actions.createFile}
          />
          <Box sx={{ fontSize: 11, fontWeight: 600, mb: 0.5 }}>文件</Box>
          <FileTree
            tree={tree}
            activeFile={activeFile}
            isModified={(p) => !!files[p] && isModified(files[p])}
            creatingFileDir={creatingFileDir}
            newFileName={newFileName}
            setNewFileName={actions.setNewFileName}
            setCreatingFileDir={actions.setCreatingFileDir}
            openFile={actions.openFile}
            createFile={actions.createFile}
            deleteFile={actions.deleteFile}
            files={files}
          />
        </Box>
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', minWidth: 0 }}>
          <TabsBar
            files={files}
            openFiles={filteredOpenFiles}
            activeFile={activeFile}
            isModified={isModified}
            setActiveFile={actions.focusFile}
            closeFile={actions.closeFile}
          />
          <EditorToolbar
            activeVF={activeVF}
            isModified={isModified}
            saveFile={actions.saveFile}
            revertFile={actions.revertFile}
          />
          <EditorArea
            activeVF={activeVF}
            updateFileContent={actions.updateFileContent}
            editorRef={editorRef}
          />
          <StatusBar
            filesCount={Object.keys(files).length}
            activeVF={activeVF}
            isModified={isModified}
          />
        </Box>
      </Sheet>
    </Box>
  );
}
