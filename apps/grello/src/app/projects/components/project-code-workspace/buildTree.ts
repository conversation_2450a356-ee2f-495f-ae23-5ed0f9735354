import type { FileNodeDir, FileNodeFile, FileTreeNode, VirtualFile } from './types';

export function buildTree(files: VirtualFile[]): FileTreeNode[] {
  const tree: FileTreeNode[] = [];
  const dirMap: Record<string, FileNodeDir> = {};

  const getOrCreateDir = (path: string) => {
    if (path === '') return undefined;
    if (dirMap[path]) return dirMap[path];
    const parentPath = path.split('/').slice(0, -1).join('/');
    const dir: FileNodeDir = {
      type: 'dir',
      name: path.split('/').pop() || '',
      path,
      children: [],
      open: true,
    };
    dirMap[path] = dir;
    if (parentPath) {
      const parent = getOrCreateDir(parentPath);
      parent?.children.push(dir);
    } else {
      tree.push(dir);
    }
    return dir;
  };

  files.forEach((f) => {
    const segments = f.path.split('/');
    const fileName = segments.pop() as string;
    const dirPath = segments.join('/');
    const parentDir = getOrCreateDir(dirPath);
    const fileNode: FileNodeFile = { type: 'file', name: fileName, path: f.path };
    if (parentDir) parentDir.children.push(fileNode);
    else tree.push(fileNode);
  });

  const sortDir = (nodes: FileTreeNode[]) => {
    nodes.sort((a, b) => {
      if (a.type !== b.type) return a.type === 'dir' ? -1 : 1;
      return a.name.localeCompare(b.name);
    });
    nodes.forEach((n) => {
      if (n.type === 'dir') sortDir(n.children);
    });
  };
  sortDir(tree);
  return tree;
}
