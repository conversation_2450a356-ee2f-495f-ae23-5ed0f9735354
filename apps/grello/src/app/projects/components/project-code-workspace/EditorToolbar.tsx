import ReplayOutlined from '@mui/icons-material/ReplayOutlined';
import SaveOutlined from '@mui/icons-material/SaveOutlined';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import type { VirtualFile } from './types';

interface EditorToolbarProps {
  activeVF: VirtualFile | null;
  isModified: (vf: VirtualFile) => boolean;
  saveFile: (p: string) => void;
  revertFile: (p: string) => void;
}

export function EditorToolbar({ activeVF, isModified, saveFile, revertFile }: EditorToolbarProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 0.5,
        alignItems: 'center',
        p: 0.5,
        borderBottom: '1px solid var(--joy-palette-neutral-outlinedBorder)',
      }}
    >
      <Typography level="body-xs" sx={{ flex: 1 }}>
        {activeVF ? activeVF.path : '无文件'}
      </Typography>
      <Tooltip title="保存 (更新 original)">
        <span>
          <IconButton
            size="sm"
            disabled={!activeVF || !isModified(activeVF)}
            onClick={() => activeVF && saveFile(activeVF.path)}
          >
            <SaveOutlined fontSize="small" />
          </IconButton>
        </span>
      </Tooltip>
      <Tooltip title="还原到任务/最近保存">
        <span>
          <IconButton
            size="sm"
            disabled={!activeVF || !isModified(activeVF)}
            onClick={() => activeVF && revertFile(activeVF.path)}
          >
            <ReplayOutlined fontSize="small" />
          </IconButton>
        </span>
      </Tooltip>
    </Box>
  );
}
