import { useEffect, useMemo, useRef, useState } from 'react';
import type { TaskLike, VirtualFile, WorkspaceState } from './types';

export interface UseWorkspaceOptions {
  tasks: TaskLike[];
}

export function useWorkspaceState({ tasks }: UseWorkspaceOptions) {
  const taskFiles = useMemo<VirtualFile[]>(() => {
    return tasks.map((t) => ({
      path: `tasks/task-${t.id}.md`,
      content: (t.content || '').trim() || `# ${typeof t.name === 'string' ? t.name : 'Task'}\n`,
      original: (t.content || '').trim() || `# ${typeof t.name === 'string' ? t.name : 'Task'}\n`,
    }));
  }, [tasks]);

  const [state, setState] = useState<WorkspaceState>(() => {
    const map: Record<string, VirtualFile> = {};
    taskFiles.forEach((f) => {
      map[f.path] = f;
    });
    return {
      files: map,
      openFiles: taskFiles[0] ? [taskFiles[0].path] : [],
      activeFile: taskFiles[0] ? taskFiles[0].path : null,
      creatingFileDir: null,
      newFileName: '',
      showUnsavedOnly: false,
      taskVersion: 0,
    };
  });

  const lastTasksRef = useRef<TaskLike[] | null>(null);
  useEffect(() => {
    if (lastTasksRef.current !== tasks) {
      setState((s) => ({ ...s, taskVersion: s.taskVersion + 1 }));
      lastTasksRef.current = tasks;
    }
  }, [tasks]);

  const isModified = (f: VirtualFile) => f.content !== f.original;

  const actions = {
    openFile(path: string) {
      setState((s) => ({
        ...s,
        openFiles: s.openFiles.includes(path) ? s.openFiles : [...s.openFiles, path],
        activeFile: path,
      }));
    },
    closeFile(path: string) {
      setState((s) => {
        const openFiles = s.openFiles.filter((p) => p !== path);
        return {
          ...s,
          openFiles,
          activeFile: s.activeFile === path ? openFiles[0] || null : s.activeFile,
        };
      });
    },
    updateFileContent(path: string, content: string) {
      setState((s) => ({
        ...s,
        files: { ...s.files, [path]: { ...s.files[path], content } },
      }));
    },
    revertFile(path: string) {
      setState((s) => ({
        ...s,
        files: { ...s.files, [path]: { ...s.files[path], content: s.files[path].original } },
      }));
    },
    saveFile(path: string) {
      setState((s) => ({
        ...s,
        files: { ...s.files, [path]: { ...s.files[path], original: s.files[path].content } },
      }));
    },
    createFile(dirPath: string, name: string) {
      if (!name) return;
      const full = dirPath ? `${dirPath}/${name}` : name;
      setState((s) => {
        if (s.files[full]) return s; // exists
        const vf: VirtualFile = { path: full, content: '', original: '' };
        return {
          ...s,
          files: { ...s.files, [full]: vf },
          openFiles: [...s.openFiles, full],
          activeFile: full,
          creatingFileDir: null,
          newFileName: '',
        };
      });
    },
    deleteFile(path: string) {
      setState((s) => {
        const { [path]: _removed, ...rest } = s.files;
        const openFiles = s.openFiles.filter((p) => p !== path);
        return {
          ...s,
          files: rest,
          openFiles,
          activeFile: s.activeFile === path ? openFiles[0] || null : s.activeFile,
        };
      });
    },
    refreshFromTasks() {
      setState((s) => {
        const map: Record<string, VirtualFile> = {};
        taskFiles.forEach((f) => {
          map[f.path] = s.files[f.path] ? { ...s.files[f.path], original: f.original } : f;
        });
        return { ...s, files: map };
      });
    },
    setCreatingFileDir(dir: string | null) {
      setState((s) => ({ ...s, creatingFileDir: dir }));
    },
    setNewFileName(name: string) {
      setState((s) => ({ ...s, newFileName: name }));
    },
    setShowUnsavedOnly(v: boolean) {
      setState((s) => ({ ...s, showUnsavedOnly: v }));
    },
    focusFile(path: string) {
      setState((s) => ({ ...s, activeFile: path }));
    },
  } as const;

  const derived = {
    taskFiles,
    isModified,
    activeVF: state.activeFile ? state.files[state.activeFile] : null,
    filteredOpenFiles: state.showUnsavedOnly
      ? state.openFiles.filter((p) => state.files[p] && isModified(state.files[p]))
      : state.openFiles,
  } as const;

  return { state, actions, derived };
}
