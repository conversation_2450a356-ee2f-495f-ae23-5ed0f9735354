import Box from '@mui/joy/Box';
import type { RefObject } from 'react';
import type { VirtualFile } from './types';

interface EditorAreaProps {
  activeVF: VirtualFile | null;
  updateFileContent: (p: string, c: string) => void;
  editorRef: RefObject<HTMLTextAreaElement>;
}

export function EditorArea({ activeVF, updateFileContent, editorRef }: EditorAreaProps) {
  return (
    <Box sx={{ flex: 1, position: 'relative', display: 'flex' }}>
      {activeVF ? (
        <textarea
          ref={editorRef}
          value={activeVF.content}
          onChange={(e) => updateFileContent(activeVF.path, e.target.value)}
          style={{
            width: '100%',
            height: '100%',
            resize: 'none',
            border: 'none',
            outline: 'none',
            padding: '12px',
            fontFamily: 'Menlo, monospace',
            fontSize: 13,
            background: 'var(--joy-palette-neutral-900, #111)',
            color: 'var(--joy-palette-neutral-50, #eee)',
            lineHeight: '1.5',
            tabSize: 2,
            whiteSpace: 'pre',
          }}
        />
      ) : (
        <Box sx={{ m: 'auto', color: 'var(--text-disabled)', fontSize: 14 }}>
          选择或新建文件开始编辑
        </Box>
      )}
    </Box>
  );
}
