'use client';
import Button from '@mui/joy/Button';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import { StatusChip } from '../../../components/StatusChip';
import { useTaskModal } from '../../../hooks/useTaskModal';

export interface TaskListItemData {
  id: string;
  name: unknown;
  systemState?: string;
}

interface TaskListViewProps {
  tasks: TaskListItemData[];
  runTaskAction: (taskId: string) => void;
  loadingIds?: string[];
}

export function TaskListView({ tasks, runTaskAction, loadingIds }: TaskListViewProps) {
  const { openTask } = useTaskModal();

  return (
    <Sheet variant="outlined" sx={{ p: 1.5, borderRadius: 'md', background: 'background.level2' }}>
      <Stack component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }} gap={0.75}>
        {tasks.map((t) => (
          <Stack
            key={t.id}
            component="li"
            direction="row"
            alignItems="center"
            spacing={1}
            sx={{
              fontSize: 13,
              lineHeight: 1.3,
              py: 0.75,
              px: 1,
              borderRadius: 'sm',
              bgcolor: 'background.level1',
              border: '1px solid',
              borderColor: 'divider',
              '&:hover': { borderColor: 'primary.outlinedBorder', cursor: 'pointer' },
            }}
            onClick={() => openTask(t.id)}
          >
            <Typography level="body-xs" sx={{ flex: 1, color: 'text.primary', fontWeight: 500 }}>
              {typeof t.name === 'string' ? t.name : JSON.stringify(t.name)}
            </Typography>
            <StatusChip status={t.systemState} />
            {t.systemState === 'INIT' && (
              <Button
                variant="outlined"
                size="sm"
                color="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  runTaskAction(t.id);
                }}
                loading={loadingIds?.includes(t.id)}
              >
                Run
              </Button>
            )}
          </Stack>
        ))}
        {tasks.length === 0 && (
          <Typography level="body-xs" sx={{ color: 'text.tertiary', p: 1 }}>
            暂无任务
          </Typography>
        )}
      </Stack>
    </Sheet>
  );
}
