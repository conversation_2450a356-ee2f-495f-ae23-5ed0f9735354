'use client';
import Button from '@mui/joy/Button';
import Chip from '@mui/joy/Chip';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import { Link } from 'react-router-dom';
import { ThemeSwitcher } from '../../../components/ThemeSwitcher';

interface ProjectHeaderProps {
  projectId: string;
  taskCount: number;
  openCreateAction: () => void;
}

export function ProjectHeader({ projectId, taskCount, openCreateAction }: ProjectHeaderProps) {
  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2.5} spacing={2}>
      <Stack direction="row" spacing={1} alignItems="center">
        <Button component={Link} to="/" variant="outlined" size="sm" color="neutral">
          ← 返回
        </Button>
        <Typography level="h3" sx={{ fontWeight: 600 }}>
          项目 {projectId}
        </Typography>
        <Chip size="sm" variant="soft" color="primary" sx={{ fontWeight: 600 }}>
          {taskCount} 任务
        </Chip>
      </Stack>
      <Stack direction="row" spacing={1} alignItems="center">
        <Button size="sm" variant="solid" color="primary" onClick={openCreateAction}>
          新建任务
        </Button>
        <ThemeSwitcher />
      </Stack>
    </Stack>
  );
}
