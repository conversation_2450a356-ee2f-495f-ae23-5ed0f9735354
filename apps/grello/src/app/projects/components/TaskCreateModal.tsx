'use client';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import DialogActions from '@mui/joy/DialogActions';
import DialogContent from '@mui/joy/DialogContent';
import DialogTitle from '@mui/joy/DialogTitle';
import Input from '@mui/joy/Input';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import Stack from '@mui/joy/Stack';
import Textarea from '@mui/joy/Textarea';

interface TaskCreateModalProps {
  open: boolean;
  closeAction: () => void;
  createAction: (payload: { name: string; content: string }) => void;
  loading?: boolean;
}

export function TaskCreateModal({
  open,
  closeAction,
  createAction,
  loading,
}: TaskCreateModalProps) {
  return (
    <Modal open={open} onClose={closeAction}>
      <ModalDialog variant="outlined" sx={{ width: 520, maxWidth: '100%' }}>
        <DialogTitle>新建任务</DialogTitle>
        <DialogContent>填写任务标题与描述。</DialogContent>
        <Box
          component="form"
          onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
            e.preventDefault();
            const fd = new FormData(e.currentTarget);
            const name = (fd.get('name') as string)?.trim();
            const content = (fd.get('content') as string)?.trim();
            if (!name || !content) return;
            createAction({ name, content });
            e.currentTarget.reset();
          }}
        >
          <Stack spacing={1.25} sx={{ my: 1 }}>
            <Input name="name" placeholder="任务标题" size="sm" autoFocus />
            <Textarea name="content" placeholder="任务描述" minRows={3} size="sm" />
          </Stack>
          <DialogActions sx={{ pt: 1 }}>
            <Button
              variant="plain"
              color="neutral"
              size="sm"
              onClick={closeAction}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" size="sm" color="primary" loading={loading}>
              创建
            </Button>
          </DialogActions>
        </Box>
      </ModalDialog>
    </Modal>
  );
}
