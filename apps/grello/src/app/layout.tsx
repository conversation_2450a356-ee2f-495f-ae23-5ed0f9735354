import type React from 'react';
import '../globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import GrelloProviders from './GrelloProviders';

const inter = Inter({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: 'Grello',
  description: 'Plan by Human, Execute by AI.',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh-CN" suppressHydrationWarning className={inter.variable}>
      <body className={inter.className}>
        <GrelloProviders>{children}</GrelloProviders>
      </body>
    </html>
  );
}
