'use client';
import CssBaseline from '@mui/joy/CssBaseline';
import { CssVarsProvider } from '@mui/joy/styles';
import React from 'react';
import { ThemeControllerProvider } from '../hooks/useThemeController';
import classicTheme from '../theme/classicTheme';
import grelloTheme from '../theme/grelloTheme';
import { TRPCReactProvider } from '../trpc-client/provider';

const themesMap = { grello: grelloTheme, classic: classicTheme } as const;

function InnerProviders({ children }: { children: React.ReactNode }) {
  // Intentionally simple wrapper for potential future cross-cutting UI
  return <>{children}</>;
}

export default function GrelloProviders({ children }: { children: React.ReactNode }) {
  const [themeName, setThemeName] = React.useState<keyof typeof themesMap>(() => {
    if (typeof window === 'undefined') return 'grello';
    const stored = window.localStorage.getItem('grello-theme-name');
    return (stored && stored in themesMap ? stored : 'grello') as keyof typeof themesMap;
  });
  React.useEffect(() => {
    try {
      window.localStorage.setItem('grello-theme-name', themeName);
    } catch {}
  }, [themeName]);
  return (
    <CssVarsProvider
      defaultMode="dark"
      modeStorageKey="grello-color-mode"
      theme={themesMap[themeName]}
      disableTransitionOnChange
    >
      <CssBaseline />
      <ThemeControllerProvider
        availableThemes={Object.keys(themesMap)}
        themeState={[themeName, (n: string) => setThemeName(n as keyof typeof themesMap)]}
      >
        <InnerProviders>
          <TRPCReactProvider>{children}</TRPCReactProvider>
        </InnerProviders>
      </ThemeControllerProvider>
    </CssVarsProvider>
  );
}
