'use client';
import { extendTheme } from '@mui/joy/styles';

// Grello custom Joy theme derived from VI spec (black + neon green)
const grelloTheme = extendTheme({
  fontFamily: {
    display:
      'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans CJK SC", sans-serif',
    body: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans CJK SC", sans-serif',
    code: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", monospace',
  },
  radius: { sm: '8px', md: '12px', lg: '16px' },
  shadow: {
    xs: '0 1px 2px rgba(0,0,0,0.25)',
    sm: '0 2px 4px rgba(0,0,0,0.28)',
    md: '0 4px 16px rgba(0,0,0,0.35)',
  },
  colorSchemes: {
    dark: {
      palette: {
        primary: {
          50: '#e7ffe8',
          100: '#c2ffc6',
          200: '#90ffa0',
          300: '#6bff8a', // hover
          400: '#4aff4f',
          500: '#39FF14', // base neon
          600: '#00FF66', // active / deeper energy
          700: '#00d957',
          800: '#00a643',
          900: '#007532',
          solidBg: '#39FF14',
          solidHoverBg: '#6BFF8A',
          solidActiveBg: '#00FF66',
          solidDisabledBg: '#2E3744',
          solidColor: '#0B0E11',
          outlinedColor: '#39FF14',
          outlinedBorder: '#39FF14',
          outlinedHoverBg: 'rgba(57,255,20,0.08)',
          outlinedActiveBg: 'rgba(57,255,20,0.16)',
          plainColor: '#39FF14',
          softBg: 'rgba(57,255,20,0.12)',
          softHoverBg: 'rgba(57,255,20,0.18)',
          softActiveBg: 'rgba(57,255,20,0.26)',
        },
        neutral: {
          50: '#12161C',
          100: '#1A2028',
          200: '#232A34',
          300: '#2E3744',
          400: '#586270',
          500: '#8A94A6',
          600: '#B3BDCC',
          700: '#D0D7E0',
          800: '#E5EAF0',
          900: '#EEF2F7',
          plainColor: '#EEF2F7',
        },
        success: { solidBg: '#22C55E', solidColor: '#0B0E11' },
        warning: { solidBg: '#FFCC00', solidColor: '#0B0E11' },
        danger: { solidBg: '#FF3B30', solidColor: '#FFFFFF' },
        background: {
          body: '#0B0E11',
          surface: '#12161C',
          level1: '#1A2028',
          level2: '#232A34',
          level3: '#2E3744',
          tooltip: '#232A34',
          backdrop: 'rgba(0,0,0,0.55)',
        },
        text: {
          primary: '#FFFFFF',
          secondary: '#EEF2F7',
          tertiary: '#B3BDCC',
        },
        divider: '#2E3744',
        focusVisible: '#6BFF8A',
      },
    },
    light: {
      palette: {
        primary: {
          50: '#e7ffe8',
          100: '#c2ffc6',
          200: '#90ffa0',
          300: '#6bff8a',
          400: '#4aff4f',
          500: '#39FF14',
          600: '#00FF66',
          700: '#00d957',
          800: '#00a643',
          900: '#007532',
          solidBg: '#39FF14',
          solidHoverBg: '#6BFF8A',
          solidActiveBg: '#00FF66',
          solidDisabledBg: '#e5e5e5',
          solidColor: '#0B0E11',
          outlinedColor: '#007532',
          outlinedBorder: '#39FF14',
          plainColor: '#007532',
        },
        neutral: {
          50: '#FFFFFF',
          100: '#F5F6F8',
          200: '#E8EBEF',
          300: '#DDE1E6',
          400: '#C2C9D1',
          500: '#8A94A6',
          600: '#586270',
          700: '#2E3744',
          800: '#232A34',
          900: '#0B0E11',
        },
        background: {
          body: '#FFFFFF',
          surface: '#FFFFFF',
          level1: '#F5F6F8',
          level2: '#E8EBEF',
          level3: '#DDE1E6',
          tooltip: '#232A34',
          backdrop: 'rgba(0,0,0,0.45)',
        },
        text: {
          primary: '#0B0E11',
          secondary: '#232A34',
          tertiary: '#586270',
        },
        divider: '#DDE1E6',
        focusVisible: '#39FF14',
      },
    },
  },
  components: {
    JoyButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          fontWeight: 600,
          textTransform: 'none',
          letterSpacing: '0.2px',
          '&:focus-visible': {
            outline: '2px solid #FFFFFF',
            boxShadow: `0 0 0 4px ${theme.vars.palette.primary.solidBg}`,
          },
        }),
      },
    },
    JoyCard: {
      styleOverrides: {
        root: ({ theme }) => ({
          transition: 'box-shadow 150ms, background 150ms, border-color 150ms',
          background: theme.vars.palette.background.surface,
          '&:hover': {
            boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
          },
        }),
      },
    },
    JoyModalDialog: {
      styleOverrides: {
        root: ({ theme }) => ({
          background: theme.vars.palette.background.surface,
          border: '1px solid var(--joy-palette-divider)',
          boxShadow: '0 8px 32px rgba(0,0,0,0.45)',
        }),
      },
    },
  },
});

export default grelloTheme;
