'use client';
import { extendTheme } from '@mui/joy/styles';

// A lighter classic theme variant (placeholder) to demonstrate theme switching.
const classicTheme = extendTheme({
  colorSchemes: {
    light: {
      palette: {
        primary: {
          500: '#0B5FFF',
          solidBg: '#0B5FFF',
          solidHoverBg: '#3277ff',
          solidActiveBg: '#0052e0',
          solidColor: '#fff',
        },
        background: {
          body: '#F5F6F8',
          surface: '#FFFFFF',
          level1: '#FFFFFF',
          level2: '#F1F3F5',
          level3: '#E5E8EC',
        },
        text: { primary: '#1D2129', secondary: '#4A4F57' },
        divider: '#DDE1E6',
      },
    },
    dark: {
      palette: {
        primary: {
          500: '#5FA3FF',
          solidBg: '#5FA3FF',
          solidHoverBg: '#84b8ff',
          solidActiveBg: '#2d7ddd',
          solidColor: '#0B0E11',
        },
        background: {
          body: '#12161C',
          surface: '#1A2028',
          level1: '#232A34',
          level2: '#2E3744',
          level3: '#394352',
        },
        text: { primary: '#FFFFFF', secondary: '#D0D7E0' },
        divider: '#2E3744',
      },
    },
  },
  fontFamily: {
    body: 'Inter, system-ui, sans-serif',
  },
});

export default classicTheme;
