'use client';
import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export function useTaskModal() {
  const location = useLocation();
  const navigate = useNavigate();

  const openTask = useCallback(
    (taskId: string, { replace }: { replace?: boolean } = {}) => {
      const searchParams = new URLSearchParams(location.search);
      searchParams.set('task', taskId);
      navigate(
        { pathname: location.pathname, search: `?${searchParams.toString()}` },
        { replace: !!replace },
      );
    },
    [location, navigate],
  );

  const closeTask = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
    if (!searchParams.has('task')) return;
    searchParams.delete('task');
    navigate(
      {
        pathname: location.pathname,
        search: searchParams.size ? `?${searchParams.toString()}` : '',
      },
      { replace: false },
    );
  }, [location, navigate]);

  const activeTaskId = new URLSearchParams(location.search).get('task') || undefined;

  return { openTask, closeTask, activeTaskId };
}
