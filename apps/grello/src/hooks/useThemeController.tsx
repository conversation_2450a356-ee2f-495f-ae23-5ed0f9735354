'use client';
import { useColorScheme } from '@mui/joy/styles';
import React from 'react';

interface ThemeContextValue {
  themeName: string;
  setThemeName: (n: string) => void;
  availableThemes: string[];
  mode: 'light' | 'dark';
  toggleMode: () => void;
}

const ThemeContext = React.createContext<ThemeContextValue | null>(null);

export function ThemeControllerProvider({
  children,
  availableThemes,
  themeState,
}: {
  children: React.ReactNode;
  availableThemes: string[];
  themeState: [string, (n: string) => void];
}) {
  const [themeName, setThemeName] = themeState;
  const { mode, setMode } = useColorScheme();
  // Persist explicit color mode (Joy already stores internally, but we mirror for cross-app or future SSR rehydration needs)
  React.useEffect(() => {
    try {
      if (mode) window.localStorage.setItem('grello-color-mode-explicit', mode);
    } catch {}
  }, [mode]);
  const value = React.useMemo<ThemeContextValue>(
    () => ({
      themeName,
      setThemeName,
      availableThemes,
      mode: (mode as 'light' | 'dark') || 'dark',
      toggleMode: () => setMode(mode === 'dark' ? 'light' : 'dark'),
    }),
    [themeName, setThemeName, availableThemes, mode, setMode],
  );
  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

export function useThemeController() {
  const ctx = React.useContext(ThemeContext);
  if (!ctx) throw new Error('useThemeController must be used within ThemeControllerProvider');
  return ctx;
}
