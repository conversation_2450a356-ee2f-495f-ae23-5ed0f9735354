import type { iString } from 'basenext/i18n';

export interface TaskCardVO {
  id: string;
  name: iString;
  content?: string | null;
  outcome?: unknown | null; // 执行结果（JSON）
  systemState: string;
  labels?: { id: string; name: string; color?: string }[]; // optional future labels
  members?: { id: string; name?: string; avatarUrl?: string }[]; // optional future members
}

export interface TaskData {
  id: string;
  name: unknown;
  projectId: string;
  content?: string;
  outcome?: unknown | null;
  systemState?: string;
  createdAt?: Date | string;
}
