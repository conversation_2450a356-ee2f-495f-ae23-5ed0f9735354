'use client';
import type { DraggableProvided, DraggableStateSnapshot } from '@hello-pangea/dnd';
import Card from '@mui/joy/Card';
import Chip from '@mui/joy/Chip';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import type React from 'react';
import type { TaskCardVO } from 'types/task';
import { useTaskModal } from '../../hooks/useTaskModal';

// Inline lightweight SVG icons to avoid pulling in @mui/icons-material package
const DescIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 24 24"
    width={16}
    height={16}
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    fill="none"
    aria-hidden="true"
    focusable="false"
    {...props}
  >
    <rect x={4} y={4} width={16} height={16} rx={2} />
    <line x1={8} y1={10} x2={16} y2={10} />
    <line x1={8} y1={14} x2={13} y2={14} />
  </svg>
);

export interface TaskCardProps {
  task: TaskCardVO;
  dragProvided: DraggableProvided;
  dragSnapshot: DraggableStateSnapshot;
}

// A Trello-like task card with subtle hover + drag styles and a quick detail modal
export const TaskCard: React.FC<TaskCardProps> = ({ task, dragProvided, dragSnapshot }) => {
  const { openTask } = useTaskModal();
  const title = typeof task.name === 'string' ? task.name : JSON.stringify(task.name);
  const hasDesc = !!task.content?.trim();
  const contentPreview = task.content ? task.content.trim().split(/\n/)[0].slice(0, 140) : '';
  function openModalViaUrl() {
    openTask(task.id);
  }

  return (
    <Card
      ref={dragProvided.innerRef}
      {...dragProvided.draggableProps}
      {...dragProvided.dragHandleProps}
      variant="outlined"
      sx={(theme) => ({
        p: 1.1,
        gap: 0.75,
        cursor: dragSnapshot.isDragging ? 'grabbing' : 'grab',
        position: 'relative',
        borderColor: dragSnapshot.isDragging ? 'primary.outlinedBorder' : 'divider',
        backgroundColor: dragSnapshot.isDragging
          ? theme.vars.palette.background.level1
          : theme.vars.palette.background.surface,
        boxShadow: dragSnapshot.isDragging
          ? '0 4px 12px rgba(0,0,0,0.4), 0 0 0 1px rgba(57,255,20,0.4)'
          : '0 1px 2px rgba(0,0,0,0.4)',
        transition: 'box-shadow 140ms, transform 140ms, background 140ms, border-color 140ms',
        '&:before': {
          content: '""',
          position: 'absolute',
          inset: 0,
          pointerEvents: 'none',
          borderRadius: 'inherit',
          boxShadow: dragSnapshot.isDragging
            ? '0 0 0 2px rgba(57,255,20,0.5), 0 0 0 4px rgba(57,255,20,0.15)'
            : 'none',
        },
        '&:hover': {
          boxShadow: '0 2px 8px rgba(0,0,0,0.48)',
          borderColor: 'primary.outlinedBorder',
          backgroundColor: theme.vars.palette.background.level1,
        },
        '&:active': {
          transform: 'translateY(1px)',
        },
      })}
      onClick={() => openModalViaUrl()}
    >
      {/* Labels row (if any) */}
      {task.labels && task.labels.length > 0 && (
        <Stack direction="row" spacing={0.5} sx={{ flexWrap: 'wrap' }}>
          {task.labels.map((l) => (
            <Chip
              key={l.id}
              size="sm"
              variant="solid"
              sx={{
                bgcolor: l.color || 'neutral.outlinedColor',
                maxWidth: 80,
                fontSize: 10,
              }}
            >
              {l.name}
            </Chip>
          ))}
        </Stack>
      )}
      <Typography level="body-sm" fontWeight={600} sx={{ lineHeight: 1.3 }}>
        {title || '(未命名任务)'}
      </Typography>
      {hasDesc && (
        <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
          {contentPreview}
          {task.content && task.content.length > contentPreview.length && '…'}
        </Typography>
      )}
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mt: 0.5 }}>
        <Stack direction="row" spacing={0.5} alignItems="center">
          {hasDesc && <DescIcon style={{ opacity: 0.6 }} />}
          {/* Future: badges for comments / attachments */}
        </Stack>
        {/* Members avatars placeholder */}
        {task.members && task.members.length > 0 && (
          <Stack direction="row" spacing={-0.5}>
            {task.members.slice(0, 3).map((m) => (
              <Sheet
                key={m.id}
                sx={{
                  width: 24,
                  height: 24,
                  borderRadius: '50%',
                  bgcolor: 'primary.solidBg',
                  color: 'primary.solidColor',
                  fontSize: 11,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px solid #fff',
                }}
              >
                {m.name?.[0]?.toUpperCase() || 'U'}
              </Sheet>
            ))}
            {task.members.length > 3 && (
              <Sheet
                sx={{
                  width: 24,
                  height: 24,
                  borderRadius: '50%',
                  bgcolor: 'neutral.softBg',
                  fontSize: 11,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px solid #fff',
                }}
              >
                +{task.members.length - 3}
              </Sheet>
            )}
          </Stack>
        )}
      </Stack>
    </Card>
  );
};
