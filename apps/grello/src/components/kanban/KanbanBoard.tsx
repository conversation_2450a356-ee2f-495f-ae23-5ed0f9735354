'use client';
import {
  DragDropContext,
  Draggable,
  type DraggableProvided,
  Droppable,
  type DroppableProvided,
  type DropResult,
} from '@hello-pangea/dnd';
import Badge from '@mui/joy/Badge';
import Box from '@mui/joy/Box';
import Sheet from '@mui/joy/Sheet';
import Skeleton from '@mui/joy/Skeleton';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import type { TaskCardVO } from 'types/task';
import { trpcClient } from '../../trpc-client/react';
import { TaskCard } from './TaskCard';

export type KanbanColumnId = 'INIT' | 'RUNNING' | 'DONE';

interface KanbanBoardProps {
  projectId: string;
}

export function KanbanBoard({ projectId }: KanbanBoardProps) {
  const tasksQuery = trpcClient.task.list.useQuery({ projectId });
  const utils = trpcClient.useUtils();
  const updateState = trpcClient.task.updateState.useMutation({
    onSuccess: () => utils.task.list.invalidate({ projectId }),
  });

  type Task = TaskCardVO & { systemState: KanbanColumnId };
  const tasks = (tasksQuery.data as unknown as Task[]) || [];
  const columns: { id: KanbanColumnId; title: string }[] = [
    { id: 'INIT', title: '待开始' },
    { id: 'RUNNING', title: '进行中' },
    { id: 'DONE', title: '已完成' },
  ];

  function handleDragEnd(result: DropResult) {
    const { destination, source, draggableId } = result;
    if (!destination) return; // dropped outside any column
    if (destination.droppableId === source.droppableId && destination.index === source.index)
      return; // no move

    const destCol = destination.droppableId as KanbanColumnId;
    const sourceCol = source.droppableId as KanbanColumnId;
    if (destCol !== sourceCol) {
      updateState.mutate({ taskId: draggableId, state: destCol });
    }
    // Note: reordering inside a column not persisted (no ordering field). Could add later.
  }

  const loading = tasksQuery.isLoading;

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', overflowX: 'auto', pb: 1 }}>
        {columns.map((c) => {
          const colTasks = tasks.filter((t) => t.systemState === c.id);
          const colorMap: Record<
            KanbanColumnId,
            { label: string; chipColor: 'primary' | 'neutral' | 'success' }
          > = {
            INIT: { label: '待开始', chipColor: 'neutral' },
            RUNNING: { label: '进行中', chipColor: 'primary' },
            DONE: { label: '已完成', chipColor: 'success' },
          };
          return (
            <Droppable droppableId={c.id} key={c.id}>
              {(dropProvided: DroppableProvided, dropSnapshot) => (
                <Sheet
                  variant="outlined"
                  ref={dropProvided.innerRef}
                  {...dropProvided.droppableProps}
                  sx={(theme) => ({
                    flex: 1,
                    minWidth: 280,
                    p: 1.5,
                    borderRadius: 'md',
                    minHeight: 340,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    background: dropSnapshot.isDraggingOver
                      ? 'linear-gradient(180deg, rgba(57,255,20,0.08), rgba(57,255,20,0.02))'
                      : theme.vars.palette.background.level2,
                    borderColor: dropSnapshot.isDraggingOver ? 'primary.outlinedBorder' : 'divider',
                    transition: 'background .18s, border-color .18s',
                    position: 'relative',
                    '&:before': {
                      content: '""',
                      position: 'absolute',
                      inset: 0,
                      pointerEvents: 'none',
                      borderRadius: 'inherit',
                      boxShadow: dropSnapshot.isDraggingOver
                        ? '0 0 0 1px rgba(57,255,20,0.4), 0 0 0 4px rgba(57,255,20,0.15)'
                        : 'none',
                    },
                  })}
                >
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{ mb: 0.5 }}
                  >
                    <Typography level="title-sm" sx={{ fontWeight: 600 }}>
                      {colorMap[c.id].label}
                    </Typography>
                    <Badge
                      size="sm"
                      variant={c.id === 'RUNNING' ? 'solid' : 'soft'}
                      color={colorMap[c.id].chipColor}
                      badgeContent={colTasks.length}
                      sx={{ '--Badge-paddingX': '6px', '--Badge-radius': '6px', fontWeight: 600 }}
                    />
                  </Stack>
                  <Stack gap={1} sx={{ flex: 1 }}>
                    {loading && colTasks.length === 0 && (
                      <>
                        <Skeleton
                          variant="rectangular"
                          level="body-sm"
                          height={50}
                          sx={{ borderRadius: 'sm' }}
                        />
                        <Skeleton
                          variant="rectangular"
                          level="body-sm"
                          height={50}
                          sx={{ borderRadius: 'sm' }}
                        />
                      </>
                    )}
                    {colTasks.map((t, index: number) => (
                      <Draggable draggableId={t.id} index={index} key={t.id}>
                        {(dragProvided: DraggableProvided, dragSnapshot) => (
                          <TaskCard
                            task={t}
                            dragProvided={dragProvided}
                            dragSnapshot={dragSnapshot}
                          />
                        )}
                      </Draggable>
                    ))}
                    {dropProvided.placeholder}
                    {colTasks.length === 0 && !loading && (
                      <Typography
                        level="body-xs"
                        sx={{ color: 'text.tertiary', py: 1, textAlign: 'center' }}
                      >
                        拖动任务到这里
                      </Typography>
                    )}
                  </Stack>
                </Sheet>
              )}
            </Droppable>
          );
        })}
      </Box>
    </DragDropContext>
  );
}
