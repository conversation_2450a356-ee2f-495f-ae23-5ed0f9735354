'use client';
import Chip, { type ChipProps } from '@mui/joy/Chip';
import type React from 'react';

export type TaskStatus = 'INIT' | 'RUNNING' | 'DONE';

interface StatusChipProps extends Omit<ChipProps, 'color'> {
  status: TaskStatus | string | undefined;
  size?: ChipProps['size'];
}

const map: Record<
  TaskStatus,
  { label: string; color: ChipProps['color']; variant: ChipProps['variant'] }
> = {
  INIT: { label: '待开始', color: 'neutral', variant: 'soft' },
  RUNNING: { label: '进行中', color: 'primary', variant: 'solid' },
  DONE: { label: '已完成', color: 'success', variant: 'soft' },
};

export const StatusChip: React.FC<StatusChipProps> = ({ status, size = 'sm', ...rest }) => {
  if (!status || !(status in map)) return null;
  const meta = map[status as TaskStatus];
  return (
    <Chip size={size} color={meta.color} variant={meta.variant} sx={{ fontWeight: 600 }} {...rest}>
      {meta.label}
    </Chip>
  );
};
