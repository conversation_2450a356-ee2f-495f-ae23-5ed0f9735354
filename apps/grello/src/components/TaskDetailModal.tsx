'use client';
import Divider from '@mui/joy/Divider';
import IconButton from '@mui/joy/IconButton';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import type React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { StatusChip } from './StatusChip';

const CloseIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 24 24"
    width={18}
    height={18}
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    fill="none"
    aria-hidden="true"
    focusable="false"
    {...props}
  >
    <line x1="18" y1="6" x2="6" y2="18" />
    <line x1="6" y1="6" x2="18" y2="18" />
  </svg>
);

export interface TaskDetailData {
  id: string;
  name: unknown;
  content?: string | null;
  outcome?: unknown | null;
  systemState?: string;
}

interface TaskDetailModalProps {
  task?: TaskDetailData | null;
  open: boolean;
  onClose?: () => void;
}

export function TaskDetailModal({ task, open, onClose }: TaskDetailModalProps) {
  const navigate = useNavigate();
  const location = useLocation();
  if (!task) return null;
  const title = typeof task.name === 'string' ? task.name : JSON.stringify(task.name);
  const hasDesc = !!task.content?.trim();
  function handleClose() {
    if (onClose) return onClose();
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete('task');
    navigate(
      {
        pathname: location.pathname,
        search: searchParams.size ? `?${searchParams.toString()}` : '',
      },
      { replace: false },
    );
  }
  return (
    <Modal open={open} onClose={handleClose}>
      <ModalDialog layout="center" sx={{ maxWidth: 720, width: '100%' }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-start"
          mb={1.5}
          gap={2}
        >
          <Stack gap={1} sx={{ flex: 1, minWidth: 0 }}>
            <Typography level="title-lg" sx={{ fontWeight: 600, lineHeight: 1.25 }}>
              {title}
            </Typography>
            {task.systemState && <StatusChip status={task.systemState} />}
          </Stack>
          <IconButton
            variant="plain"
            size="sm"
            onClick={handleClose}
            sx={{ '--IconButton-radius': '50%' }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Divider />
        <Sheet variant="plain" sx={{ mt: 2, maxHeight: '60vh', overflow: 'auto', pr: 1 }}>
          {hasDesc ? (
            <Typography level="body-sm" sx={{ whiteSpace: 'pre-wrap' }}>
              {task.content}
            </Typography>
          ) : (
            <Typography level="body-xs" color="neutral">
              暂无描述
            </Typography>
          )}
          {typeof task.outcome !== 'undefined' && task.outcome !== null
            ? (() => {
                const outcomeStr = JSON.stringify(task.outcome, null, 2);
                return (
                  <>
                    <Divider sx={{ my: 1 }} />
                    <Typography
                      level="body-xs"
                      sx={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}
                    >
                      {outcomeStr}
                    </Typography>
                  </>
                );
              })()
            : null}
        </Sheet>
      </ModalDialog>
    </Modal>
  );
}
