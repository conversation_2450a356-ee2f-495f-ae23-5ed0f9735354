'use client';
import Button from '@mui/joy/Button';
import Input from '@mui/joy/Input';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import type React from 'react';
import { useState } from 'react';

export interface CreateProjectModalProps {
  open: boolean;
  closeAction: () => void;
  createAction: (name: string, done: () => void) => void;
}

export const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  open,
  closeAction,
  createAction,
}) => {
  const [creating, setCreating] = useState(false);
  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (creating) return;
    const fd = new FormData(e.currentTarget);
    const name = (fd.get('name') as string)?.trim();
    if (!name) return;
    setCreating(true);
    createAction(name, () => {
      setCreating(false);
      closeAction();
    });
  }
  return (
    <Modal open={open} onClose={closeAction} disablePortal>
      <ModalDialog sx={{ width: 420, maxWidth: '100%' }}>
        <Typography level="title-lg" sx={{ mb: 1 }}>
          创建项目
        </Typography>
        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          <Input name="name" placeholder="项目名称" autoFocus size="md" variant="soft" />
          <Stack direction="row" spacing={1} mt={1}>
            <Button type="submit" loading={creating} color="primary" variant="solid">
              创建
            </Button>
            <Button
              type="button"
              variant="outlined"
              color="neutral"
              onClick={closeAction}
              disabled={creating}
            >
              取消
            </Button>
          </Stack>
        </form>
      </ModalDialog>
    </Modal>
  );
};
