'use client';
import IconButton from '@mui/joy/IconButton';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import type React from 'react';
import { Link } from 'react-router-dom';
import type { ProjectCardVO } from '../../types/project';

export const ProjectGrid: React.FC<{
  projects: ProjectCardVO[];
  onToggleStarAction: (id: string) => void;
}> = ({ projects, onToggleStarAction }) => {
  if (projects.length === 0)
    return (
      <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
        暂无项目
      </Typography>
    );
  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill,minmax(200px,1fr))',
        gap: 16,
      }}
    >
      {projects.map((p) => (
        <Sheet
          key={p.id}
          variant="outlined"
          sx={{
            borderRadius: 'md',
            p: 1.5,
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            minHeight: 120,
            background: 'var(--joy-palette-background-level1)',
            transition: 'border-color .2s, box-shadow .2s',
            '&:hover': {
              boxShadow: '0 2px 8px rgba(0,0,0,0.35)',
              borderColor: 'primary.outlinedBorder',
            },
          }}
        >
          <Stack
            direction="row"
            spacing={0.5}
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 0.5 }}
          >
            <Typography
              component={Link}
              to={`/${p.id}`}
              level="body-sm"
              sx={{
                fontWeight: 600,
                textDecoration: 'none',
                color: 'text.primary',
                flex: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {typeof p.name === 'string' ? p.name : JSON.stringify(p.name)}
            </Typography>
            <Tooltip title={p.starred ? '取消星标' : '加星'} variant="solid" arrow size="sm">
              <IconButton
                size="sm"
                variant="plain"
                onClick={() => onToggleStarAction(p.id)}
                sx={{ color: p.starred ? 'warning.solidBg' : 'text.tertiary' }}
              >
                ★
              </IconButton>
            </Tooltip>
          </Stack>
          <Typography level="body-xs" sx={{ color: 'text.tertiary', lineHeight: 1.4, flex: 1 }}>
            {p.description
              ? typeof p.description === 'string'
                ? p.description
                : JSON.stringify(p.description)
              : '无描述'}
          </Typography>
        </Sheet>
      ))}
    </div>
  );
};
