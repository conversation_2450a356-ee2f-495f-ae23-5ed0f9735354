'use client';
import Dropdown from '@mui/joy/Dropdown';
import IconButton from '@mui/joy/IconButton';
import ListDivider from '@mui/joy/ListDivider';
import Menu from '@mui/joy/Menu';
import MenuButton from '@mui/joy/MenuButton';
import MenuItem from '@mui/joy/MenuItem';
import Switch from '@mui/joy/Switch';
import Tooltip from '@mui/joy/Tooltip';
import type React from 'react';
import { useThemeController } from '../hooks/useThemeController';

const ThemeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={18}
    height={18}
    viewBox="0 0 24 24"
    stroke="currentColor"
    strokeWidth={2}
    fill="none"
    strokeLinecap="round"
    strokeLinejoin="round"
    aria-hidden="true"
    focusable="false"
    {...props}
  >
    <circle cx={12} cy={12} r={5} />
    <path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
  </svg>
);

export function ThemeSwitcher() {
  const { themeName, setThemeName, mode, toggleMode, availableThemes } = useThemeController();
  return (
    <Dropdown>
      <MenuButton
        slots={{ root: IconButton }}
        slotProps={{ root: { variant: 'plain', color: 'neutral', size: 'sm' } }}
      >
        <ThemeIcon />
      </MenuButton>
      <Menu size="sm" variant="soft" sx={{ minWidth: 180 }}>
        {availableThemes.map((t) => (
          <MenuItem key={t} selected={t === themeName} onClick={() => setThemeName(t)}>
            {t === 'grello' ? 'Grello Neon' : 'Classic'}
          </MenuItem>
        ))}
        <ListDivider />
        <MenuItem>
          <Tooltip
            title={mode === 'dark' ? '切换到浅色' : '切换到深色'}
            placement="left"
            variant="solid"
          >
            <Switch
              checked={mode === 'dark'}
              onChange={toggleMode}
              size="sm"
              color="primary"
              endDecorator={mode === 'dark' ? 'Dark' : 'Light'}
            />
          </Tooltip>
        </MenuItem>
      </Menu>
    </Dropdown>
  );
}
