import { act, renderHook } from '@testing-library/react';

// Polyfills for jsdom environment
if (typeof window !== 'undefined' && !window.matchMedia) {
  class MM implements MediaQueryList {
    matches = false;
    media: string;
    onchange: ((this: MediaQueryList, ev: MediaQueryListEvent) => unknown) | null = null;
    constructor(q: string) {
      this.media = q;
    }
    addListener() {}
    removeListener() {}
    addEventListener() {}
    removeEventListener() {}
    dispatchEvent() {
      return false;
    }
  }
  window.matchMedia = (query: string) => new MM(query);
}
if (typeof globalThis.fetch === 'undefined') {
  // Minimal fetch stub to avoid network attempts during provider init
  globalThis.fetch = async () =>
    new Response('{}', { status: 200, headers: { 'Content-Type': 'application/json' } });
}

import type React from 'react';
import GrelloProviders from '../app/GrelloProviders';
import { useThemeController } from '../hooks/useThemeController';

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <GrelloProviders>{children}</GrelloProviders>
);

describe('theme persistence', () => {
  beforeEach(() => {
    if (typeof window !== 'undefined') window.localStorage.clear();
  });
  it('persists selected themeName to localStorage', () => {
    const { result } = renderHook(() => useThemeController(), { wrapper });
    expect(result.current.themeName).toBe('grello');
    act(() => result.current.setThemeName('classic'));
    expect(typeof window !== 'undefined' && window.localStorage.getItem('grello-theme-name')).toBe(
      'classic',
    );
  });
  it('persists mode toggle explicitly', () => {
    const { result } = renderHook(() => useThemeController(), { wrapper });
    const startMode = result.current.mode;
    act(() => result.current.toggleMode());
    const stored =
      typeof window !== 'undefined'
        ? window.localStorage.getItem('grello-color-mode-explicit')
        : null;
    expect(stored && stored !== startMode).toBeTruthy();
  });
});
