// Import the generated client (will be generated into ./orm/prisma-client by prisma generate)
// Fallback to runtime require if types not yet generated.
// eslint-disable-next-line @typescript-eslint/no-var-requires
import { PrismaClient } from '../../orm/prisma-client';

declare global {
  // eslint-disable-next-line no-var
  var __grello_prisma: PrismaClient | undefined;
}

const prisma = global.__grello_prisma || new PrismaClient();
if (process.env.NODE_ENV !== 'production') global.__grello_prisma = prisma;

export const db = {
  prisma,
};
