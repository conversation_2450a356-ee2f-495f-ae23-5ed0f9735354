import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server';
import { initTRPC } from '@trpc/server';
import { generateNanoID } from 'basenext/utils';
import { ZodError, z } from 'zod';
import { db } from '@/lib/db';

export const createTRPCContext = async () => ({
  prisma: db.prisma,
});
export type Context = Awaited<ReturnType<typeof createTRPCContext>>;

const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    if (error.cause instanceof ZodError) {
      return {
        ...shape,
        message: 'Input validation error',
      };
    }
    return shape;
  },
});

export const router = t.router;
export const publicProcedure = t.procedure;

// Project router
const projectInput = z.object({ name: z.string().min(1), description: z.string().optional() });
export const projectRouter = router({
  list: publicProcedure.query(async ({ ctx }) => {
    return ctx.prisma.project.findMany({ orderBy: [{ starred: 'desc' }, { updatedAt: 'desc' }] });
  }),
  new: publicProcedure
    .input(projectInput.pick({ name: true, description: true }).partial({ description: true }))
    .mutation(async ({ input, ctx }) => {
      const id = generateNanoID('prj');
      const data: any = { id, name: input.name };
      if (input.description) data.description = input.description;
      return ctx.prisma.project.create({ data });
    }),
  toggleStar: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const proj = await ctx.prisma.project.findUnique({ where: { id: input.projectId } });
      if (!proj) throw new Error('Project not found');
      return ctx.prisma.project.update({
        where: { id: input.projectId },
        data: { starred: !proj.starred },
      });
    }),
});

// Task router
const taskCreateInput = z.object({ projectId: z.string(), name: z.string(), content: z.string() });
const taskIdInput = z.object({ taskId: z.string() });
const taskUpdateStateInput = z.object({
  taskId: z.string(),
  state: z.enum(['INIT', 'RUNNING', 'DONE']),
});
export const taskRouter = router({
  list: publicProcedure.input(z.object({ projectId: z.string() })).query(async ({ input, ctx }) => {
    return ctx.prisma.task.findMany({
      where: { projectId: input.projectId },
      orderBy: { createdAt: 'desc' },
    });
  }),
  new: publicProcedure.input(taskCreateInput).mutation(async ({ input, ctx }) => {
    const id = generateNanoID('tsk');
    return ctx.prisma.task.create({
      data: {
        id,
        projectId: input.projectId,
        name: input.name,
        content: input.content,
        systemState: 'INIT',
      },
    });
  }),
  get: publicProcedure.input(taskIdInput).query(async ({ input, ctx }) => {
    const task = await ctx.prisma.task.findUnique({ where: { id: input.taskId } });
    if (!task) throw new Error('Task not found');
    return task;
  }),
  run: publicProcedure.input(taskIdInput).mutation(async ({ input, ctx }) => {
    const exists = await ctx.prisma.task.findUnique({ where: { id: input.taskId } });
    if (!exists) throw new Error('Task not found');
    await ctx.prisma.task.update({
      where: { id: input.taskId },
      data: { systemState: 'RUNNING' },
    });
    return ctx.prisma.task.update({
      where: { id: input.taskId },
      data: {
        systemState: 'DONE',
        outcome: {
          finishedAt: new Date().toISOString(),
          summary: 'Task execution completed',
        },
      },
    });
  }),
  updateState: publicProcedure.input(taskUpdateStateInput).mutation(async ({ input, ctx }) => {
    const exists = await ctx.prisma.task.findUnique({ where: { id: input.taskId } });
    if (!exists) throw new Error('Task not found');
    return ctx.prisma.task.update({
      where: { id: input.taskId },
      data: { systemState: input.state },
    });
  }),
});

export const appRouter = router({ project: projectRouter, task: taskRouter });
export type AppRouter = typeof appRouter;
export type RouterInputs = inferRouterInputs<AppRouter>;
export type RouterOutputs = inferRouterOutputs<AppRouter>;
