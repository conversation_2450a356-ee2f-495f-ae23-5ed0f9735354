# Grello VI 需求稿（黑 + 荧光绿主题）

> 版本：v0.1（草案）
> 品牌定位：**Generative Trello** — “Plan by Human, Execute by AI.”
> 关键词：卡片化、AI 能量/闪电、极简、可扩展到产品 UI 与营销物料。

---

## 0. 总览目标

* 建立一套以 **黑 + 荧光绿** 为核心的品牌识别系统，适配：Logo、官网/App、Pitch Deck、社媒与物料。
* 兼顾产品可用性（深色/浅色 UI）、可访问性（WCAG 2.1 AA）、与工程落地（前端设计 Tokens、Tailwind 配置、导出规范）。

---

## 1. 品牌语调与文案

* **角色定位**：AI 项目执行官（助你“像老板一样指挥 + 自动完成任务”）。
* **语气**：自信、干练、以行动为导向；少夸张、多证据；句式短、动词强。
* **示例口号**：

  * Plan by Human, Execute by AI.
  * Cards that *do* the work.
  * From Boards to Results.

> 文案禁忌：避免“山寨 Trello”的表述；强调 **自动执行** 与 **生成式 AI** 差异。

---

2. Logo 规范（方向 2：魔法格子 + 光效）

构成：

图形：三列卡片格子（看板隐喻）+ 卡片边缘发光、周围点状能量粒子。

字标：Inter 字体，“Grello”，荧光绿，简洁现代。

安全空间：

外接圆半径 X，四周留白 ≥ 0.5X。

最小尺寸：

图文组合：≥ 120 px 宽；印刷 ≥ 30 mm。

单图标：≥ 24 px。

配色版本：

主版：黑底 + 荧光绿字标与图形（带柔光）

反白版：深底 → 白字，图标保持荧光绿光效

单色版：全黑/全白（低成本场景）

误用示例：

不可改变格子数量与比例，不可取消发光效果，不可替换为蓝色。

几何参数（建议）：

卡片圆角：R = 12–16%；列间隙：整体宽度 10–15%；发光范围：卡片外扩 20–30 px，柔边。

---

## 3. 色彩系统（Brand Colors）

> 主色为 **荧光绿**，强调“能量/执行”；深色背景为主品牌舞台；提供浅色 UI 与印刷近似。

### 3.1 品牌主色

* **Grello Neon** `#39FF14` （主荧光绿）
* **Grello Mint** `#6BFF8A`（用于悬停/渐变过渡）
* **Grello Lime** `#A6FF00`（高亮点缀，慎用）

### 3.2 深色背景与中性色

* **Grello Black** `#0B0E11`（主背景）
* **Onyx** `#12161C`
* **Graphite** `#1A2028`
* **Charcoal** `#232A34`
* **Slate** `#2E3744`
* **Mist (文本反差用浅灰)** `#EEF2F7`
* **White** `#FFFFFF`

### 3.3 功能色（UI）

* **Success** `#22C55E`
* **Warning** `#FFCC00`
* **Danger** `#FF3B30`
* **Info** `#38BDF8`

### 3.4 渐变与发光

* **AI Surge（线性）**: `linear-gradient(130deg, #00FF66 0%, #39FF14 48%, #A6FF00 100%)`
* **Neon Glow（外发光建议）**: 外发光色 `#39FF14`，强度 30–60%，柔边 16–24 px（按 1× 尺寸建议）。

### 3.5 使用占比（品牌面）

* 深色背景 60–70%，荧光绿 15–25%，中性色 10–20%，功能色 ≤5%。
* **避免** 将荧光绿用作大段正文文字色。

### 3.6 对比度建议（WCAG AA）

* 深底上正文文本：`#FFFFFF`/`#EEF2F7` 对 `#0B0E11` → **≥ 7:1**
* 绿底按钮上的文字：使用 `#0B0E11`（黑）或 `#12161C`（深灰）→ **≥ 4.5:1**
* 仅将 **荧光绿** 用于强调/按钮填充/图标，不用于小字号正文。

---

## 4. 字体系统（Inter）

* **主字体**：Inter Variable（优先使用可变字重）
* **备用**：system-ui, Helvetica Neue, Arial, Noto Sans CJK SC（中文环境）

**字重建议**：

* 标题：SemiBold 600 / Bold 700
* 导航/按钮：Medium 500
* 正文：Regular 400

**排版系统**：

* 行高：标题 1.2–1.25，正文 1.5–1.65
* 字距：按钮与大写字母微正字距（+0.2% \~ +0.5%）

---

## 5. 图标与插画

* **图标风格**：2 px 线性描边，圆角端点；在深色背景上使用 `#EEF2F7` 或 `#FFFFFF`；强调态可用 `#39FF14`。
* **插画风格**：几何卡片 + 能量“电流/粒子”线；限制颜色 ≤ 3（黑/白/绿）。

---

## 6. 组件与 UI 规范（核心）

**基础栅格**：8 px 基准 → 卡片与按钮圆角 **R8/12/16**。

**按钮（Button）**：

* Primary：绿底 `#39FF14`，文字 `#0B0E11`；Hover：`#6BFF8A`；Active：`#00FF66`；Focus：1 px 白 + 2 px 绿外描边。
* Secondary：深灰底 `#232A34` + 白字；Hover：`#2E3744`。

**卡片（Card）**：

* 背景：`#12161C`；边框：`#2E3744`；阴影：0 4 16 rgba(0,0,0,.35)。
* 高亮态：边框换为 `#39FF14`，或左侧 3 px 绿色分隔条。

**表单（Input）**：

* 填充 `#12161C`；边框 `#2E3744`；聚焦：边框 `#39FF14` + 内阴影 `rgba(57,255,20,.2)`。

**标签（Tag/Status）**：

* 成功：`#22C55E`；进行中（AI）：`#39FF14`；待处理：`#8A94A6`；阻塞：`#FF3B30`。

**数据可视化**：

* 主系列：`#39FF14`；对比系列：`#8A94A6`、`#586270`。
* 网格线：`#232A34`；轴文字：`#B3BDCC`。

---

## 7. 无障碍（A11y）

* 文本最小字号：网页正文 ≥ 14 px，移动端正文 ≥ 15/16 px。
* 交互目标：最小 40×40 px；相邻点击区 ≥ 8 px 间隙。
* 键盘可达：Focus 可见（双描边方案）。
* 动效可控：动画时间 120–200 ms，提供“减少动态”选项。

---

## 8. 设计 Tokens（工程落地）

### 8.1 CSS 变量（示例）

```css
:root {
  --grello-black: #0B0E11;
  --grello-onyx: #12161C;
  --grello-graphite: #1A2028;
  --grello-slate: #2E3744;
  --grello-white: #FFFFFF;

  --grello-neon: #39FF14;
  --grello-neon-hover: #6BFF8A;
  --grello-neon-active: #00FF66;

  --success: #22C55E;
  --warning: #FFCC00;
  --danger:  #FF3B30;
  --info:    #38BDF8;

  --radius-sm: 8px;  
  --radius-md: 12px;
  --radius-lg: 16px;
}
```

### 8.2 Tailwind 主题片段

```ts
// tailwind.config.ts（片段）
export default {
  theme: {
    extend: {
      colors: {
        grello: {
          black: '#0B0E11',
          onyx: '#12161C',
          slate: '#2E3744',
          neon: '#39FF14',
          neonHover: '#6BFF8A',
          neonActive: '#00FF66',
        },
      },
      borderRadius: {
        sm: '8px', md: '12px', lg: '16px',
      },
      boxShadow: {
        card: '0 4px 16px rgba(0,0,0,.35)',
      }
    }
  }
}
```

### 8.3 Design Tokens（JSON）

```json
{
  "color": {
    "background": {"value": "#0B0E11"},
    "text": {"value": "#FFFFFF"},
    "brand": {"primary": {"value": "#39FF14"}, "hover": {"value": "#6BFF8A"}, "active": {"value": "#00FF66"}},
    "border": {"value": "#2E3744"}
  },
  "radius": {"sm": {"value": "8px"}, "md": {"value": "12px"}, "lg": {"value": "16px"}},
  "shadow": {"card": {"value": "0 4 16 rgba(0,0,0,.35)"}}
}
```

---

## 9. 动效（Motion）

* 语义：**AI Surge**（能量贯穿卡片）。
* 基本参数：进入 160–200 ms、悬停 120 ms、离开 120 ms；缓动 `cubic-bezier(.2,.8,.2,1)`。
* 反馈：按钮点击产生 4–8 px 内发光，持续 ≤ 120 ms。

---

## 10. 应用场景规范

**App 图标**：

* iOS：1024×1024（圆角由系统裁切）；Android：512×512（自适应图标/前景 + 背景）。

**Favicon & OG**：

* Favicon：32×32 / 48×48 PNG + SVG；
* Web App Manifest：`icons` 提供 192 / 512；
* Open Graph：1200×630（深底 + 绿 Logo，居中）。

**Pitch Deck**：

* 模板比例 16:9；封面使用深色背景 + 局部能量渐变；页眉细线 `#2E3744`；关键数字用荧光绿。

**社媒**：

* Twitter/X Header：1500×500；Profile：400×400；
* LinkedIn Banner：1584×396；YouTube Banner：2560×1440（安全区 1546×423）。

**印刷**：

* CMYK 近似（参考）：

  * Grello Neon `#39FF14` ≈ C 65 / M 0 / Y 100 / K 0（供应商需二次校色）
  * Grello Black `#0B0E11` ≈ C 80 / M 70 / Y 60 / K 80
* 如需专色，建议与印厂沟通 Pantone 荧光绿近似号，打样确认。

---

## 11. 文件导出与命名

* **Logo 主文件**：`/brand/logo/grello-logo-primary.svg`（矢量，首选）
* **反色/单色**：`/brand/logo/grello-logo-reverse.svg`，`/brand/logo/grello-logo-mono-black.svg`
* **App Icon**：`/brand/appicon/grello-appicon-1024.png`（另导 512/256/128/64）
* **社媒适配**：`/brand/social/grello-og-1200x630.png` 等
* **设计 Tokens**：`/brand/tokens/grello-tokens.json`，`/brand/tokens/tailwind.config.ts`

---

## 12. 审核清单（给设计/前端/运营）

* [ ] Logo 清晰可读（≥ 最小尺寸）
* [ ] 背景对比度达标（AA）
* [ ] 荧光绿未用于小字号正文
* [ ] 交互 Focus 明显且键盘可达
* [ ] 社媒/OG 尺寸正确
* [ ] 印刷前做打样，确认萤光色
* [ ] 不使用 Trello 蓝色或其典型几何比例

---

## 13. 后续扩展（可选）

* 次要点缀色（如紫/电蓝）在 **数据可视化** 场景受控引入。
* 插画系统组件化（卡片、闪电、粒子路径）。
* 统一图标库（24 px/32 px 基线网格）。

---

> 备注：本稿为 **VI 需求草案**，用于指导 Logo 细化与整套物料设计/开发落地。后续可根据真实屏幕/打印打样结果微调色值与参数。
