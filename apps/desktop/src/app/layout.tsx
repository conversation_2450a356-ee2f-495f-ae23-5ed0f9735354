// 'use client';

import { getDictionary } from '@bika/contents/i18n/translate';
// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { WebsiteLayoutNext } from '@bika/domains/website/client/website-layout-next';
import type { Metadata } from 'next';
import type React from 'react';
import { Suspense } from 'react';
import './globals.css';
import { getBaseUrl } from '@/utils/base';
// const geistSans = Geist({
//   variable: '--font-geist-sans',
//   subsets: ['latin'],
// });

// const geistMono = Geist_Mono({
//   variable: '--font-geist-mono',
//   subsets: ['latin'],
// });

export const metadata: Metadata = {
  title: 'Bika.ai Desktop',
  description: 'Bika.ai Desktop Client',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dictionary = await getDictionary('en');
  return (
    <Suspense fallback={<div>Suspense Loading...</div>}>
      <WebsiteLayoutNext
        headers={new Headers()}
        mode={'SPA'}
        dictionary={dictionary}
        initialData={{
          hostname: getBaseUrl(), // 'https://bika.ai',
          servers: {
            docServerUrl: '',
            storagePublicUrl: '',
            formAppAIBaseUrl: '',
          },
          appEnv: 'DESKTOP',
          version: process.env.VERSION || 'desktop',
          themeMode: 'dark',
          themeStyle: 'default',
          env: {},
          headers: new Headers(),
          isFromCNHost: false,
          isFromCN: false,
          locale: 'en',
          isHome: false,
        }}
        // themeMode={
        //   'dark'
        //   // theme === 'system' ? cookie.get('app-theme-mode')?.value || 'dark' : theme
        // }
      >
        {children}
      </WebsiteLayoutNext>
    </Suspense>
  );
}
