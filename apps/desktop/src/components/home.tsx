'use client';
import { useTRPCQuery } from '@bika/api-caller/context';
import { SpaceClientLayout } from '@bika/domains/space/client/routing/space-layout';
import SpaceRouterEntryPage from '@bika/domains/space/client/routing/space-router-entry-page';
import { useAuth } from '@bika/domains/website/client/context';
import { LogoGraph } from '@bika/ui/components/logo/index';
import { useEffect, useState } from 'react';
import { Login } from './login';

type PageStatus = 'loading' | 'login' | 'home';

export const Home = () => {
  const trpcQuery = useTRPCQuery();
  const auth = useAuth({ initialAuth: null });
  const { refetchMe } = auth;
  const [pageStatus, setPageStatus] = useState<PageStatus | null>('loading');
  const getLastActiveSpace = trpcQuery.space.getLastActiveSpace.useQuery(undefined, {
    enabled: false,
  });

  useEffect(() => {
    refetchMe().then(async (res) => {
      if (res) {
        setPageStatus('home');
        getLatestSpaceId();
        return;
      }
      setPageStatus('login');
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getLatestSpaceId = async () => {
    const data = await getLastActiveSpace.refetch();
    window.history.pushState({}, '', '/?pathname=/space/' + data.data?.id);
  };

  if (pageStatus === 'loading') {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}
      >
        <LogoGraph />
      </div>
    );
  }

  if (pageStatus === 'login') {
    return <Login />;
  }

  return (
    <>
      <SpaceRouterEntryPage routeLayout={SpaceClientLayout} nodeServerError={''} mode="SPA" />
    </>
  );
};
