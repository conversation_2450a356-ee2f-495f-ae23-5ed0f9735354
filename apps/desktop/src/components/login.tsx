'use client';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button-component';
import { StandalonePage } from '@bika/ui/components/standalone/index';
import { onOpenUrl } from '@tauri-apps/plugin-deep-link';
import { openUrl } from '@tauri-apps/plugin-opener';
import { useEffect, useState } from 'react';
export const Login = () => {
  const [waitLogin, setWaitLogin] = useState<boolean>(false);
  // const { basePath } = useInitContext();

  const globalContext = useGlobalContext();
  const basePath = globalContext.hostname;

  useEffect(() => {
    onOpenUrl((urls) => {
      const url = new URL(urls[0]);
      const token = url.searchParams.get('token');
      localStorage.setItem('auth-token', token || '');
      // setWaitLogin(false);
      // setIsLogin(false);
      window.location.reload();
    });
  }, []);

  return (
    <StandalonePage>
      <div className="flex flex-col items-center justify-center h-full">
        {waitLogin ? (
          <div className="text-h3 text-[--text-primary] w-[280px] text-center">
            Go to the browser to complete login
          </div>
        ) : (
          <>
            <div className="text-h3 text-[--text-primary] w-[280px] text-center">
              Log in or create an account to collaborate
            </div>
            <div>
              <Button
                sx={{ width: '280px', height: '48px', marginTop: '24px', marginBottom: '24px' }}
                onClick={async () => {
                  // await openUrl('http://localhost:3000/auth/deep-page?mode=login&from=desktop');
                  await openUrl(`${basePath}/auth/deep-page?mode=login&from=desktop`);
                  setWaitLogin(true);
                }}
              >
                Log in with browser
              </Button>
            </div>
          </>
        )}
      </div>
    </StandalonePage>
  );
};
