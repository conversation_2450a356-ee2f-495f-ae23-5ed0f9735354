{"$schema": "../../../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Bika AI", "version": "0.1.0", "identifier": "ai.bika", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3030", "beforeDevCommand": "pnpm run dev", "beforeBuildCommand": "pnpm run build"}, "app": {"windows": [{"title": "Bika AI", "width": 1200, "height": 1000, "resizable": true, "fullscreen": false}], "security": {"csp": ""}}, "bundle": {"active": true, "targets": "dmg", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"signingIdentity": "Developer ID Application: Vika Limited (P3NHJFZ69B)"}}, "plugins": {"deep-link": {"desktop": {"schemes": ["bika"]}}}}