import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import { Platform } from 'react-native';
import { useGlobalContext } from '@/context/global/provider';
import { deviceTypeToString } from '@/utils/device';
import { handleRedirect } from '@/utils/notification';

export interface PushNotificationState {
  expoPushToken?: Notifications.ExpoPushToken;
  notification?: Notifications.Notification;
}

export const usePushNotification = (): PushNotificationState => {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });

  const [expoPushToken, setExpoPushToken] = useState<Notifications.ExpoPushToken | undefined>();

  const [notification, setNotification] = useState<Notifications.Notification | undefined>();

  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    registerForPushNotificationsAsync().then((token) => {
      setExpoPushToken(token as any);
    });

    notificationListener.current = Notifications.addNotificationReceivedListener((n) => {
      setNotification(n);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('notification response', response);

      // SecureStore.setItem('next_app_action', JSON.stringify(notification));

      router.push(handleRedirect(response.notification.request.content.data as any) as any);
    });

    return () => {
      Notifications.removeNotificationSubscription(notificationListener.current!);
      Notifications.removeNotificationSubscription(responseListener.current!);
    };
  }, []);

  return {
    expoPushToken,
    notification,
  };
};

async function registerForPushNotificationsAsync(): Promise<string | undefined> {
  let token: string | undefined;

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.error('Failed to get push token for push notification!');
      return;
    }

    token = (
      await Notifications.getExpoPushTokenAsync({
        projectId: Constants?.expoConfig?.extra?.eas.projectId,
      })
    ).data;
  } else {
    console.error('Must use physical device for Push Notifications');
  }

  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  return token;
}

export async function schedulePushNotification({
  title,
  body,
  data,
}: {
  title: string;
  body: string;
  data: any;
}) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
    },
    trigger: { seconds: 2 },
  });
}

export function useNotificationObserver() {
  useEffect(() => {
    let isMounted = true;

    const redirect = (notification: Notifications.Notification) => {
      const { data } = notification.request.content;

      const url = data?.url;

      return null;
    };

    Notifications.getLastNotificationResponseAsync().then((response) => {
      if (!isMounted || !response?.notification) {
        return;
      }
      redirect(response?.notification);
    });

    const subscription = Notifications.addNotificationResponseReceivedListener((response) => {
      redirect(response.notification);
    });

    return () => {
      isMounted = false;
      subscription.remove();
    };
  }, []);
}

export function useNotificationRegistrationObserver() {
  const { expoPushToken } = usePushNotification();

  const { trpc } = useGlobalContext();

  const { brand, modelName, deviceType, osName, osVersion } = Device;

  const createOrUpdateDevice = async () => {
    if (!expoPushToken) {
      return;
    }

    await trpc.user.createOrUpdateUserDevice.mutate({
      brand: brand ?? undefined,
      deviceName: modelName ?? undefined,
      deviceType: deviceTypeToString(deviceType),
      osName: osName ?? undefined,
      osVersion: osVersion ?? undefined,
      pushToken: (expoPushToken as unknown as string) ?? undefined,
      type: 'HARDWARE_DEVICE',
      name: '硬件设备绑定',
      description: '硬件设备集成绑定',
    });
  };

  useEffect(() => {
    createOrUpdateDevice();

    return () => {
      'cleanup';
    };
  }, [expoPushToken]);
}
