import AsyncStorage from '@react-native-async-storage/async-storage';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import LottieView from 'lottie-react-native';
import React, { useRef } from 'react';
import { ImageBackground, ScrollView, TouchableOpacity, View } from 'react-native';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

export default function WelcomeScreen() {
  const router = useRouter();

  const { locale } = useGlobalContext();

  const { t } = locale;

  const colors = useColor();

  const animation = useRef(null);

  const redirectToAuth = async () => {
    await SecureStore.deleteItemAsync('session_cookie');

    await AsyncStorage.clear().then(() => {
      router.push('/auth');
    });
  };

  const features = [
    {
      logo: t.slogan.highlights[0].icon,
      title: t.slogan.highlights[0].name,
      description: t.slogan.highlights[0].description,
    },
    {
      logo: t.slogan.highlights[1].icon,
      title: t.slogan.highlights[1].name,
      description: t.slogan.highlights[1].description,
    },
    {
      logo: t.slogan.highlights[2].icon,
      title: t.slogan.highlights[2].name,
      description: t.slogan.highlights[2].description,
    },
    {
      logo: t.slogan.highlights[3].icon,
      title: t.slogan.highlights[3].name,
      description: t.slogan.highlights[3].description,
    },
  ];

  return (
    <View className="flex-1">
      <ImageBackground
        source={require('@/assets/images/welcome/background.png')}
        resizeMode="cover"
        className="flex-1"
      >
        <ScrollView className="flex-1">
          <View
            style={{
              top: 80,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'flex-start',
              gap: 20,
              marginHorizontal: 16,
            }}
          >
            <View className="gap-2">
              <LottieView
                autoPlay
                ref={animation}
                style={{
                  width: 42,
                  height: 42,
                }}
                source={require('@/assets/lottie/logo.json')}
              />
              <View>
                <VText variant="H1" color={colors.textCommonPrimary}>
                  {t.welcome.title}
                </VText>
                <VText variant="B1" color={colors.textCommonTertiary}>
                  {t.welcome.message}
                </VText>
              </View>
            </View>
            <View className="gap-2">
              {features.map((feature, index) => (
                <View key={index} className="w-full flex-row items-start gap-2">
                  <Image
                    source={`${getBaseUrl()}${feature.logo}`}
                    style={{
                      width: 24,
                      height: 24,
                    }}
                  />
                  <View className="shrink justify-center space-y-1">
                    <VText variant="H4" color="#18CC90">
                      {feature.title}
                    </VText>
                    <VText variant="B2" color={colors.textCommonTertiary}>
                      {feature.description}
                    </VText>
                  </View>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
        <View className="absolute bottom-8 flex w-full flex-col items-center justify-center self-center">
          <TouchableOpacity
            style={{
              alignItems: 'center',
              alignSelf: 'center',
              backgroundColor: '#7A63FF',
              borderRadius: 8,
              display: 'flex',
              height: 48,
              justifyContent: 'center',
              margin: 8,
              width: '90%',
            }}
            onPress={redirectToAuth}
          >
            <VText variant="H5" color={colors.textStaticPrimary}>
              {t.welcome.get_started}
            </VText>
          </TouchableOpacity>
        </View>
      </ImageBackground>
    </View>
  );
}
