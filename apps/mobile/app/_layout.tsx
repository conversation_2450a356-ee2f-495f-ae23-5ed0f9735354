import { APICallerProvider } from '@bika/api-caller';
import { LocaleProvider } from '@bika/contents/i18n';
import { getDictionary } from '@bika/contents/i18n/translate';
import type { AuthVO } from '@bika/types/user/vo';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { ThemeProvider } from '@react-navigation/native';
import type { Locale } from 'basenext/i18n';
import { Slot, useRouter } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { PostHogProvider } from 'posthog-react-native';
import { useEffect, useState } from 'react';
import { LogBox, useColorScheme } from 'react-native';
import { DarkTheme, LightTheme } from '@/constants/Colors';
import { ActionProvider, useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { GlobalProvider, useGlobalContext } from '@/context/global';
import { Theme } from '@/context/global/types';
import { getSessionCookie, setSessionCookie } from '@/context/global/utils/session';
import { usei18n } from '@/hooks/usei18n';
import { baseUrl } from '@/utils/base';
import { timeoutPromise } from '@/utils/network';
import { getTimezone } from '@/utils/time';
import '../styles/global.css';

export { ErrorBoundary } from 'expo-router';

LogBox.ignoreAllLogs();

const InitialLayout = () => {
  const router = useRouter();

  const { trpc } = useGlobalContext();

  const { showSnackbar } = useAction();

  const { setInitLocale, getStoredLocale } = usei18n();

  const onRedirect = async () => {
    const sessionCookie = getSessionCookie();

    if (!sessionCookie) {
      return router.replace('/welcome');
    }

    try {
      const me = await Promise.race([trpc.auth.getMe.query(), timeoutPromise]);

      console.log('me', me);

      console.log('sessionCookie', sessionCookie);

      if (me) {
        if (!sessionCookie) {
          await setSessionCookie((me as AuthVO)?.session?.id ?? '');
        }
        router.replace(`/(app)/app`);
      } else {
        router.replace('/welcome');
      }
    } catch (err) {
      showSnackbar(`${err}, please try again later.`, ActionType.ERROR);
    }
  };

  useEffect(() => {
    Promise.resolve(setInitLocale()).then(() => {
      Promise.resolve(getStoredLocale()).then(() => {
        onRedirect();
      });
    });
  }, []);

  return <Slot />;
};

const RootLayout = () => {
  const colorScheme = useColorScheme();
  const [dictionary, setDictionary] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initLayout = async () => {
      try {
        const locale: Locale = 'en';
        const dict = await getDictionary(locale);
        setDictionary(dict);
      } catch (error) {
        console.error('Error initializing layout:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initLayout();
  }, [colorScheme]);

  if (isLoading || !dictionary) {
    return null; // 或者返回一个加载指示器
  }

  const getHeaders = () => {
    const headers = new Map();
    const session = getSessionCookie();

    if (session) headers.set('Authorization', `Bearer ${session}`);

    headers.set('x-trpc-source', 'mobile');
    headers.set('x-bika-timezone', getTimezone());

    return Object.fromEntries(headers);
  };

  const theme = (SecureStore.getItem('theme') as Theme) ?? Theme.System;
  const currentTheme = theme === Theme.System ? colorScheme : theme;
  const currentThemeValue = currentTheme === Theme.Light ? LightTheme : DarkTheme;

  return (
    <PostHogProvider
      apiKey="phc_w6CZX84QHSNkZAyksgHlAwvdY3nFnmrbjmXKR3NG7IX"
      options={{
        host: 'https://app.posthog.com',
      }}
    >
      <LocaleProvider defaultLocale="en" defaultDictionary={dictionary}>
        <APICallerProvider bikaBasePath={baseUrl} headers={getHeaders()}>
          <GlobalProvider>
            <ThemeProvider value={currentThemeValue}>
              <ActionSheetProvider>
                <ActionProvider>
                  <InitialLayout />
                </ActionProvider>
              </ActionSheetProvider>
            </ThemeProvider>
          </GlobalProvider>
        </APICallerProvider>
      </LocaleProvider>
    </PostHogProvider>
  );
};

export default RootLayout;
