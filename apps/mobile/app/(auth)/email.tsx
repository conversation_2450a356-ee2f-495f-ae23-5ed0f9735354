import type { AuthVO } from '@bika/types/user/vo';
import { useEffect, useState } from 'react';
import { SafeAreaView, TouchableOpacity, View } from 'react-native';
import { StaticLoading } from '@/components/Loading';
import { VInput, VInputWithButton } from '@/components/UI/Input';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global';
import { setSessionCookie } from '@/context/global/utils/session';
import { useColor } from '@/hooks/useColor';

export default function AuthEmailScreen() {
  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { reloadApp, showSnackbar } = useAction();

  const colors = useColor();

  const [loading, setLoading] = useState(false);

  const [email, setEmail] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);

  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      if (countdown > 0) {
        setCountdown(countdown - 1);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [countdown]);

  useEffect(() => {
    if (countdown === 0) {
      setIsCodeSent(false);
    }
  }, [countdown]);

  const sendVerifyCode = async () => {
    setIsCodeSent(true);
    setCountdown(60);
    setLoading(true);

    await trpc.email.sendVerificationMail
      .mutate({ email })
      .then((res) => {
        if (res) {
          showSnackbar(t.action.email_sent, ActionType.SUCCESS);
        }

        setLoading(false);
      })
      .catch((e) => {
        console.log('🚀 ~ sendVerifyCode ~ e:', e.message);
      });
  };

  const onSignInPress = async () => {
    setLoading(true);

    try {
      await trpc.auth.login
        .mutate({
          type: 'EMAIL_CODE',
          username: email,
          credential: verifyCode,
        })
        .then(async (me) => {
          console.log('email auth me', me);
          setLoading(false);

          if (!me?.session?.id) {
            return;
          }

          await setSessionCookie((me as unknown as AuthVO)?.session?.id ?? '').then(() => {
            reloadApp();
          });
        });
    } catch (err: any) {
      showSnackbar(`error: ${err.message}`, ActionType.ERROR);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          flex: 1,
          justifyContent: 'space-start',
          marginHorizontal: 16,
          marginVertical: 20,
          padding: 24,
        }}
      >
        <StaticLoading loading={loading} />
        <View></View>
        <View
          style={{
            gap: 8,
            marginTop: 80,
          }}
        >
          <VInput
            placeholder={t.user.email}
            type="emailAddress"
            value={email}
            onChangeText={setEmail}
          />
          <VInputWithButton
            placeholder={t.user.verification_code}
            buttonTitle={countdown > 0 ? String(countdown) : t.action.send}
            value={verifyCode}
            onChangeText={setVerifyCode}
            onPress={sendVerifyCode}
            disabled={countdown > 0 || isCodeSent || !email}
          />
          <TouchableOpacity
            style={{
              alignItems: 'center',
              backgroundColor:
                loading || !email || !verifyCode ? colors.bgBrandDisabled : colors.bgBrandDefault,
              borderRadius: 10,
              display: 'flex',
              flexDirection: 'row',
              gap: 8,
              height: 48,
              justifyContent: 'center',
              padding: 10,
            }}
            disabled={loading || !email || !verifyCode}
            onPress={onSignInPress}
          >
            <VText variant="B2" color={colors.textStaticPrimary}>
              {t.auth.sign_in}
            </VText>
          </TouchableOpacity>
        </View>
        {/* <View>
          <Text
            style={{
              color: colors.textCommonPrimary,
              fontSize: 12,
              fontWeight: '400',
            }}
          >
            {t.auth.terms_and_conditions}
          </Text>
        </View> */}
      </View>
    </SafeAreaView>
  );
}
