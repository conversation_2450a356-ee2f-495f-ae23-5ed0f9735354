import { AntDesign } from '@expo/vector-icons';

import Checkbox from 'expo-checkbox';
import { useRouter } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useState } from 'react';
import { SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { EmailFilled } from '@/assets/svg';
import { AppleAuthButton } from '@/components/Auth/apple';
import { GitHubAuthButton } from '@/components/Auth/github';
import { GoogleAuthButton } from '@/components/Auth/google';
import { StaticLoading } from '@/components/Loading';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global';
import { setSessionCookie } from '@/context/global/utils/session';
import { useColor } from '@/hooks/useColor';
import { isChinaVisitor } from '@/utils/base';

export default function AuthScreen() {
  const router = useRouter();

  const colors = useColor();

  const isFromChina = isChinaVisitor();

  const { reloadApp, showSnackbar } = useAction();

  const [textChecked, setTextChecked] = useState(false);

  const [authLoading, setAuthLoading] = useState(false);

  const toggleAuthLoading = (isLoading: boolean) => {
    setAuthLoading(isLoading);
  };

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const isCheckText = () => {
    if (textChecked) {
      return true;
    }
    showSnackbar(t.auth.agree_title, ActionType.ERROR);
    return false;
  };

  const doQuickLogin = async () => {
    if (!isCheckText()) {
      return;
    }
    console.log('quickLogin start');

    setAuthLoading(true);

    await trpc.auth.quickLogin.mutate().then((res) => {
      const sessionToken = res.session.id ?? '';

      console.log('quickLogin res', res);

      setSessionCookie(sessionToken).then(() => {
        reloadApp();
      });

      console.log('quickLogin done');

      setAuthLoading(false);
    });
  };

  const checkTerms = () => {
    WebBrowser.openBrowserAsync('https://bika.ai/zh-CN/terms-of-service');
  };

  const checkPrivacy = () => {
    WebBrowser.openBrowserAsync('https://bika.ai/zh-CN/privacy');
  };

  return (
    <SafeAreaView style={{ flex: 1, flexDirection: 'column' }}>
      <View style={styles.container}>
        <StaticLoading loading={authLoading} />
        <View style={styles.header}>
          <VText variant="H2" color={colors.textCommonPrimary}>
            {t.auth.login_and_register}
          </VText>
        </View>
        <View style={{ gap: 20, flex: 2, marginTop: 80 }}>
          <View style={styles.main}>
            <TouchableOpacity
              style={{
                alignItems: 'center',
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                display: 'flex',
                flexDirection: 'row',
                gap: 8,
                height: 48,
                justifyContent: 'center',
                padding: 10,
              }}
              onPress={() => {
                if (isCheckText()) {
                  router.push('/(auth)/email');
                }
              }}
            >
              {/* <AntDesign name="mail" size={24} color={colors.rainbowIndigo5} /> */}
              <EmailFilled
                width={24}
                height={24}
                color={colors.rainbowIndigo5}
                fill={colors.rainbowIndigo5}
              />
              <VText variant="H7" color={colors.textCommonPrimary}>
                {t.auth.continue_with_email}
              </VText>
            </TouchableOpacity>

            <GoogleAuthButton onLoading={toggleAuthLoading} isCheckText={isCheckText} />

            <View style={styles.orContainer}>
              <View
                style={{
                  backgroundColor: colors.borderCommonDefault,
                  flex: 1,
                  height: 1,
                  marginHorizontal: 16,
                }}
              />
              <VText variant="H6" color={colors.textCommonSecondary}>
                {t.auth.or}
              </VText>
              <View
                style={{
                  backgroundColor: colors.borderCommonDefault,
                  flex: 1,
                  height: 1,
                  marginHorizontal: 16,
                }}
              />
            </View>
            <TouchableOpacity
              style={{
                alignItems: 'center',
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                display: 'flex',
                flexDirection: 'row',
                gap: 8,
                height: 48,
                justifyContent: 'center',
                padding: 10,
              }}
              onPress={doQuickLogin}
            >
              <AntDesign name="user" size={24} color={colors.textCommonPrimary} />
              <VText variant="H6" color={colors.textCommonPrimary}>
                {t.auth.quick_login}
              </VText>
            </TouchableOpacity>
            <View className="flex flex-row items-center justify-center">
              <VText variant="B4" color={colors.textCommonSecondary}>
                {t.auth.quick_login_description}
              </VText>
            </View>
            <View className="mt-12 flex flex-row items-start justify-center">
              <View style={{ marginRight: 8 }}>
                <Checkbox
                  value={textChecked}
                  onValueChange={(value) => {
                    setTextChecked(value);
                  }}
                  style={{
                    transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
                  }}
                />
              </View>
              <View className="flex w-[95%] flex-row flex-wrap items-center justify-start">
                <VText variant="B4" color={colors.textCommonSecondary}>
                  {t.top_up.read_and_accept_toc}
                </VText>
                <TouchableOpacity onPress={checkTerms}>
                  <VText variant="B4" color={colors.textBrandDefault}>
                    {t.auth.terms_of_service}
                  </VText>
                </TouchableOpacity>
                <View className="mx-1">
                  <VText variant="B4" color={colors.textCommonSecondary}>
                    {t.settings.coin_rewards.and}
                  </VText>
                </View>
                <TouchableOpacity onPress={checkPrivacy}>
                  <VText variant="B4" color={colors.textBrandDefault}>
                    {t.auth.privacy_policy}
                  </VText>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
        <View className="flex flex-row items-center justify-center" style={{ gap: 16 }}>
          <GitHubAuthButton onLoading={toggleAuthLoading} isCheckText={isCheckText} />
          <AppleAuthButton onLoading={toggleAuthLoading} isCheckText={isCheckText} />
          {isFromChina && (
            <TouchableOpacity
              style={{
                alignItems: 'center',
                borderColor: colors.borderCommonDefault,
                borderWidth: 1,
                borderRadius: 10,
                display: 'flex',
                height: 48,
                width: 48,
                padding: 10,
              }}
              onPress={COMING_SOON}
            >
              <AntDesign name="wechat" size={24} color={colors.rainbowGreen5} />
            </TouchableOpacity>
          )}
        </View>
        {/* <VText variant="B4" color={colors.textCommonSecondary}>
          {t.auth.terms_and_conditions}
        </VText> */}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    margin: 8,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginVertical: 24,
    padding: 24,
  },
  header: {
    gap: 8,
    marginVertical: 8,
  },
  main: {
    gap: 16,
  },
  orContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 8,
  },
});
