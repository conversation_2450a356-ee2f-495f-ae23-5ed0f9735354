import type {
  StoreTemplateCategoryEnum,
  TemplateCardVO,
  TemplateCenterSectionVO,
} from '@bika/types/template/vo';
// import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { Image, ImageBackground } from 'expo-image';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
// import ContextMenu, { ContextMenuAction } from 'react-native-context-menu-view';
import { PreLoading } from '@/components/Loading';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { getBaseUrl } from '@/utils/base';

export default function TemplateScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { error } = useLocalSearchParams();

  const spaceId = useSpaceId();

  const { showSnackbar } = useAction();

  const [templates, setTemplates] = useState<TemplateCenterSectionVO[] | null>(null);

  const [searchResult, setSearchResult] = useState<TemplateCardVO[] | null>(null);

  const [loading, setLoading] = useState<boolean>(false);

  const [currentCategory, setCurrentCategory] = useState<StoreTemplateCategoryEnum>('recommend');

  const fetchTemplates = async (type?: StoreTemplateCategoryEnum) => {
    setLoading(true);

    setCurrentCategory(type ?? 'recommend');

    await trpc.template.fetchTemplatesSections
      .query({
        category: type ?? 'recommend',
      })
      .then((res) => {
        setTemplates(res);
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchTemplates();
  }, [trpc]);

  const onSearch = (text: string) => {
    if (text.length === 0) {
      setSearchResult(null);
    } else if (text) {
      const results = templates?.flatMap((section) =>
        section.templates.filter((template) => {
          return (
            iStringParse(template?.name)?.toLowerCase().includes(text.toLowerCase()) ||
            iStringParse(template?.description)?.toLowerCase().includes(text.toLowerCase())
          );
        }),
      );

      setSearchResult(results ?? null);
    }
  };

  // const contextMenuActions = (Object.entries(TemplateCategoryStringConfig).map(
  //   ([key, value]) => ({
  //     title: iStringParse(value.name),
  //     systemIcon: currentCategory === key && 'checkmark',
  //   }),
  // ) ?? [
  //   {
  //     title: '',
  //   },
  // ]) as ContextMenuAction[];

  useEffect(() => {
    if (error) {
      showSnackbar(`${t.error.error}: ${error}`, ActionType.ERROR);
    }
  }, [error]);

  const renderTemplateItem = ({ item }: { item: TemplateCardVO }) => (
    <View
      style={{
        alignItems: 'center',
        borderRadius: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        minHeight: 54,
        marginVertical: 6,
      }}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
        <Image
          source={{ uri: getBaseUrl() + item.cover }}
          style={{ width: 48, height: 48, borderRadius: 4 }}
        />
        <View style={{ flexShrink: 1, width: '65%' }}>
          <VText variant="B2" color={colors.textCommonPrimary}>
            {iStringParse(item.name)}
          </VText>
          <VText variant="B4" color={colors.textCommonTertiary} numberOfLines={2}>
            {iStringParse(item.description)}
          </VText>
        </View>
      </View>
      <TouchableOpacity
        style={{
          backgroundColor: colors.bgControlsDefault,
          height: 32,
          padding: 6,
          borderRadius: 6,
          width: 48,
        }}
        className="flex items-center justify-center"
        onPress={() =>
          router.push(`/(app)/space/${spaceId}/template/${encodeURIComponent(item.templateId)}`)
        }
      >
        <VText variant="B3" color={colors.textCommonPrimary}>
          {t.action.get}
        </VText>
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          headerSearchBarOptions: {
            placeholder: t.action.search,
            cancelButtonText: t.action.cancel,
            onChangeText(e) {
              onSearch(e.nativeEvent.text);
            },
          },
          // headerRight: () => (
          //   <ContextMenu
          //     actions={contextMenuActions}
          //     dropdownMenuMode={true}
          //     onPress={async (e) =>
          //       fetchTemplates(
          //         Object.keys(TemplateCategoryStringConfig)[
          //           e.nativeEvent.index
          //         ] as StoreTemplateCategoryEnum,
          //       )
          //     }
          //   >
          //     <AntDesign
          //       name="filter"
          //       size={20}
          //       color={colors.textCommonPrimary}
          //     />
          //   </ContextMenu>
          // ),
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, gap: 16 }}
          showsVerticalScrollIndicator={false}
        >
          {!searchResult && (
            <ImageBackground
              source={require('@/assets/images/template/TemplateCenterCover.png')}
              style={{
                minHeight: 120,
                height: 'auto',
                width: '100%',
                justifyContent: 'center',
              }}
            >
              <View style={{ marginHorizontal: 16, gap: 4, flexShrink: 1 }}>
                <VText variant="H4" color={colors.textStaticPrimary}>
                  {t.template.template}
                </VText>
                <VText variant="B2" color={colors.textStaticPrimary}>
                  {t.slogan.slogan_prd_l}
                </VText>
              </View>
            </ImageBackground>
          )}
          {loading || !templates ? (
            <PreLoading />
          ) : (
            <View style={{ marginHorizontal: 16, gap: 10, flexShrink: 1 }}>
              {searchResult ? (
                <>
                  {searchResult.length > 0 ? (
                    <FlashList
                      data={searchResult}
                      estimatedItemSize={54}
                      renderItem={renderTemplateItem}
                    />
                  ) : (
                    <VText variant="B2" color={colors.textCommonPrimary}>
                      {t.space.no_data}
                    </VText>
                  )}
                </>
              ) : (
                templates.map((section, index) => (
                  <View key={index}>
                    <View>
                      <VText variant="H4" color={colors.textCommonPrimary}>
                        {iStringParse(section.name)}
                      </VText>
                      {section.description && (
                        <VText variant="B2" color={colors.textCommonTertiary}>
                          {iStringParse(section.description)}
                        </VText>
                      )}
                    </View>
                    <FlashList
                      data={section.templates}
                      estimatedItemSize={54}
                      renderItem={renderTemplateItem}
                      extraData={locale}
                    />
                  </View>
                ))
              )}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
