import type { StoreTemplateVO } from '@bika/types/template/vo';
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Stack, useGlobalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, TextInput, TouchableOpacity, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { SimpleTag } from '@/components/UI/Tag';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global/provider';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { getBaseUrl } from '@/utils/base';

export default function TemplateInfoComingSoonScreen() {
  const colors = useColor();

  const searchParams = useGlobalSearchParams();

  const router = useRouter();

  const templateId = searchParams.templateId as string;

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { showSnackbar } = useAction();

  const [templateRepo, setTemplateRepo] = useState<StoreTemplateVO | null>(null);

  const [feedback, setFeedback] = useState<string>('');

  const [loading, setLoading] = useState<boolean>(false);

  const getTemplateDetails = async () => {
    setLoading(true);
    await trpc.template.detail
      .query({
        templateId,
      })
      .then((res) => {
        setTemplateRepo(res);
        setLoading(false);
      });
  };

  const onSubmit = async () => {
    await trpc.template.applyWaitingList
      .mutate({
        message: feedback,
        templateId,
      })
      .then(() => {
        showSnackbar(t.template.feedback_thanks, ActionType.SUCCESS);
        router.back();
      });
  };

  useEffect(() => {
    getTemplateDetails();
  }, []);

  if (loading || !templateRepo) {
    return <PreLoading />;
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerLeft: () => {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
              </TouchableOpacity>
            );
          },
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <View
          className="flex flex-row"
          style={{
            paddingHorizontal: 16,
            gap: 12,
          }}
        >
          <Image
            source={{
              uri: `${getBaseUrl()}/${templateRepo.current.data?.cover}`,
            }}
            style={{
              width: 96,
              height: 96,
              borderRadius: 4,
            }}
          />
          <View className="flex shrink flex-col justify-between">
            <View className="flex flex-row items-center gap-1">
              <VText variant="H5" color={colors.textCommonPrimary}>
                {iStringParse(templateRepo.current.data?.name)}
              </VText>
              {templateRepo.verified && (
                <MaterialCommunityIcons
                  name="check-decagram-outline"
                  size={24}
                  color={colors.textBrandDefault}
                />
              )}
              {templateRepo.current.data?.visibility === 'WAITING_LIST' ? (
                <SimpleTag text={t.action.coming_soon} color={colors.textCommonPrimary} />
              ) : (
                <SimpleTag
                  text={`v${templateRepo.current.data?.version}`}
                  color={colors.textCommonPrimary}
                />
              )}
            </View>
          </View>
        </View>
        <View
          style={{
            padding: 16,
            gap: 12,
          }}
        >
          <VText variant="B2" color={colors.textCommonPrimary}>
            {t.action.coming_soon}
          </VText>
          <View>
            <VText variant="B3" color={colors.textCommonPrimary}>
              {t.template.feedback_placeholder}
            </VText>
            <TextInput
              style={{
                backgroundColor: colors.bgControlsDefault,
                color: colors.textCommonPrimary,
                padding: 8,
                borderRadius: 4,
                marginTop: 8,
                minHeight: 54,
                height: 'auto',
              }}
              value={feedback}
              onChangeText={(text) => setFeedback(text)}
              enterKeyHint="done"
            />
          </View>
        </View>
        <TouchableOpacity
          style={{
            position: 'absolute',
            bottom: 30,
            minHeight: 44,
            width: '90%',
            alignSelf: 'center',
            borderRadius: 8,
            backgroundColor: feedback ? colors.bgBrandDefault : colors.bgBrandDisabled,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={onSubmit}
          disabled={!feedback}
        >
          <VText
            variant="B1"
            color={feedback ? colors.textStaticPrimary : colors.textStaticDisabled}
          >
            {t.action.submit}
          </VText>
        </TouchableOpacity>
      </SafeAreaView>
    </>
  );
}
