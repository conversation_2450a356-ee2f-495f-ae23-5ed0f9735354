import { TemplateCategoryStringConfig } from '@bika/contents/config/client';
import type { TemplateRepoRelease } from '@bika/types/template/bo';
import type { StoreTemplateVO } from '@bika/types/template/vo';
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { Stack, useGlobalSearchParams, useRouter } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import { SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import ActionSheet, { type ActionSheetRef } from 'react-native-actions-sheet';
import { PreLoading } from '@/components/Loading';
import { MarkdownRender, RenderNodeResourceIcon } from '@/components/Render';
import { ExpandableTemplateReleaseItem } from '@/components/template/release';
import { FullSeparator, IconSeparator } from '@/components/UI/Separator';
import { CategoryTag, SimpleTag } from '@/components/UI/Tag';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global/provider';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { getBaseUrl } from '@/utils/base';

export default function TemplateInfoScreen() {
  const colors = useColor();

  const router = useRouter();

  const searchParams = useGlobalSearchParams();

  const templateId = searchParams.templateId as string;

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const { showSnackbar } = useAction();

  const [templateRepo, setTemplateRepo] = useState<StoreTemplateVO | null>(null);

  const [templateReleases, setTemplateReleases] = useState<TemplateRepoRelease[]>([]);

  const [currentTab, setCurrentTab] = useState<number>(0);

  const [loading, setLoading] = useState<boolean>(false);

  const getTemplateDetails = async () => {
    await trpc.template.detail
      .query({
        templateId,
      })
      .then((res) => {
        setTemplateRepo(res);
      });
  };

  const getTemplateReleases = async () => {
    await trpc.template.releases
      .query({
        templateId,
      })
      .then((res) => {
        setTemplateReleases(res);
      });
  };

  useEffect(() => {
    getTemplateDetails();

    getTemplateReleases();
  }, []);

  const actionSheetRef = useRef<ActionSheetRef>(null);

  const onGetTemplate = () => {
    if (templateRepo?.current.data?.visibility === 'WAITING_LIST') {
      router.push(`./${templateId}/coming_soon`);
    } else {
      actionSheetRef.current?.show();
    }
  };

  const installTemplate = async () => {
    setLoading(true);

    await trpc.space.installTemplate
      .mutate({
        spaceId,
        templateId,
      })
      .then(() => {
        setLoading(false);
      });
  };

  const starTemplate = async () => {
    await trpc.template.star
      .mutate({
        templateId,
      })
      .then(() => {
        if (!templateRepo?.isStarred) {
          showSnackbar(t.template.star_success, ActionType.SUCCESS);
        } else {
          showSnackbar(t.template.unstar_success, ActionType.SUCCESS);
        }

        getTemplateDetails();
      });
  };

  if (loading || !templateRepo) {
    return <PreLoading />;
  }

  const categoryKey = (key: string) => key as keyof typeof TemplateCategoryStringConfig;
  const categoryName = (key: string) => TemplateCategoryStringConfig[categoryKey(key)]?.name;

  const author = templateRepo.current.data?.author;
  const authorName = author
    ? String(author)
        .match(/^[^<]+/)?.[0]
        ?.trim()
    : null;

  const ActionSheetContext = () => {
    return (
      <ActionSheet
        ref={actionSheetRef}
        containerStyle={{
          backgroundColor: colors.bgCommonDefault,
          padding: 16,
          height: 220,
        }}
      >
        <View style={{ gap: 16 }}>
          <View
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              justifyContent: 'flex-start',
              flexDirection: 'row',
              gap: 12,
            }}
          >
            <Image
              source={{
                uri: `${getBaseUrl()}/${templateRepo.current.data?.cover}`,
              }}
              style={{
                width: 96,
                height: 96,
                borderRadius: 4,
              }}
            />
            <View
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                height: 96,
                width: '70%',
              }}
            >
              <VText variant="H5" color={colors.textCommonPrimary}>
                {iStringParse(templateRepo.current.data?.name)}
              </VText>
              <VText variant="B2" color={colors.textCommonTertiary}>
                v{templateRepo.current.data?.version}
              </VText>
            </View>
          </View>
          <FullSeparator />
          <TouchableOpacity
            style={{
              backgroundColor: colors.bgBrandDefault,
              borderRadius: 4,
              width: '100%',
              height: 42,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={installTemplate}
          >
            <VText variant="B3" color={colors.textStaticPrimary}>
              {t.action.install}
            </VText>
          </TouchableOpacity>
        </View>
      </ActionSheet>
    );
  };

  const RenderTab = () => {
    switch (currentTab) {
      case 2:
        return (
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
              gap: 8,
            }}
          >
            <FlashList
              data={templateReleases}
              scrollEnabled={false}
              estimatedItemSize={54}
              renderItem={({ item }) => <ExpandableTemplateReleaseItem item={item} />}
            />
          </ScrollView>
        );
      case 1:
        return (
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <FlashList
              data={templateRepo.current.data?.resources}
              ItemSeparatorComponent={IconSeparator}
              scrollEnabled={false}
              estimatedItemSize={54}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    minHeight: 56,
                    paddingHorizontal: 16,
                  }}
                >
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 12,
                    }}
                  >
                    <View
                      className="flex items-center justify-center"
                      style={{
                        backgroundColor: colors.bgControlsHover,
                        borderRadius: 4,
                        padding: 4,
                        height: 32,
                        width: 32,
                      }}
                    >
                      <RenderNodeResourceIcon type={item.resourceType} />
                    </View>
                    <View
                      style={{
                        alignSelf: 'center',
                        width: '70%',
                      }}
                    >
                      <VText
                        variant="B2"
                        color={colors.textCommonPrimary}
                        style={{ flexShrink: 1 }}
                      >
                        {iStringParse(item.name)}
                      </VText>
                      {item.description && (
                        <VText variant="B4" color={colors.textCommonTertiary} numberOfLines={1}>
                          {iStringParse(item.description)}
                        </VText>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              )}
            />
          </ScrollView>
        );
      case 0:
      default:
        return (
          <View>
            {templateRepo?.readme && iStringParse(templateRepo?.readme).length > 0 ? (
              <MarkdownRender content={iStringParse(templateRepo?.readme)} />
            ) : (
              <VText variant="H6" color={colors.textCommonPrimary}>
                {t.template.no_readme}
              </VText>
            )}
          </View>
        );
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: iStringParse(templateRepo.current.data?.name),
          headerTintColor: colors.textCommonPrimary,
          headerBackground: () => (
            <View
              style={{
                backgroundColor: colors.bgTemplateNav,
                opacity: 1,
                flex: 1,
              }}
            />
          ),
        }}
      />
      <LinearGradient
        colors={[colors.bgTemplateGradient, colors.bgCommonDefault]}
        start={{ x: 0, y: 0.2 }}
        end={{ x: 0, y: 0.5 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={{ paddingHorizontal: 16, flex: 1, gap: 8 }}>
            <View className="flex flex-row" style={{ gap: 12 }}>
              <Image
                source={{
                  uri: `${getBaseUrl()}/${templateRepo.current.data?.cover}`,
                }}
                style={{
                  width: 96,
                  height: 96,
                  borderRadius: 4,
                }}
              />
              <View className="flex shrink flex-col justify-between">
                <View className="flex flex-row items-center gap-1">
                  <VText variant="H5" color={colors.textCommonPrimary}>
                    {iStringParse(templateRepo.current.data?.name)}
                  </VText>
                  {templateRepo.verified && (
                    <MaterialCommunityIcons
                      name="check-decagram-outline"
                      size={24}
                      color={colors.textBrandDefault}
                    />
                  )}
                  {templateRepo.current.data?.visibility === 'WAITING_LIST' ? (
                    <SimpleTag text={t.action.coming_soon} color={colors.textCommonPrimary} />
                  ) : (
                    <SimpleTag
                      text={`v${templateRepo.current.data?.version}`}
                      color={colors.textCommonPrimary}
                    />
                  )}
                </View>
                <VText variant="B2" color={colors.textCommonSecondary} numberOfLines={2}>
                  {iStringParse(templateRepo.current.data?.description)}
                </VText>
                <View className="flex w-full flex-row items-center gap-2">
                  <TouchableOpacity
                    style={{
                      backgroundColor: colors.bgBrandDefault,
                      borderRadius: 4,
                      width: '50%',
                      height: 32,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onPress={onGetTemplate}
                  >
                    <VText variant="B3" color={colors.textStaticPrimary}>
                      {t.action.get}
                    </VText>
                  </TouchableOpacity>
                  <ActionSheetContext />
                  <TouchableOpacity
                    style={{
                      backgroundColor: colors.bgControlsDefault,
                      borderRadius: 4,
                      width: '50%',
                      height: 32,
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      gap: 1,
                    }}
                    onPress={starTemplate}
                  >
                    <AntDesign
                      name={templateRepo?.isStarred ? 'star' : 'staro'}
                      size={12}
                      color={
                        templateRepo?.isStarred ? colors.rainbowOrange5 : colors.textCommonPrimary
                      }
                    />
                    <VText
                      variant="B3"
                      color={
                        templateRepo?.isStarred ? colors.rainbowOrange5 : colors.textCommonPrimary
                      }
                    >
                      {`${t.template.favorite}(${templateRepo?.stars})`}
                    </VText>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View style={{ gap: 8, borderRadius: 8, flex: 1 }}>
              <View className="flex flex-row items-center gap-2">
                <Image
                  source={require('@/assets/images/icon.png')}
                  style={{
                    width: 20,
                    height: 20,
                    borderRadius: 9999,
                  }}
                />
                <VText variant="B2" color={colors.textCommonSecondary}>
                  {authorName}
                </VText>
              </View>
              <View className="flex flex-row flex-wrap gap-1">
                {typeof templateRepo?.category === 'string' ? (
                  <CategoryTag
                    text={iStringParse(categoryName(templateRepo?.category))}
                    color={colors.textCommonPrimary}
                  />
                ) : (
                  templateRepo?.category.map((category, idx) => (
                    <CategoryTag
                      key={`category-${idx}-${category}`}
                      text={iStringParse(categoryName(category))}
                      color={colors.textCommonPrimary}
                    />
                  ))
                )}
              </View>
              <SegmentedControl
                values={[
                  t.template.readme,
                  t.resource.included_resources,
                  // t.template.architecture,
                  t.template.releases_history,
                ]}
                selectedIndex={currentTab}
                onChange={(event) => setCurrentTab(event.nativeEvent.selectedSegmentIndex)}
              />
              <ScrollView showsVerticalScrollIndicator={false}>
                <RenderTab />
              </ScrollView>
            </View>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </>
  );
}
