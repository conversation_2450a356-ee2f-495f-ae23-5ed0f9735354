import type { TodoVO } from '@bika/types/mission/vo';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView } from 'react-native';
import FabGroup from '@/components/Button/FabGroup';
import { NOT_SUPPORTED_COMPONENT } from '@/components/NOT_SUPPORTED';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function MyAgendaScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [todos, setTodos] = useState<TodoVO[] | null>(null);

  useEffect(() => {
    const getTodos = async () => {
      await trpc.my.todos
        .query({
          spaceId: spaceId as string,
        })
        .then((res) => {
          setTodos(res);
        });
    };

    getTodos();
  }, []);

  console.log('1111 agenda', todos);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1, padding: 16 }}
      >
        {/* <FlashList
          ListEmptyComponent={
            <VText variant="H6" color={colors.textCommonPrimary}>
              {t.ag.no_reminder_so_far}
            </VText>
          }
          data={todos}
          estimatedItemSize={200}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={{
                backgroundColor: colors.bgControlsDefault,
                marginVertical: 8,
                padding: 16,
                borderRadius: 8,
              }}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {item.name}
              </VText>
              {item.description && (
                <VText variant="B3" color={colors.textCommonTertiary}>
                  {item.description}
                </VText>
              )}
            </TouchableOpacity>
          )}
        /> */}
        <NOT_SUPPORTED_COMPONENT />
      </ScrollView>
      <FabGroup />
    </SafeAreaView>
  );
}
