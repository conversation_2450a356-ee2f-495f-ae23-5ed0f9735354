/* eslint-disable no-nested-ternary */
import type { TodoVO } from '@bika/types/mission/vo';
import type { MemberVO } from '@bika/types/unit/vo';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import { FlashList } from '@shopify/flash-list';
import { format } from 'date-fns';
import { useNavigation, useRouter } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import FabGroup from '@/components/Button/FabGroup';
import { PreLoading } from '@/components/Loading';
import { AvatarImage } from '@/components/UI/Avatar';
import { StatusTag } from '@/components/UI/Tag';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function MyTodosScreen() {
  const router = useRouter();

  const colors = useColor();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [todos, setTodos] = useState<
    Record<'recent' | 'unfinished' | 'myCreated' | 'finished', TodoVO[] | null>
  >({
    recent: null,
    unfinished: null,
    myCreated: null,
    finished: null,
  });

  const [selectedIndex, setSelectedIndex] = useState(0);

  const [loading, setLoading] = useState(true);

  const [refreshing, setRefreshing] = useState(false);

  const fetchTodos = useCallback(async () => {
    setLoading(true);

    try {
      const results = await Promise.allSettled<TodoVO[]>([
        trpc.my.todos.query({ spaceId, queryType: 'ALL' }),
        trpc.my.todos.query({ spaceId, queryType: 'PENDING' }),
        trpc.my.todos.query({ spaceId, queryType: 'MY_CREATED' }),
        trpc.my.todos.query({ spaceId, queryType: 'COMPLETED' }),
      ]);

      setTodos({
        recent: results[0].status === 'fulfilled' ? results[0].value : [],
        unfinished: results[1].status === 'fulfilled' ? results[1].value : [],
        myCreated: results[2].status === 'fulfilled' ? results[2].value : [],
        finished: results[3].status === 'fulfilled' ? results[3].value : [],
      });
    } finally {
      setLoading(false);
    }
  }, [spaceId, trpc.my.todos]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTodos().then(() => setRefreshing(false));
  }, [fetchTodos]);

  useEffect(() => {
    fetchTodos();
  }, [focused]);

  const todoData =
    selectedIndex === 0
      ? todos.recent
      : selectedIndex === 1
        ? todos.unfinished
        : selectedIndex === 2
          ? todos.myCreated
          : todos.finished;

  return (
    <SafeAreaView style={{ flex: 1, gap: 8 }}>
      <SegmentedControl
        values={[
          `${t.todo.recent} (${todos.recent?.length ?? 0})`,
          `${t.todo.pending} (${todos.unfinished?.length ?? 0})`,
          `${t.todo.my_created} (${todos.myCreated?.length ?? 0})`,
          `${t.todo.finished} (${todos.finished?.length ?? 0})`,
        ]}
        selectedIndex={selectedIndex}
        onChange={(event) => setSelectedIndex(event.nativeEvent.selectedSegmentIndex)}
        style={{ marginHorizontal: 16 }}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        contentContainerStyle={{
          flexGrow: 1,
          marginHorizontal: 16,
        }}
      >
        {loading && !todoData ? (
          <PreLoading />
        ) : (
          <FlashList
            ListEmptyComponent={
              <View className="flex items-center justify-center">
                <VText variant="H4" color={colors.textCommonPrimary}>
                  {t.todo.no_todo_so_far}
                </VText>
              </View>
            }
            data={todoData}
            estimatedItemSize={200}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={{
                  backgroundColor: colors.bgControlsDefault,
                  marginVertical: 4,
                  padding: 16,
                  borderRadius: 8,
                }}
                className="gap-2"
                onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/mission/${item.id}`)}
              >
                <View className="flex w-10/12 flex-row items-center gap-1">
                  <View>{item.status && <StatusTag status={item.status} />}</View>
                  <View>
                    <VText variant="H6" color={colors.textCommonPrimary} numberOfLines={1}>
                      {item.name}
                    </VText>
                  </View>
                </View>
                {item.description && (
                  <VText variant="B3" color={colors.textCommonTertiary} numberOfLines={5}>
                    {item.description}
                  </VText>
                )}
                <View className="flex flex-row items-center" style={{ gap: 16 }}>
                  <VText variant="B3" color={colors.textCommonTertiary}>
                    {format(new Date(item.createAt as string), 'yyyy-MM-dd')}
                  </VText>
                  {item?.assignees?.map((assignee) => (
                    <View key={assignee.id} className="flex flex-row items-center">
                      <AvatarImage
                        size={24}
                        content={(assignee as MemberVO).avatar}
                        alt={assignee.name}
                        withBorder
                      />
                    </View>
                  ))}
                </View>
              </TouchableOpacity>
            )}
          />
        )}
      </ScrollView>
      <FabGroup />
    </SafeAreaView>
  );
}
