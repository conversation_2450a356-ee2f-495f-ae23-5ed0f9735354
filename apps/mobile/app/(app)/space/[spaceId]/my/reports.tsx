import type { ReportVO } from '@bika/types/report/vo';
import { AntDesign } from '@expo/vector-icons';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import { FlashList } from '@shopify/flash-list';
import { useNavigation, useRouter } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import FabGroup from '@/components/Button/FabGroup';
import { PreLoading } from '@/components/Loading';
import { HTMLRender } from '@/components/Render';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { i18n } from '@/hooks/usei18n';

export default function MyReportsScreen() {
  const router = useRouter();

  const colors = useColor();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [readReports, setReadReports] = useState<ReportVO[] | null>(null);
  const [unreadReports, setUnreadReports] = useState<ReportVO[] | null>(null);

  const [selectedIndex, setSelectedIndex] = useState(0);

  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const getReadReports = async () => {
    setLoading(true);
    await trpc.my.reports
      .query({
        spaceId,
        read: true,
      })
      .then((res) => {
        setReadReports(res);
        setLoading(false);
      });
  };

  const getUnreadReports = async () => {
    setLoading(true);
    await trpc.my.reports
      .query({
        spaceId,
        read: false,
      })
      .then((res) => {
        setUnreadReports(res);
        setLoading(false);
      });
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);

    getUnreadReports().then(() => {
      getReadReports().then(() => {
        setRefreshing(false);
      });
    });
  }, []);

  useEffect(() => {
    if (focused) {
      getUnreadReports();

      getReadReports();
    }
  }, [focused]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          paddingHorizontal: 16,
          flex: 1,
          gap: 10,
        }}
      >
        <SegmentedControl
          values={[
            `${t.report.unread} (${unreadReports?.length ?? 0})`,
            `${t.report.read} (${readReports?.length ?? 0})`,
          ]}
          selectedIndex={selectedIndex}
          onChange={(event) => {
            setSelectedIndex(event.nativeEvent.selectedSegmentIndex);
          }}
        />
        <ScrollView
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {loading && (!unreadReports || !readReports) ? (
            <PreLoading />
          ) : (
            <FlashList
              ListEmptyComponent={
                <View className="flex items-center justify-center">
                  <VText variant="H4" color={colors.textCommonPrimary}>
                    {t.report.no_report_so_far}
                  </VText>
                </View>
              }
              data={selectedIndex === 0 ? unreadReports : readReports}
              estimatedItemSize={200}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    backgroundColor: colors.bgControlsDefault,
                    padding: 16,
                    marginBottom: 10,
                    borderRadius: 8,
                    gap: 6,
                  }}
                  onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/report/${item.id}`)}
                >
                  <View className="flex flex-row items-center justify-between">
                    <View className="flex flex-row items-center gap-1">
                      <AntDesign name="filetext1" size={16} color={colors.textCommonPrimary} />
                      <VText variant="H7" color={colors.textCommonPrimary}>
                        {item.subject}
                      </VText>
                    </View>
                    {item?.createdAt && (
                      <VText variant="B4" color={colors.textCommonPrimary}>
                        {new Date(item?.createdAt).toLocaleString(i18n.locale)}
                      </VText>
                    )}
                  </View>
                  {!!item.bodyClip && (
                    <View className="ml-6">
                      <HTMLRender content={item.bodyClip} />
                    </View>
                  )}
                </TouchableOpacity>
              )}
            />
          )}
        </ScrollView>
      </View>
      <FabGroup />
    </SafeAreaView>
  );
}
