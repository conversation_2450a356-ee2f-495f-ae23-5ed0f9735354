import type { SpaceRender<PERSON> } from '@bika/types/space/vo';
import type { MemberVO, TeamSubListPageVO, TeamVO } from '@bika/types/unit/vo';
import { AntDesign, Entypo } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import Checkbox from 'expo-checkbox';
import { Stack, useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  Alert,
  type NativeSyntheticEvent,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import ContextMenu, { type ContextMenuOnPressNativeEvent } from 'react-native-context-menu-view';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import SweetSFSymbol from 'sweet-sfsymbols';
import { PreLoading } from '@/components/Loading';
import { AvatarImage } from '@/components/UI/Avatar';
import { Header<PERSON>utton } from '@/components/UI/Button';
import { IconSeparator, SelectSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { usePermission, useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { rgbaToHex } from '@/utils/color';

export default function TeamAndMembersScreen() {
  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const permission = usePermission();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { tm, mode, withRole, memberId, parentTeamName } = useLocalSearchParams() as {
    tm: string[];
    mode: string;
    withRole: string;
    memberId: string;
    parentTeamName: string;
  };

  console.log('tm', tm);

  const spaceId = useSpaceId();

  const [currentSpace, setCurrentSpace] = useState<SpaceRenderVO | null>(null);

  const [teamList, setTeamList] = useState<TeamSubListPageVO | null>(null);

  const [loading, setLoading] = useState<boolean>(true);

  const [onEdit, setOnEdit] = useState<boolean>(false);

  const [selectedTeam, setSelectedTeam] = useState<TeamVO | null>(null);

  const generateBreadcrumbTitle = () => {
    if (!currentSpace) return t.space.teams;

    const parentTeamNameList = String(parentTeamName).split('/').slice(1);

    return currentSpace.name + parentTeamNameList ? parentTeamNameList.join(' / ') : '';
  };

  const onContextMenuPress = (e: NativeSyntheticEvent<ContextMenuOnPressNativeEvent>) => {
    switch (e.nativeEvent.index) {
      case 0:
        setOnEdit(!onEdit);
        break;
      case 1:
        Alert.prompt(
          t.team.create_team,
          '',
          (teamName) => {
            createTeam(teamName);
          },
          'plain-text',
        );
        break;
      case 2:
        Alert.prompt(
          t.team.edit_team,
          '',
          (teamName) => {
            updateTeam(tm[tm.length - 1], teamName);
          },
          'plain-text',
        );
        break;
      case 3:
        deleteTeamPrompt(tm[tm.length - 1], 1);
        break;
      default:
        break;
    }
  };

  const getSpaceById = async () => {
    setLoading(true);
    await trpc.space.info
      .query({
        identify: spaceId,
      })
      .then((res) => {
        setCurrentSpace(res);
        setLoading(false);
      });
  };

  const getUnits = async () => {
    setLoading(true);

    await trpc.team.subList
      .query({
        spaceId,
        teamId: tm[tm.length - 1],
      })
      .then((res) => {
        setTeamList(res);
        setLoading(false);
      });
  };

  const createTeam = async (teamName: string) => {
    await trpc.team.create
      .mutate({
        spaceId,
        name: teamName,
        parentTeamId: tm[tm.length - 1],
      })
      .then(() => {
        getUnits();
      });
  };

  const updateTeam = async (teamId: string, teamName: string) => {
    await trpc.team.update
      .mutate({
        spaceId,
        id: teamId,
        name: teamName,
      })
      .then(() => {
        getUnits();
      });
  };

  const deleteTeam = async (teamId: string, type: number) => {
    await trpc.team.delete
      .mutate({
        spaceId,
        id: teamId,
      })
      .then(() => {
        if (type === 1) {
          router.back();
        } else {
          getUnits();
        }
      });
  };

  const deleteTeamPrompt = async (teamId: string, type: number) => {
    Alert.alert(t.team.delete_team, t.team.delete_team_description, [
      {
        text: t.action.cancel,
        style: 'cancel',
      },
      {
        text: t.action.confirm,
        style: 'destructive',
        onPress: () => deleteTeam(teamId, type),
      },
    ]);
  };

  const addMemberToTeam = async () => {
    if (!selectedTeam) return;

    await trpc.team.addMembers
      .mutate({
        spaceId,
        id: selectedTeam?.id,
        memberIds: [memberId],
      })
      .then(() => {
        router.back();
      });
  };

  useEffect(() => {
    if (focused) {
      getSpaceById();

      getUnits();
    }
  }, [focused]);

  if (loading && !teamList) {
    return <PreLoading />;
  }

  const HeaderRightButton = () => {
    switch (mode) {
      case 'select':
        return (
          <HeaderButton
            onPress={() =>
              router.replace({
                pathname: `/(app)/space/${spaceId}/(modal)/account/invite`,
                params: {
                  withTeam: JSON.stringify(selectedTeam as TeamVO),
                  withRole,
                },
              })
            }
            title={t.action.confirm}
            disabled={!selectedTeam}
          />
        );
      case 'add':
        return (
          ((teamList && teamList.data.filter((unit) => unit.type === 'Member')) ||
            selectedTeam) && (
            <HeaderButton
              onPress={() => addMemberToTeam()}
              title={t.action.add}
              disabled={!selectedTeam || !memberId}
            />
          )
        );
      default:
        return (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: 20,
            }}
          >
            <TouchableOpacity
              onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/account/invite`)}
            >
              <SweetSFSymbol
                name="person.crop.circle.badge.plus"
                colors={[rgbaToHex(colors.textCommonPrimary)]}
                size={20}
              />
            </TouchableOpacity>
            {permission.updateMember && (
              <ContextMenu
                actions={[
                  { systemIcon: 'plus', title: t.action.edit },
                  {
                    systemIcon: 'plus',
                    title: t.team.create_team,
                  },
                  {
                    disabled: tm.length === 1,
                    systemIcon: 'plus',
                    title: t.team.edit_team,
                  },
                  {
                    disabled: tm.length === 1,
                    systemIcon: 'trash',
                    title: t.action.delete,
                    destructive: true,
                  },
                ]}
                dropdownMenuMode={true}
                onPress={(e) => onContextMenuPress(e)}
              >
                <Entypo name="dots-three-horizontal" size={20} color={colors.textCommonPrimary} />
              </ContextMenu>
            )}
          </View>
        );
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t.space.members_and_teams,
          headerTransparent: true,
          headerRight: HeaderRightButton,
        }}
      />
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaView style={{ flex: 1, marginBottom: 50 }}>
          <VContext title={generateBreadcrumbTitle()} titleIndent>
            {teamList && teamList.data.length > 0 ? (
              <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  flexGrow: 1,
                  backgroundColor: colors.bgControlsDefault,
                  borderRadius: 10,
                  paddingVertical: 4,
                  marginVertical: 4,
                  marginHorizontal: 16,
                }}
              >
                <FlashList
                  // data={
                  //   mode === 'select' || mode === 'add'
                  //     ? teamList?.filter((unit) => !(unit as MemberVO).email)
                  //     : teamList
                  // }
                  data={teamList.data}
                  renderItem={({ item }) => (
                    <Swipeable
                      enabled={item.type === 'Team'}
                      renderRightActions={() => (
                        <TouchableOpacity
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                          onPress={() => {
                            if (item.type === 'Team') {
                              deleteTeamPrompt((item as TeamVO).id, 0);
                            }
                          }}
                        >
                          <VText variant="B1" color={colors.textCommonPrimary}>
                            {t.action.delete}
                          </VText>
                        </TouchableOpacity>
                      )}
                    >
                      <TouchableOpacity
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          height: 54,
                          marginHorizontal: 8,
                          paddingHorizontal: 8,
                        }}
                        onPress={() => {
                          if (item.type === 'Team') {
                            const path = [...new Set([...tm, item.id])].join('/');

                            const parentTeamNameList = String(parentTeamName).split('/');

                            const newParentTeamName = [
                              ...new Set([...parentTeamNameList, item.name]),
                            ].join('/');

                            router.push({
                              pathname: `/(app)/space/${spaceId}/(modal)/team-and-members/${path}`,
                              params: {
                                mode,
                                withRole,
                                memberId,
                                parentTeamName: newParentTeamName,
                              },
                            });
                          } else if (item.type === 'Member') {
                            router.push(`/(app)/space/${spaceId}/(modal)/member/${item.id}`);
                          }
                        }}
                      >
                        <View
                          style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 10,
                          }}
                        >
                          <Checkbox
                            value={selectedTeam?.id === item.id}
                            onValueChange={() => {
                              if (selectedTeam?.id === item.id) {
                                setSelectedTeam(null);
                              } else {
                                setSelectedTeam(item as TeamVO);
                              }
                            }}
                            color={colors.bgBrandDefault}
                            style={{
                              borderWidth: 1.5,
                              borderRadius: 9999,
                              display: mode === 'select' || mode === 'add' ? 'flex' : 'none',
                            }}
                          />
                          {item.type === 'Team' ? (
                            <View
                              style={{
                                backgroundColor: '#907FF033',
                                borderRadius: 5,
                                padding: 8,
                                width: 32,
                                height: 32,
                              }}
                            >
                              <AntDesign name="team" size={16} color={colors.textBrandDefault} />
                            </View>
                          ) : (
                            <AvatarImage
                              content={(item as MemberVO).avatar}
                              size={32}
                              alt={(item as MemberVO).name}
                            />
                          )}
                          <View
                            style={{
                              flexDirection: 'column',
                            }}
                          >
                            <VText variant="B1" color={colors.textCommonPrimary}>
                              {item.name}
                            </VText>
                            {item.type === 'Member' && (
                              <VText variant="B2" color={colors.textCommonTertiary}>
                                {(item as MemberVO)?.email}
                              </VText>
                            )}
                          </View>
                        </View>
                        {item.type === 'Team' && (
                          <View className="flex flex-row items-center gap-2">
                            <VText variant="B1" color={colors.textCommonTertiary}>
                              {item.memberCount}
                            </VText>
                            <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                          </View>
                        )}
                      </TouchableOpacity>
                    </Swipeable>
                  )}
                  ItemSeparatorComponent={mode === 'select' ? SelectSeparator : IconSeparator}
                  scrollEnabled={false}
                  estimatedItemSize={54}
                />
              </ScrollView>
            ) : (
              <View className="flex items-center justify-center" style={{ height: '100%' }}>
                <VText variant="H6" color={colors.textCommonPrimary}>
                  {t.space.no_data}
                </VText>
              </View>
            )}
          </VContext>
          {onEdit && (
            <View
              style={{
                position: 'absolute',
                bottom: 0,
                backgroundColor: colors.bgControlsDefault,
                width: '100%',
                height: 60,
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  paddingVertical: 8,
                  paddingHorizontal: 20,
                }}
              >
                <VText variant="B1" color={colors.textBrandDefault}>
                  {t.action.select_all}
                </VText>
              </View>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  paddingVertical: 8,
                  paddingHorizontal: 20,
                }}
              >
                <VText variant="B1" color={colors.textDangerDefault}>
                  {t.team.remove_index_members}
                </VText>
              </View>
            </View>
          )}
        </SafeAreaView>
      </GestureHandlerRootView>
    </>
  );
}
