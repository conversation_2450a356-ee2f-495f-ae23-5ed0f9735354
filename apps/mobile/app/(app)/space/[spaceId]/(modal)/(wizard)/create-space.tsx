import type { AIIntentUIResolveAIO } from '@bika/types/ai/bo';
import type { AIWizardVO } from '@bika/types/ai/vo';
import { useGlobalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';
import { IntentUIRender } from '@/components/IntentUI/Render';
import { AvatarImage } from '@/components/UI/Avatar';
import { SystemMessage } from '@/components/UI/Message';
import { useGlobalContext } from '@/context/global';
import { iStringParse } from '@/hooks/usei18n';

export default function CreateSpaceScreen() {
  const globalParams = useGlobalSearchParams();

  const router = useRouter();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { spaceId } = globalParams;

  console.log('spaceId', spaceId);

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [wizard, setWizard] = useState<AIWizardVO | null>(null);

  const [humanMessage, setHumanMessage] = useState<string>('');
  const [aiMessage, setAiMessage] = useState<string>('');

  const setDialogAndMessage = (dialog: AIWizardVO) => {
    setWizard(dialog);
    setAiMessage(iStringParse(dialog.lastAiMessage!.text));

    if (dialog.lastHumanMessage) setHumanMessage(iStringParse(dialog.lastHumanMessage.text));

    setIsLoading(false);
  };

  useEffect(() => {
    const getWizard = async () => {
      const newWizard = await trpc.ai.newWizard.mutate({
        spaceId: spaceId as string,
        intent: {
          type: 'STEP_WIZARD',
          stepWizardType: 'CREATE_SPACE',
        },
      });

      setWizard(newWizard);
    };

    getWizard();
  }, []);

  console.log('createSpaceWizard', wizard);

  const sendUI = async (uiResolve: AIIntentUIResolveAIO) => {
    setAiMessage('');

    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId: wizard!.id,
      resolve: {
        type: 'UI',
        uiResolve,
      },
    });

    setDialogAndMessage(refreshDialog);
    setAiMessage(iStringParse(refreshDialog.lastAiMessage!.text));
  };

  const sendMessage = async (humanSay: string) => {
    setHumanMessage(humanSay);
    setAiMessage('');

    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId: wizard!.id,
      resolve: {
        type: 'MESSAGE',
        message: humanSay,
      },
    });

    setDialogAndMessage(refreshDialog);
    setAiMessage(iStringParse(refreshDialog.lastAiMessage!.text));

    if (refreshDialog.resolutionStatus === 'NOT_STARTED') {
      sendMessage(t.action.ok);
    }
  };

  const onClose = () => {
    const isPresented = router.canGoBack();

    if (isPresented) {
      router.back();
    } else {
      router.push(`/(app)/space/${spaceId}/(home)`);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView style={styles.container}>
        <View
          style={{
            alignItems: 'flex-start',
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'row',
            gap: 8,
            width: '100%',
            alignSelf: 'center',
          }}
        >
          <AvatarImage size={40} local localUrl={require('@/assets/images/icon.png')} alt="AI" />
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              width: '85%',
            }}
          >
            <SystemMessage message={iStringParse(wizard?.lastAiMessage?.text)} />
            <IntentUIRender
              wizard={wizard}
              sendUI={sendUI}
              sendMessage={sendMessage}
              close={onClose}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 10,
  },
});
