import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import type { AIWizardVO } from '@bika/types/ai/vo';
import { useGlobalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { IntentUIRender } from '@/components/IntentUI/Render';
import { AvatarImage } from '@/components/UI/Avatar';
import { SystemMessage } from '@/components/UI/Message';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';

export default function OnboardingScreen() {
  const globalParams = useGlobalSearchParams();

  const router = useRouter();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { spaceId } = globalParams;

  console.log('spaceId', spaceId);

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isDone, setIsDone] = useState<boolean>(false);

  const [onboardingWizard, setOnboardingWizard] = useState<AIWizardVO | null>(null);

  const colors = useColor();
  const [humanMessage, setHumanMessage] = useState<string>('');
  const [aiMessage, setAiMessage] = useState<string>('');

  const setDialogAndMessage = (dialog: AIWizardVO) => {
    const lastMessage = dialog.messages[dialog.messages.length - 1];
    setOnboardingWizard(dialog);
    setAiMessage(iStringParse(lastMessage.text));

    if (lastMessage.role === 'user') {
      setHumanMessage(iStringParse(lastMessage.text));
    }

    setIsLoading(false);
  };

  useEffect(() => {
    const getWizard = async () => {
      const newOnboardingInitWizard = await trpc.ai.newWizard.mutate({
        spaceId: spaceId as string,
        intent: {
          type: 'STEP_WIZARD',
          stepWizardType: 'ONBOARDING_INIT',
        },
      });

      const newOnboardingTrialWizard = await trpc.ai.newWizard.mutate({
        spaceId: spaceId as string,
        intent: {
          type: 'STEP_WIZARD',
          stepWizardType: 'ONBOARDING_TRIAL',
          step: 1,
        },
      });

      setOnboardingWizard((newOnboardingInitWizard || newOnboardingTrialWizard) as AIWizardVO);
    };

    getWizard();
  }, []);

  console.log('onboardingWizard', onboardingWizard);

  const sendUI = async (uiResolve: AIIntentUIResolveDTO) => {
    setAiMessage('');

    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId: onboardingWizard!.id,
      resolve: {
        type: 'UI',
        uiResolve,
      },
    });

    const lastMessage = refreshDialog.messages[refreshDialog.messages.length - 1];

    if (lastMessage.final) {
      setIsDone(true);
    }

    setDialogAndMessage(refreshDialog as AIWizardVO);
    setAiMessage(iStringParse(refreshDialog.messages[refreshDialog.messages.length - 1].text));
  };

  const sendMessage = async (humanSay: string) => {
    setHumanMessage(humanSay);
    setAiMessage('');

    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId: onboardingWizard!.id,
      resolve: {
        type: 'MESSAGE',
        message: humanSay,
      },
    });

    setDialogAndMessage(refreshDialog as AIWizardVO);
    setAiMessage(iStringParse(refreshDialog.messages[refreshDialog.messages.length - 1].text));

    if (refreshDialog.resolutionStatus === 'NOT_STARTED') {
      sendMessage(t.action.ok);
    }
  };

  const onClose = () => {
    const isPresented = router.canGoBack();

    if (isPresented) {
      router.back();
    } else {
      router.push(`/(app)/space/${spaceId}/(home)`);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView
        style={{
          flex: 1,
          margin: 10,
        }}
      >
        <View
          style={{
            alignItems: 'flex-start',
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'row',
            gap: 8,
            width: '100%',
            alignSelf: 'center',
          }}
        >
          <AvatarImage size={40} local localUrl={require('@/assets/images/icon.png')} alt="AI" />
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              width: '85%',
            }}
          >
            <SystemMessage
              message={iStringParse(
                onboardingWizard?.messages?.[(onboardingWizard?.messages || []).length - 1]?.text,
              )}
            />
            <IntentUIRender
              wizard={onboardingWizard}
              sendUI={sendUI}
              sendMessage={sendMessage}
              close={onClose}
            />
            {isDone && (
              <TouchableOpacity
                onPress={onClose}
                style={{
                  alignItems: 'center',
                  backgroundColor: colors.textBrandDefault,
                  borderRadius: 5,
                  display: 'flex',
                  flexDirection: 'row',
                  height: 40,
                  justifyContent: 'center',
                  padding: 10,
                  width: '100%',
                }}
              >
                <VText variant="H6" color={colors.textStaticPrimary}>
                  Onboarding completed
                </VText>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
