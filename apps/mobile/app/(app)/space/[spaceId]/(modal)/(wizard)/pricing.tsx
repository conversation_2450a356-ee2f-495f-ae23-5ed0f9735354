import { Pricing } from '@bika/contents/config/mobile';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Checkbox from 'expo-checkbox';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Linking,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { GradientTag } from '@/components/UI/Tag';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

export default function PricingScreen() {
  const colors = useColor();

  const router = useRouter();

  const { locale } = useGlobalContext();
  const { t } = locale;

  const [selectedPlanCategoryType, setSelectedPlanCategoryType] =
    useState<Pricing.PricingPlanCategoryType>('Individual');
  const [planInterval, setPlanInterval] = useState<Pricing.PricingPlanInterval>('Month');

  const findInitialPlanIndex = (categoryType: Pricing.PricingPlanCategoryType) => {
    const category = Pricing.PricingPlan.find((p) => p.type === categoryType);
    return category?.plans.findIndex((p) => p.type === 'Free') ?? 0;
  };

  const [checkedPlanIndex, setCheckedPlanIndex] = useState(findInitialPlanIndex('Individual'));

  const currentPlanType =
    Pricing.PricingPlan.find((p) => p.type === selectedPlanCategoryType)?.plans ?? [];
  const currentPlan = currentPlanType[checkedPlanIndex] || {};

  const handlePlanCategoryTypeChange = (planCategoryType: Pricing.PricingPlanCategoryType) => {
    setSelectedPlanCategoryType(planCategoryType);
    setCheckedPlanIndex(findInitialPlanIndex(planCategoryType));
  };

  const handleBenefitPress = async () => {
    const url = getBaseUrl();
    if (await Linking.canOpenURL(url)) Linking.openURL(url);
    else console.error(`Can't handle url: ${url}`);
  };

  function getButtonText(plan: Pricing.PricingPlanItem) {
    switch (plan.name) {
      case 'Free':
        return 'Get Started';
      case 'Business':
        return 'Contact Us';
      default:
        return 'Subscribe';
    }
  }

  const buttonText = getButtonText(currentPlan);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.mainView}>
        <View style={styles.contentContainer}>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              gap: 10,
            }}
          >
            <Image source={require('@/assets/images/icon.png')} style={styles.iconImage} />
            <VText variant="H4" color={colors.textCommonPrimary}>
              {t.pricing.change_your_plan}
            </VText>
          </View>
          <View style={{ gap: 10 }}>
            <View
              style={{
                alignSelf: 'center',
                borderColor: colors.borderCommonDefault,
                borderRadius: 20,
                borderWidth: 1,
                display: 'flex',
                flexDirection: 'row',
                height: 40,
                justifyContent: 'space-between',
                padding: 5,
                width: '50%',
              }}
            >
              <TouchableOpacity
                onPress={() => setPlanInterval('Month')}
                style={[
                  styles.planIntervalView,
                  {
                    backgroundColor: planInterval === 'Month' ? colors.bgTagDefault : 'transparent',
                  },
                ]}
              >
                <VText variant="B3" color={colors.textCommonPrimary}>
                  {t.pricing.month}
                </VText>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setPlanInterval('Year')}
                style={[
                  styles.planIntervalView,
                  {
                    backgroundColor: planInterval === 'Year' ? colors.bgTagDefault : 'transparent',
                  },
                ]}
              >
                <VText variant="B3" color={colors.textCommonPrimary}>
                  {t.pricing.year}
                </VText>
              </TouchableOpacity>
            </View>
            <View style={styles.planTypeContainer}>
              {Pricing.PricingPlan.map((type) => {
                return (
                  <TouchableWithoutFeedback
                    key={type.type}
                    onPress={() => handlePlanCategoryTypeChange(type.type)}
                  >
                    <View
                      style={[
                        styles.typeView,
                        {
                          borderColor:
                            selectedPlanCategoryType === type.type
                              ? colors.textBrandDefault
                              : colors.borderCommonDefault,
                        },
                      ]}
                    >
                      <VText
                        variant="B2"
                        color={
                          selectedPlanCategoryType === type.type
                            ? colors.textBrandDefault
                            : colors.borderCommonDefault
                        }
                      >
                        {type.type}
                      </VText>
                    </View>
                  </TouchableWithoutFeedback>
                );
              })}
            </View>
          </View>
          <ScrollView style={styles.scrollView}>
            <View style={styles.scrollViewContainer}>
              {currentPlanType.map((plan, index) => {
                const globalIndex =
                  Pricing.PricingPlan.findIndex((p) => p.type === selectedPlanCategoryType) *
                    currentPlanType.length +
                  index;

                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      {
                        alignSelf: 'center',
                        backgroundColor: colors.bgCommonHigh,
                        borderRadius: 15,
                        borderWidth: 1,
                        gap: 6,
                        padding: 16,
                        width: '90%',
                      },
                      {
                        borderColor:
                          checkedPlanIndex === globalIndex
                            ? colors.textBrandDefault
                            : colors.borderCommonDefault,
                      },
                    ]}
                    onPress={() => {
                      setCheckedPlanIndex(globalIndex);
                    }}
                  >
                    <View style={styles.planTitle}>
                      <View style={styles.planTitleLeft}>
                        <VText variant="H6" color={colors.textCommonPrimary}>
                          {plan.name}
                        </VText>
                        {plan.popular && (
                          <GradientTag colors={['#00EFC1', '#8EB3F9', '#ED6160']}>
                            {t.pricing.popular}
                          </GradientTag>
                        )}
                      </View>
                      <Checkbox
                        style={styles.checkbox}
                        color={
                          checkedPlanIndex === globalIndex ? colors.textBrandDefault : undefined
                        }
                        value={checkedPlanIndex === globalIndex}
                        onValueChange={() => setCheckedPlanIndex(globalIndex)}
                      />
                    </View>
                    <VText variant="B2" color={colors.textCommonTertiary}>
                      {plan.description}
                    </VText>
                    <View>
                      {plan.name !== 'Free' &&
                      plan.name !== Pricing.EnterprisePricingPlanType.Business ? (
                        <View style={styles.priceView}>
                          <VText variant="H4" color={colors.textCommonPrimary}>
                            ${plan.interval[planInterval.toLowerCase() as 'month' | 'year'].price}
                          </VText>
                          <VText variant="B2" color={colors.textCommonPrimary}>
                            / {planInterval.toLowerCase()} / {t.pricing.user}
                          </VText>
                        </View>
                      ) : (
                        <VText variant="H4" color={colors.textCommonPrimary}>
                          {plan.type}
                        </VText>
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </ScrollView>
        </View>
        <View
          style={{
            alignSelf: 'center',
            bottom: 0,
            gap: 15,
            height: 100,
            paddingTop: 20,
            position: 'absolute',
            width: '90%',
          }}
        >
          <View style={styles.footerTextView}>
            <VText variant="B2" color={colors.textCommonTertiary}>
              计划每年自动续订，直至取消。
            </VText>
            <Pressable onPress={handleBenefitPress}>
              <VText
                variant="B2"
                color={colors.textCommonTertiary}
                style={{
                  textDecorationLine: 'underline',
                }}
              >
                {t.pricing.view_benefit_details}
              </VText>
            </Pressable>
          </View>
          <TouchableOpacity
            style={{
              backgroundColor: colors.textBrandDefault,
              borderRadius: 15,
              paddingVertical: 16,
            }}
            onPress={async () => {
              await AsyncStorage.setItem('pricing', 'true').then(() => router.back());
            }}
          >
            <VText
              variant="H6"
              color={colors.textStaticPrimary}
              style={{
                textAlign: 'center',
              }}
            >
              {buttonText}
            </VText>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  checkbox: {
    borderRadius: 10,
    height: 15,
    width: 15,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    gap: 30,
  },
  footerTextView: {
    display: 'flex',
    flexDirection: 'row',
    gap: 5,
  },
  iconImage: {
    alignSelf: 'center',
    borderRadius: 12,
    height: 48,
    width: 48,
  },
  mainView: {
    display: 'flex',
    height: '100%',
    justifyContent: 'space-between',
  },
  planIntervalView: {
    alignItems: 'center',
    borderRadius: 20,
    display: 'flex',
    justifyContent: 'center',
    padding: 5,
    width: '50%',
  },
  planTitle: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  planTitleLeft: {
    alignContent: 'center',
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'center',
  },
  planTypeContainer: {
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '90%',
  },
  priceView: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    gap: 5,
  },
  scrollView: {
    height: '100%',
  },
  scrollViewContainer: {
    gap: 20,
  },
  typeView: {
    alignItems: 'center',
    borderBottomWidth: 2,
    display: 'flex',
    justifyContent: 'center',
    paddingVertical: 10,
    width: '50%',
  },
});
