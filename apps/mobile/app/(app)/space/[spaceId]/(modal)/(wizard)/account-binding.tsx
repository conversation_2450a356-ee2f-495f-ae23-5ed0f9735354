import type { AIWizardVO } from '@bika/types/ai/vo';
import { useGlobalSearchParams } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';
import { BindingAccount } from '@/components/IntentUI/BindingAccount';
import { AvatarImage } from '@/components/UI/Avatar';
import { SystemMessage } from '@/components/UI/Message';
import { useGlobalContext } from '@/context/global';

export default function AccountBindingScreen() {
  const globalParams = useGlobalSearchParams();

  const { trpc, locale } = useGlobalContext();
  const { t } = locale;

  const { spaceId } = globalParams;

  console.log('spaceId', spaceId);

  const [accountBindingWizard, setAccountBindingWizard] = useState<AIWizardVO | null>(null);

  useEffect(() => {
    const getWizard = async () => {
      const newWizard = await trpc.ai.newWizard.mutate({
        spaceId: spaceId as string,
        intent: {
          type: 'SEARCH',
        },
      });

      setAccountBindingWizard(newWizard);
    };

    getWizard();
  }, []);

  console.log('account binding wizard', accountBindingWizard);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView style={styles.container}>
        <View
          style={{
            alignItems: 'flex-start',
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'row',
            gap: 8,
            width: '100%',
            alignSelf: 'center',
          }}
        >
          <AvatarImage size={40} local localUrl={require('@/assets/images/icon.png')} alt="AI" />
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              width: '85%',
            }}
          >
            <SystemMessage message={t.account.account_binding_description} />
            <BindingAccount />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 10,
  },
});
