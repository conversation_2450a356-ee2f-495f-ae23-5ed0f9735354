import { AntDesign } from '@expo/vector-icons';
import { StackActions } from '@react-navigation/native';
import { Stack, useNavigation, useRouter } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { EditorProvider } from '@/context/record/record-editor-provider';
import { useColor } from '@/hooks/useColor';

export default function ModalLayout() {
  const navigation = useNavigation();
  const router = useRouter();
  console.log('导航状态:', navigation.getState());
  const { locale } = useGlobalContext();

  const { t } = locale;

  const colors = useColor();

  return (
    <EditorProvider>
      <Stack
        screenOptions={{
          headerBackTitle: t.action.back,
          headerTintColor: colors.textBrandDefault,
          headerTransparent: true,
        }}
      >
        <Stack.Screen
          name="settings"
          options={{
            title: t.user.settings,
            headerLargeTitle: true,
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.pop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="mission/[missionId]"
          options={{
            title: '',
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.pop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="report/[reportId]"
          options={{
            headerLargeTitle: false,
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.pop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="record/detail"
          options={{
            title: t.record.record_detail,
          }}
        />
        <Stack.Screen
          name="record/update"
          options={{
            title: t.record.modify_record,
          }}
        />
        <Stack.Screen
          name="record/comment"
          options={{
            title: t.record.record_comment,
          }}
        />
        <Stack.Screen
          name="record/create"
          options={{
            title: t.record.create_record,
          }}
        />
        <Stack.Screen
          name="space/audit"
          options={{
            title: t.settings.space.space_audits,
          }}
        />
        <Stack.Screen
          name="space/settings"
          options={{
            title: t.space.space_settings,
          }}
        />
        <Stack.Screen
          name="member/[memberId]"
          options={{
            title: '',
            headerTransparent: true,
          }}
        />
        <Stack.Screen
          name="space/integration/index"
          options={{
            title: t.settings.space.third_party_integration,
          }}
        />
        <Stack.Screen
          name="space/integration/smtp"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/integration/x"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/integration/wecom"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/integration/dingtalk"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/integration/telegram"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/integration/lark"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/integration/slack"
          options={{
            title: '',
          }}
        />
        <Stack.Screen
          name="space/avatar"
          options={{
            title: t.avatar.avatar,
          }}
        />
        <Stack.Screen
          name="team-and-members/[...tm]"
          options={{
            title: t.space.members_and_teams,
          }}
        />
        <Stack.Screen
          name="role/[...r]"
          options={{
            title: t.space.role,
          }}
        />
        <Stack.Screen
          name="create-anything"
          options={{
            title: t.record.create,
          }}
        />
        <Stack.Screen
          name="magic"
          options={{
            title: t.brand.brand,
          }}
        />
        <Stack.Screen name="launcher" />
        <Stack.Screen
          name="notification/index"
          options={{
            title: t.notification.notification,
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.popToTop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="notification/settings"
          options={{
            title: t.notification.notification_settings,
          }}
        />
        <Stack.Screen
          name="(wizard)/account-binding"
          options={{
            title: t.account.account_binding,
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="(wizard)/create-space"
          options={{
            title: t.wizard.create_space,
          }}
        />
        <Stack.Screen
          name="account/invite"
          options={{
            title: t.invite.invite_members,
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.popToTop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="(wizard)/onboarding"
          options={{
            title: t.wizard.onboarding,
            // 开启 modal 模式
            presentation: 'modal',
            // 禁用下滑页面关闭页面
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name="(wizard)/pricing"
          options={{
            title: '',
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.popToTop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="user/settings"
          options={{
            title: t.user.settings,
          }}
        />
        <Stack.Screen
          name="user/avatar"
          options={{
            title: t.user.avatar,
          }}
        />
        <Stack.Screen
          name="user/email"
          options={{
            title: t.user.email,
          }}
        />
        <Stack.Screen
          name="user/debug"
          options={{
            title: t.user.debug,
            headerLargeTitle: true,
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.pop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="account/api"
          options={{
            title: t.settings.account.api,
          }}
        />
        <Stack.Screen
          name="account/info"
          options={{
            title: t.account.account_info,
            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.pop())}>
                  <AntDesign name="close" size={20} color={colors.textCommonPrimary} />
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="account/referral"
          options={{
            title: t.settings.account.referral,
          }}
        />
        <Stack.Screen
          name="account/top-up"
          options={{
            title: t.settings.account.top_up,
          }}
        />
        <Stack.Screen
          name="about"
          options={{
            title: t.settings.about.about_brand,
          }}
        />
        <Stack.Screen
          name="account/login_record"
          options={{
            title: t.settings.account.session_logs,
          }}
        />
        <Stack.Screen
          name="account/binding"
          options={{
            title: t.settings.account.connected_account,
          }}
        />
        <Stack.Screen
          name="node/view-list"
          options={{
            title: t.resource.toggle_view,
            presentation: 'modal',

            headerLeft: () => {
              return (
                <TouchableOpacity onPress={() => navigation.dispatch(StackActions.pop())}>
                  <VText variant="H6">{t.action.cancel}</VText>
                </TouchableOpacity>
              );
            },
          }}
        />
        <Stack.Screen
          name="node/cell-editor"
          options={{
            title: t.resource.edit_field,
            presentation: 'modal',

            // headerLeft: () => {
            //   return (
            //     <TouchableOpacity onPress={() => router.back()}>
            //       <VText variant="H6">取消</VText>
            //     </TouchableOpacity>
            //   );
            // },
          }}
        />
      </Stack>
    </EditorProvider>
  );
}
