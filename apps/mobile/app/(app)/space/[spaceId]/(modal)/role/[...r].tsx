import type { <PERSON><PERSON>, RoleVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import Checkbox from 'expo-checkbox';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { AvatarImage } from '@/components/UI/Avatar';
import { HeaderButton } from '@/components/UI/Button';
import { IconSeparator, SelectSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { usePermission, useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function RoleScreen() {
  const router = useRouter();

  const colors = useColor();

  const permission = usePermission();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { r, mode, withTeam, memberId } = useLocalSearchParams() as {
    r: string[];
    mode: string;
    withTeam: string;
    memberId: string;
  };

  const spaceId = useSpaceId();

  const [roleList, setRoleList] = useState<RoleVO[]>([]);

  const [roleMembers, setRoleMembers] = useState<MemberVO[]>([]);

  const [selectedRoles, setSelectedRoles] = useState<RoleVO[]>([]);

  const [loading, setLoading] = useState<boolean>(true);

  const getTeamsAndMembers = async () => {
    setLoading(true);
    await trpc.role.members
      .query({
        spaceId,
        id: r[r.length - 1],
      })
      .then((res) => {
        setRoleMembers(res);
        setLoading(false);
      });
  };

  const getRoleList = async () => {
    setLoading(true);
    await trpc.role.list
      .query({
        spaceId,
      })
      .then((res) => {
        setRoleList(res.data);
        setLoading(false);
      });
  };

  const addMemberToRole = async () => {
    if (!selectedRoles) return;

    await trpc.member.update
      .mutate({
        spaceId,
        id: memberId,
        data: {
          roleIds: selectedRoles.map((role) => role.id),
        },
      })
      .then(() => {
        router.back();
      });
  };

  useEffect(() => {
    if (r[r.length - 1] !== 'index') {
      getTeamsAndMembers();
    } else {
      getRoleList();
    }
  }, []);

  if (loading && !roleList) {
    return <PreLoading />;
  }

  const HeaderRightButton = () => {
    switch (mode) {
      case 'select':
        return (
          <HeaderButton
            onPress={() =>
              router.replace({
                pathname: `/(app)/space/${spaceId}/(modal)/account/invite`,
                params: {
                  withRole: JSON.stringify(selectedRoles as RoleVO[]),
                  withTeam,
                },
              })
            }
            title={t.action.confirm}
            disabled={!selectedRoles}
          />
        );
      case 'add':
        return (
          (roleList.length > 0 || selectedRoles) && (
            <HeaderButton
              onPress={() => addMemberToRole()}
              title={t.action.add}
              disabled={!memberId}
            />
          )
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t.space.role,
          headerTransparent: true,
          headerRight: HeaderRightButton,
        }}
      />
      <SafeAreaView style={{ flex: 1, marginBottom: 50 }}>
        <VContext title={t.space.role} titleIndent>
          <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1 }}>
            {r[r.length - 1] !== 'index' ? (
              <FlashList
                ListEmptyComponent={
                  <View className="flex items-center justify-center" style={{ height: '100%' }}>
                    <VText variant="H6" color={colors.textCommonPrimary}>
                      {t.settings.role.member_no_role_tips}
                    </VText>
                  </View>
                }
                data={roleMembers}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 16,
                      minHeight: 54,
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginHorizontal: 16,
                    }}
                    disabled={permission.updateRole}
                    onPress={() => {
                      router.push(`/(app)/space/${spaceId}/(modal)/member/${item.id}`);
                    }}
                  >
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 16,
                      }}
                    >
                      <AvatarImage size={32} content={item.avatar} alt={item.name} withBorder />
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignContent: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {item.name}
                        </VText>
                        <VText variant="B2" color={colors.textCommonTertiary}>
                          {item.email}
                        </VText>
                      </View>
                    </View>
                    {permission.updateRole && (
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 4,
                        }}
                      >
                        <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                      </View>
                    )}
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id}
                ItemSeparatorComponent={IconSeparator}
                scrollEnabled={false}
                estimatedItemSize={54}
              />
            ) : (
              <FlashList
                ListEmptyComponent={
                  <View className="flex items-center justify-center" style={{ height: '100%' }}>
                    <VText variant="H6" color={colors.textCommonPrimary}>
                      {t.space.no_data}
                    </VText>
                  </View>
                }
                data={roleList}
                extraData={selectedRoles}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 16,
                      height: 54,
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginHorizontal: 16,
                    }}
                    disabled={!permission.readRole}
                    onPress={() => {
                      if (mode === 'select') {
                        setSelectedRoles([
                          ...(selectedRoles ?? []),
                          {
                            id: item.id,
                            name: item.name,
                            type: item.type,
                            manageSpace: item.manageSpace,
                          },
                        ]);
                      } else if (mode === 'add') {
                        return null;
                      } else {
                        const path = [...new Set([...r, item.id])].join('/');

                        router.push(`/(app)/space/${spaceId}/(modal)/role/${path}`);
                      }
                    }}
                  >
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 10,
                      }}
                    >
                      <Checkbox
                        value={!!selectedRoles?.find((role) => role.id === item.id)}
                        onValueChange={() => {
                          const existingRoleIndex = selectedRoles?.findIndex(
                            (role) => role.id === item.id,
                          );

                          if (
                            existingRoleIndex !== -1 &&
                            existingRoleIndex !== undefined &&
                            selectedRoles !== null &&
                            selectedRoles?.length !== 0
                          ) {
                            setSelectedRoles(selectedRoles.filter((role) => role.id !== item.id));
                          } else {
                            setSelectedRoles([
                              ...(selectedRoles ?? []),
                              {
                                id: item.id,
                                name: item.name,
                                type: item.type,
                                manageSpace: item.manageSpace,
                              },
                            ]);
                          }
                        }}
                        color={colors.bgBrandDefault}
                        style={{
                          borderWidth: 1.5,
                          borderRadius: 9999,
                          display: mode === 'select' || mode === 'add' ? 'flex' : 'none',
                        }}
                      />
                      <View
                        style={{
                          backgroundColor: '#FFB75233',
                          borderRadius: 5,
                          padding: 8,
                          borderWidth: 1,
                          borderColor: colors.borderCommonDefault,
                        }}
                      >
                        <AntDesign name="team" size={16} color="#FFB752" />
                      </View>
                      <VText variant="B1" color={colors.textCommonPrimary}>
                        {item.name}
                      </VText>
                    </View>
                    <View className="flex flex-row items-center gap-2">
                      <VText variant="B1" color={colors.textCommonTertiary}>
                        {item?.memberCount}
                      </VText>
                      {permission.readRole && (
                        <>
                          {mode !== 'select' && mode !== 'add' && (
                            <View>
                              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                            </View>
                          )}
                        </>
                      )}
                    </View>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id}
                ItemSeparatorComponent={mode === 'select' ? SelectSeparator : IconSeparator}
                scrollEnabled={false}
                estimatedItemSize={54}
              />
            )}
          </ScrollView>
        </VContext>
      </SafeAreaView>
    </>
  );
}
