import type {
  DatabaseFieldWithId,
  DatabaseMultiSelectField,
  DatabaseSingleSelectField,
} from '@bika/types/database/bo';
import { useLocalSearchParams } from 'expo-router';
import { SafeAreaView, Text, View } from 'react-native';
import { match } from 'ts-pattern';
import { SelectFieldCellEditor } from '@/components/cell-editor/select-field-cell-editor';

type ISelectField = {
  field: string;
  value: string;
  // setValue: (value: T) => void;
};

type IProps = ISelectField;

export default function CellEditor() {
  const { field: fieldStr, value: valueStr } = useLocalSearchParams<ISelectField>();

  const field = JSON.parse(fieldStr as string) as DatabaseFieldWithId;
  const value = JSON.parse(valueStr as string);
  const fieldType = field.type;

  return (
    <SafeAreaView>
      <View>
        {match(fieldType)
          .with('SINGLE_SELECT', () => {
            return (
              <SelectFieldCellEditor field={field as DatabaseSingleSelectField} value={value} />
            );
          })
          .with('MULTI_SELECT', () => {
            return (
              <SelectFieldCellEditor field={field as DatabaseMultiSelectField} value={value} />
            );
          })
          .otherwise(() => {
            return <Text>OTHER</Text>;
          })}
      </View>
    </SafeAreaView>
  );
}
