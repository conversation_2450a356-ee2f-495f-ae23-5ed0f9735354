import { useApiCaller } from '@bika/api-caller';
import type { ViewType } from '@bika/types/database/bo';
import type { ViewVO } from '@bika/types/database/vo';
import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { IconSeparator } from '@/components/UI/Separator';
import { ItemSkeleton } from '@/components/UI/Skeleton';
import { VText } from '@/components/UI/Text';
import { ViewIconMap } from '@/constants/view';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';

interface IProps {
  view: ViewVO;
}

export default function ViewListScreen() {
  const { trpc, locale } = useGlobalContext();
  const { trpcQuery } = useApiCaller();
  const router = useRouter();
  const colors = useColor();
  const searchParams = useLocalSearchParams<{
    databaseId: string;
    viewId: string;
    spaceId: string;
  }>();

  const { data } = trpcQuery.database.info.useQuery({
    databaseId: searchParams.databaseId,
  });

  const views = data?.views;

  const isTableView = (viewType: ViewType) => {
    return viewType === 'TABLE';
  };

  return (
    <SafeAreaView>
      <ScrollView>
        <View className="p-4">
          <View className="rounded" style={{ backgroundColor: colors.bgControlsDefault }}>
            <FlashList
              data={views}
              ListEmptyComponent={<ItemSkeleton />}
              renderItem={({ item }: { item: ViewVO }) => {
                const Button = isTableView(item.type) ? TouchableOpacity : TouchableWithoutFeedback;

                const Icon = ViewIconMap[item.type];
                return (
                  <Button
                    onPress={() => {
                      if (!isTableView(item.type)) {
                        return;
                      }
                      router.back();
                      router.setParams({
                        viewId: item.id,
                      });
                    }}
                  >
                    <View className="flex h-[44px] flex-row items-center px-4 py-3">
                      <View className="mr-3 flex items-center justify-center">
                        <Icon
                          fill={
                            isTableView(item.type)
                              ? colors.textCommonPrimary
                              : colors.textCommonDisabled
                          }
                        />
                      </View>
                      <View className="flex-1">
                        <VText
                          variant="B1"
                          color={
                            isTableView(item.type)
                              ? colors.textCommonPrimary
                              : colors.textCommonDisabled
                          }
                        >
                          {item.name}
                        </VText>
                      </View>
                      {searchParams.viewId === item.id && (
                        <View>
                          <AntDesign name="check" size={16} color={colors.textBrandDefault} />
                        </View>
                      )}
                    </View>
                  </Button>
                );
              }}
              keyExtractor={(item) => item.id}
              ItemSeparatorComponent={IconSeparator}
              scrollEnabled={false}
              estimatedItemSize={54}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
