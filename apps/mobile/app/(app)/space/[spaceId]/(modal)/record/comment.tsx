import type { RecordCommentVO } from '@bika/types/database/vo';
import { FlashList } from '@shopify/flash-list';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import ContextMenu from 'react-native-context-menu-view';
import SweetSFSymbol from 'sweet-sfsymbols';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { AvatarImage } from '@/components/UI/Avatar';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { rgbaToHex } from '@/utils/color';

export default function RecordCommentScreen() {
  const { databaseId, recordId } = useLocalSearchParams();

  const spaceId = useSpaceId();

  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const [comments, setComments] = useState<RecordCommentVO[] | null>(null);

  const [inputText, setInputText] = useState('');

  const getComments = async () => {
    await trpc.database.getComments
      .query({
        databaseId: databaseId as string,
        recordId: recordId as string,
      })
      .then((data) => {
        setComments(data.data);
      });
  };

  const addComment = async () => {
    if (!inputText) return;

    await trpc.database.addComment
      .mutate({
        databaseId: databaseId as string,
        recordId: recordId as string,
        content: inputText,
      })
      .then(() => {
        setInputText('');
        getComments();
        Keyboard.dismiss();
      });
  };

  useEffect(() => {
    getComments();
  }, []);

  // console.log('comments', comments);

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <View className="flex flex-row">
              {Platform.OS === 'ios' && (
                <ContextMenu
                  actions={[
                    {
                      title: 'All',
                    },
                    {
                      title: 'Comment Only',
                    },
                    {
                      title: 'Mention Only',
                    },
                  ]}
                  dropdownMenuMode={true}
                  onPress={(e) => COMING_SOON()}
                >
                  <SweetSFSymbol
                    name="line.3.horizontal.decrease.circle"
                    colors={[rgbaToHex(colors.textCommonPrimary)]}
                    size={20}
                  />
                </ContextMenu>
              )}
            </View>
          ),
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}
        >
          <ScrollView style={{ flexGrow: 1 }}>
            <View style={{ marginHorizontal: 16 }}>
              <FlashList
                data={comments}
                renderItem={({ item }) => (
                  <View style={{ paddingVertical: 10, gap: 10 }}>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: 8,
                        alignItems: 'center',
                      }}
                    >
                      <AvatarImage
                        size={32}
                        content={item.user.avatar}
                        alt={item.member ? item.member.name : item.user.name}
                      />
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 4,
                        }}
                      >
                        <VText variant="B1" color={colors.textCommonSecondary}>
                          {item.member ? item.member.name : item.user.name} {t.action.commented}
                        </VText>
                        <VText variant="B1" color={colors.textCommonTertiary}>
                          {new Date(item.createAt).toLocaleString()}
                        </VText>
                      </View>
                    </View>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 4,
                        borderRadius: 4,
                        paddingHorizontal: 16,
                        paddingVertical: 8,
                        backgroundColor: colors.bgControlsDefault,
                      }}
                    >
                      <VText variant="B1" color={colors.textCommonSecondary}>
                        {item.content}
                      </VText>
                    </View>
                  </View>
                )}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                estimatedItemSize={54}
              />
            </View>
          </ScrollView>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}
          >
            <View
              className="flex w-full flex-row items-center justify-between"
              style={{
                borderTopWidth: 1,
                borderTopColor: colors.borderCommonDefault,
                padding: 16,
              }}
            >
              <TextInput
                style={{
                  borderRadius: 4,
                  width: '90%',
                  color: colors.textCommonPrimary,
                }}
                value={inputText}
                onChangeText={(text) => setInputText(text)}
                returnKeyType="send"
                placeholder="Type a message..."
              />
              <TouchableOpacity onPress={addComment}>
                <SweetSFSymbol
                  name="paperplane"
                  size={16}
                  colors={[rgbaToHex(colors.textCommonPrimary)]}
                />
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
}
