/* eslint-disable */

import type { AttachmentVO } from '@bika/types/attachment/vo';
import {
  type DatabaseField,
  type DatabaseFieldConfigSelectOption,
  DatabaseSingleSelectField,
} from '@bika/types/database/bo';
import type {
  DatabaseVO,
  RecordCreateVO,
  RecordDetailVO,
  RecordUpdateVO,
} from '@bika/types/database/vo';
import type { MemberVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import { useCameraPermissions } from 'expo-camera';
import Checkbox from 'expo-checkbox';
import * as DocumentPicker from 'expo-document-picker';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import {
  KeyboardAvoidingView,
  Pressable,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import ContextMenu from 'react-native-context-menu-view';
import FileViewer from 'react-native-file-viewer';
import SweetSFSymbol from 'sweet-sfsymbols';
import { StaticLoading } from '@/components/Loading';
import { AvatarImage } from '@/components/UI/Avatar';
import { DateTimePicker } from '@/components/UI/DateTimePicker';
import { VInput } from '@/components/UI/Input';
import { FullSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { imagePickerConfig } from '@/hooks/useImagePicker';
import { iStringParse } from '@/hooks/usei18n';
import { rgbaToHex } from '@/utils/color';
import { uploadAttachment } from '@/utils/file';

export default function CreateRecordScreen() {
  const router = useRouter();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { showSnackbar } = useAction();

  const { databaseId, viewId } = useLocalSearchParams();

  const { trpc, locale } = useGlobalContext();

  const [loading, setLoading] = useState<boolean>(false);

  const { t } = locale;

  const [recordDetail, setRecordDetail] = useState<RecordDetailVO | null>(null);

  const [error, setError] = useState<Error | null>(null);

  const [formData, setFormData] = useState<RecordCreateVO | null>(null);

  const getNodeResource = async () => {
    await trpc.node.detail
      .query({
        id: databaseId as string,
      })
      .then((res) => {
        const { views } = res.resource as DatabaseVO;

        // const view = views.find((v) => v.id === viewId)!;
        const view = views[0];

        setRecordDetail({
          fields: view.columns,
          revision: 0,
        });
      });
  };

  const onCreate = async () => {
    setLoading(true);
    try {
      await trpc.database.createRecord
        .mutate({
          databaseId: databaseId as string,
          ...formData!,
        })
        .then((res) => {
          setLoading(false);
          if (res.id) {
            showSnackbar(t.record.create_record_success, ActionType.SUCCESS);
            router.back();
          } else {
            throw new Error(t.record.create_record_failed);
          }
        });
    } catch (err: any) {
      setError(err);
      setLoading(false);
      showSnackbar(t.record.create_record_failed, ActionType.ERROR);
      router.back();
    }
  };

  const handleFieldChange = (fieldId: string, value: string | string[]) => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      cells: {
        ...prevFormData?.cells,
        [fieldId]: value,
      },
    }));
  };

  useEffect(() => {
    getNodeResource();
  }, []);

  console.log(error);

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 20,
              }}
            >
              <TouchableOpacity onPress={onCreate}>
                <SweetSFSymbol
                  name="checkmark"
                  colors={[rgbaToHex(colors.textCommonPrimary)]}
                  size={20}
                />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <SafeAreaView style={{ flex: 1 }}>
          <View style={{ paddingHorizontal: 10, flex: 1 }}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <View style={{ marginVertical: 10 }}>
                {/* <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.record.create_record}: {recordId as string}
                </VText> */}
                {error && (
                  <VText variant="B2" color={colors.textDangerDefault}>
                    {error.message}
                  </VText>
                )}
              </View>
              <RenderRecordForm
                formData={formData}
                recordDetail={recordDetail}
                onFieldChange={handleFieldChange}
              />
              <View style={{ height: 200 }} />
            </ScrollView>
          </View>
        </SafeAreaView>
      </KeyboardAvoidingView>
      <StaticLoading loading={loading} />
    </>
  );
}

const RenderRecordForm = ({
  formData,
  recordDetail,
  onFieldChange,
}: {
  formData: RecordCreateVO | RecordUpdateVO | null;
  recordDetail: RecordDetailVO | null;
  onFieldChange: (fieldId: string, value: string | string[]) => void;
}) => {
  return (
    <View
      style={{
        gap: 10,
      }}
    >
      {recordDetail?.fields.map((field) => {
        return (
          <RenderRecordFormType
            key={field.id}
            formData={formData}
            field={field}
            onFieldChange={onFieldChange}
          />
        );
      })}
    </View>
  );
};

const RenderRecordFormType = ({
  formData,
  field,
  onFieldChange,
}: {
  formData: RecordCreateVO | RecordUpdateVO | null;
  field: DatabaseField;
  onFieldChange: (fieldId: string, value: string | string[]) => void;
}) => {
  const spaceId = useSpaceId();

  const colors = useColor();

  const { trpc, locale, auth } = useGlobalContext();

  const { t } = locale;

  const { me } = auth;

  const [members, setMembers] = useState<MemberVO[] | null>(null);

  const [permission, requestPermission] = useCameraPermissions();

  const [photos, setPhotos] = useState<ImagePicker.ImagePickerResult[]>([]);

  const [document, setDocument] = useState<DocumentPicker.DocumentPickerResult[]>([]);

  const [uploadResponse, setUploadResponse] = useState<AttachmentVO[] | null>(null);

  if (!field) return;

  const [uploadWaiting, setUploadWaiting] = useState<boolean>(false);

  const convertedSingleSelectOptions = useMemo(() => {
    if (field.type !== 'SINGLE_SELECT') return [];
    return field?.property?.options?.map((option: DatabaseFieldConfigSelectOption) => ({
      title: option?.name,
    }));
  }, [field]);

  const convertedMemberOptions = members?.map((member) => ({
    title: member.name,
  }));

  const getMembers = async () => {
    await trpc.member.list
      .query({
        spaceId,
      })
      .then((res) => {
        setMembers(res.data);
      });
  };

  const takePhotoOrRecordVideo = async () => {
    if (!permission?.granted) requestPermission();
    const result = await ImagePicker.launchCameraAsync(imagePickerConfig);
    if (!result.canceled) setPhotos((prevPhotos) => [...prevPhotos, result]);

    const theResponse = await onUploadAttachment(result);

    console.log('theResponse', theResponse);

    if (!theResponse) return;

    onFieldChange(field.id!, JSON.stringify(theResponse));
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync(imagePickerConfig);
    if (!result.canceled) setPhotos((prevPhotos) => [...prevPhotos, result]);

    const theResponse = await onUploadAttachment(result);

    console.log('theResponse', theResponse);

    if (!theResponse) return;

    onFieldChange(field.id!, JSON.stringify(theResponse));
  };

  const pickDocument = async () => {
    const result = await DocumentPicker.getDocumentAsync();

    if (!result.canceled) {
      setDocument((prevDocument) => [...prevDocument, result]);
    }

    const theResponse = await onUploadAttachment(result);

    console.log('theResponse', theResponse);

    if (!theResponse) return;

    onFieldChange(field.id!, JSON.stringify(theResponse!));
  };

  const onUploadAttachment = async (
    file: ImagePicker.ImagePickerResult | DocumentPicker.DocumentPickerResult,
  ) => {
    console.log('start upload process');

    let finalResponse: AttachmentVO[] = [];

    finalResponse = uploadResponse ?? [];

    setUploadWaiting(true);

    if (!file) return;

    const asset = file.assets?.[0];

    if (!asset) return;

    const fileExt = asset?.uri.split('.').pop();

    const isImage = (
      asset: ImagePicker.ImagePickerAsset | DocumentPicker.DocumentPickerAsset,
    ): asset is ImagePicker.ImagePickerAsset => {
      return 'fileSize' in asset;
    };

    const size = isImage(asset) ? asset.fileSize : asset.size;

    const { path, presignedPutUrl } = await trpc.attachment.getPresignedPut.mutate({
      // fileExt: `.${fileExt}`,
      contentType: asset.mimeType,
      size: size ?? 0,
    });

    const uploadAttachmentResponse = await uploadAttachment(
      presignedPutUrl,
      asset?.uri,
      fileExt as string,
      asset?.mimeType ?? 'image/png',
      size ?? 0,
    );

    if (!uploadAttachmentResponse.ok) {
      console.error('error when uploading attachment');
    }

    await trpc.attachment.create
      .mutate({
        path,
        prefix: 'database',
      })
      .then((response) => {
        finalResponse = [...finalResponse, response];

        setUploadResponse((prevUploadResponse) =>
          prevUploadResponse ? [...prevUploadResponse, response] : [response],
        );

        console.log('Attachment created:', response);
        setUploadWaiting(false);
      });

    return finalResponse;
  };

  useEffect(() => {
    getMembers();
  }, []);

  switch (field.type) {
    case 'ATTACHMENT':
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 8,
              padding: 10,
              minHeight: 50,
              justifyContent: 'center',
              gap: 10,
            }}
          >
            <ContextMenu
              actions={[
                {
                  systemIcon: 'camera',
                  title: t.record.take_photo_or_record_video,
                },
                {
                  systemIcon: 'photo',
                  title: t.record.select_from_gallery,
                },
                {
                  systemIcon: 'doc',
                  title: t.record.select_from_files,
                },
              ]}
              dropdownMenuMode={true}
              onPress={(e) => {
                if (e.nativeEvent.index === 0) {
                  takePhotoOrRecordVideo();
                } else if (e.nativeEvent.index === 1) {
                  pickImage();
                } else if (e.nativeEvent.index === 2) {
                  pickDocument();
                }
              }}
            >
              <VText variant="B2" color={colors.textBrandDefault}>
                {t.record.add_attachment}
              </VText>
            </ContextMenu>
            {photos.length > 0 ||
              (document.length > 0 && (
                <View
                  style={{
                    flexDirection: 'column',
                    gap: 10,
                  }}
                >
                  <View>
                    {photos.map((photo) => (
                      <Pressable
                        onPress={async () => {
                          await FileViewer.open(photo.assets?.[0].uri as any);
                        }}
                        key={photo?.assets?.[0].uri}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 10,
                        }}
                      >
                        <Image
                          source={{ uri: photo?.assets?.[0].uri }}
                          style={{ width: 36, height: 36 }}
                        />
                        <VText variant="B2" color={colors.textCommonSecondary}>
                          {photo?.assets?.[0].fileName ?? 'Image'}
                        </VText>
                      </Pressable>
                    ))}
                  </View>
                  {photos.length > 0 && <FullSeparator />}
                  <View>
                    {document.map((doc) => (
                      <Pressable
                        onPress={async () => {
                          await FileViewer.open(doc.assets?.[0].uri as any);
                        }}
                        key={doc?.assets?.[0].uri}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 10,
                        }}
                      >
                        <Image
                          source={{ uri: doc?.assets?.[0].uri }}
                          style={{ width: 36, height: 36 }}
                        />
                        <VText variant="B2" color={colors.textCommonSecondary}>
                          {doc?.assets?.[0].name}
                        </VText>
                      </Pressable>
                    ))}
                  </View>
                </View>
              ))}
          </View>
        </VContext>
      );
    case 'CHECKBOX':
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <Checkbox
            style={{
              borderRadius: 9999,
              height: 25,
              width: 25,
            }}
            value={formData?.cells[field.id!] === 'true'}
            onValueChange={(newValue) => {
              onFieldChange(field.id!, String(newValue));
            }}
          />
        </VContext>
      );
    case 'DATETIME':
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              padding: 8,
              borderRadius: 8,
            }}
          >
            <DateTimePicker
              date={new Date(formData?.cells[field.id]?.toString() ?? '')}
              setDate={(date: Date) => onFieldChange(field.id!, new Date(date).toISOString())}
              mode="date"
            />
          </View>
        </VContext>
      );
    case 'CREATED_TIME':
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <Pressable
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 8,
              padding: 10,
              minHeight: 50,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
            onPress={() => onFieldChange(field.id!, new Date(new Date().getTime()).toISOString())}
          >
            <VText variant="B2" color={colors.textCommonSecondary}>
              {`${new Date(new Date().getTime()).toLocaleDateString()} ${field.id} `}
            </VText>
          </Pressable>
        </VContext>
      );
    case 'SINGLE_SELECT':
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <ContextMenu
            actions={convertedSingleSelectOptions}
            dropdownMenuMode={true}
            onPress={(e) => {
              onFieldChange(field.id!, [e.nativeEvent.name]);
            }}
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 8,
              padding: 10,
              minHeight: 50,
              justifyContent: 'space-between',
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <VText variant="B2" color={colors.textCommonSecondary}>
              {formData?.cells[field.id!]?.toString() || t.record.select_option}
            </VText>
            <AntDesign name="down" size={16} color={colors.textCommonTertiary} />
          </ContextMenu>
        </VContext>
      );
    case 'MEMBER': {
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <ContextMenu
            actions={convertedMemberOptions}
            dropdownMenuMode={true}
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 8,
              padding: 10,
              minHeight: 50,
              justifyContent: 'space-between',
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'row',
            }}
            onPress={(e) => {
              const selectedMemberIds =
                members
                  ?.filter((member) => member.name === e.nativeEvent.name)
                  .map((member) => member.id?.toString()) ?? [];

              onFieldChange(field.id!, selectedMemberIds || []);
            }}
          >
            <VText variant="B2" color={colors.textCommonSecondary}>
              {formData?.cells[field.id!]?.toString() ?? t.record.select_member}
            </VText>
            <AntDesign name="down" size={16} color={colors.textCommonTertiary} />
          </ContextMenu>
        </VContext>
      );
    }
    case 'CREATED_BY':
      return (
        <VContext title={iStringParse(field.name)} required={field.required}>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 8,
              padding: 10,
              minHeight: 50,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            {me && (
              <View className="flex flex-row items-center" style={{ gap: 5 }}>
                <AvatarImage size={24} content={me?.user.avatar ?? undefined} alt={me?.user.name} />
                <VText variant="B2" color={colors.textCommonSecondary}>
                  {me?.user.name}
                </VText>
              </View>
            )}
          </View>
        </VContext>
      );
    case 'PHONE':
    case 'EMAIL':
    case 'SINGLE_TEXT':
      return (
        <VInput
          title={iStringParse(field.name)}
          value={formData?.cells[field.id!]?.toString() ?? ''}
          onChangeText={(text) => onFieldChange(field.id!, text)}
          required={field.required}
        />
      );
    case 'LONG_TEXT':
      return (
        <VInput
          title={iStringParse(field.name)}
          value={formData?.cells[field.id!]?.toString() ?? ''}
          onChangeText={(text) => onFieldChange(field.id!, text)}
          multiline
          required={field.required}
        />
      );
    default:
      return (
        <VContext title={iStringParse(field.name)}>
          <VText variant="B2" color={colors.textCommonSecondary}>
            {t.action.coming_soon}
          </VText>
        </VContext>
      );
  }
};
