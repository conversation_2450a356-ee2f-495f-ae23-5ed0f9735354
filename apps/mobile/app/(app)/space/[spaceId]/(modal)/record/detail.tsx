import type { RecordDetailVO } from '@bika/types/database/vo';
import { Entypo } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { Stack, useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Platform, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import ContextMenu from 'react-native-context-menu-view';
import SweetSFSymbol from 'sweet-sfsymbols';
import FabGroup from '@/components/Button/FabGroup';
import { CellValue } from '@/components/cell-value/cell-value';
import { PreLoading } from '@/components/Loading';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { rgbaToHex } from '@/utils/color';

export default function RecordDetailScreen() {
  const { databaseId, recordId } = useLocalSearchParams();

  const spaceId = useSpaceId();

  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const [recordDetail, setRecordDetail] = useState<RecordDetailVO | null>(null);

  const [loading, setLoading] = useState<boolean>(true);

  const getRecordDetail = async () => {
    setLoading(true);
    await trpc.database.getRecord
      .query({
        databaseId: databaseId as string,
        recordId: recordId as string,
      })
      .then((data) => {
        setRecordDetail(data);
        setLoading(false);
      });
  };

  const deleteRecord = async () => {
    await trpc.database.deleteRecords
      .mutate({
        databaseId: databaseId as string,
        recordIds: [recordId as string],
      })
      .then(() => {
        router.back();
      });
  };

  useEffect(() => {
    if (focused) {
      getRecordDetail();
    }
  }, [focused]);

  if (loading && !recordDetail) {
    return <PreLoading />;
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 20,
              }}
            >
              <TouchableOpacity
                onPress={() =>
                  router.push({
                    pathname: `/(app)/space/${spaceId}/(modal)/record/update`,
                    params: {
                      databaseId: databaseId as string,
                      recordId: recordId as string,
                    },
                  })
                }
              >
                <SweetSFSymbol
                  name="pencil"
                  colors={[rgbaToHex(colors.textCommonPrimary)]}
                  size={20}
                />
              </TouchableOpacity>
              {Platform.OS === 'ios' && (
                <ContextMenu
                  actions={[
                    {
                      systemIcon: 'ellipsis.message',
                      title: t.record.record_comment,
                    },
                    {
                      systemIcon: 'person.badge.plus',
                      title: t.record.request_modify,
                    },
                    { systemIcon: 'pin', title: t.record.record_pin },
                    {
                      systemIcon: 'trash',
                      title: t.record.record_delete,
                      destructive: true,
                    },
                  ]}
                  dropdownMenuMode={true}
                  onPress={(e) => {
                    switch (e.nativeEvent.index) {
                      case 0:
                        router.push({
                          pathname: `/(app)/space/${spaceId}/(modal)/record/comment`,
                          params: {
                            databaseId: databaseId as string,
                            recordId: recordId as string,
                          },
                        });
                        break;
                      case 3:
                        deleteRecord();
                        break;
                      default:
                        COMING_SOON();
                        break;
                    }
                  }}
                >
                  <Entypo name="dots-three-horizontal" size={20} color={colors.textCommonPrimary} />
                </ContextMenu>
              )}
            </View>
          ),
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <ScrollView contentContainerStyle={{ flexGrow: 1, marginHorizontal: 16 }}>
          <FlashList
            data={recordDetail?.fields}
            renderItem={({ item }) => {
              return (
                <View
                  className="flex flex-col"
                  style={{
                    gap: 4,
                    borderRadius: 8,
                    padding: 8,
                    marginVertical: 4,
                    backgroundColor: colors.bgControlsDefault,
                  }}
                >
                  <VText variant="B1" color={colors.textCommonSecondary}>
                    {iStringParse(item?.name)}
                  </VText>
                  <CellValue
                    field={recordDetail?.fields.find((field) => field.id === item?.id)}
                    cell={recordDetail?.record?.cells[item?.id]}
                  />
                </View>
              );
            }}
          />
        </ScrollView>
        <FabGroup />
      </SafeAreaView>
    </>
  );
}
