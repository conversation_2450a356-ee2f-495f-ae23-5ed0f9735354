import {
  aiLaunchers,
  allLauncherCommands,
  routerLaunchers,
  uiModalLaunchers,
  urlLaunchers,
} from '@bika/contents/config/client';
import type { LauncherCommand } from '@bika/types/ai/bo';
import { FontAwesome5 } from '@expo/vector-icons';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import { FlashList } from '@shopify/flash-list';
import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { launcherRedirect } from '@/utils/launcher';

const CommandSection = ({ title, data }: { title: string; data: LauncherCommand[] }) => {
  const spaceId = useSpaceId();

  const router = useRouter();

  const colors = useColor();

  return (
    <VContext title={title} titleIndent>
      <View
        style={{
          alignItems: 'center',
          backgroundColor: colors.bgControlsDefault,
          borderRadius: 10,
          display: 'flex',
          flexDirection: 'row',
          flexGrow: 1,
          flexWrap: 'wrap',
          justifyContent: 'center',
          width: '100%',
        }}
      >
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1 }}>
          <FlashList
            data={data}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={{
                  borderRadius: 10,
                  flexShrink: 1,
                  padding: 16,
                }}
                onPress={() => router.replace(launcherRedirect(item, spaceId) as any)}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {iStringParse(item?.text)}
                  {/* {JSON.stringify(item)} */}
                </VText>
              </TouchableOpacity>
            )}
            ItemSeparatorComponent={TextSeparator}
            scrollEnabled={false}
            estimatedItemSize={54}
          />
        </ScrollView>
      </View>
    </VContext>
  );
};

export default function LauncherScreen() {
  const router = useRouter();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { locale } = useGlobalContext();

  const { t } = locale;

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchInput, setSearchInput] = useState('');

  const filteredLaunchers = allLauncherCommands.filter((item: LauncherCommand) =>
    iStringParse(item.text)?.toLowerCase().includes(searchInput.toLowerCase()),
  );

  const RenderFilteredSections = () => {
    if (filteredLaunchers.length === 0 && searchInput) {
      return (
        <TouchableOpacity
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
            justifyContent: 'center',
            alignItems: 'center',
            height: 54,
            paddingHorizontal: 16,
            display: 'flex',
            flexDirection: 'row',
            gap: 8,
          }}
          onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/magic`)}
        >
          <FontAwesome5 name="magic" size={16} color={colors.textCommonPrimary} />
          <VText variant="B1" color={colors.textCommonPrimary}>
            {t.launcher.command_not_found}
          </VText>
        </TouchableOpacity>
      );
    }

    const uiModalFiltered = filteredLaunchers.filter((item: LauncherCommand) =>
      uiModalLaunchers.includes(item),
    );
    const routerFiltered = filteredLaunchers.filter((item: LauncherCommand) =>
      routerLaunchers.includes(item),
    );
    const aiFiltered = filteredLaunchers.filter((item: LauncherCommand) =>
      aiLaunchers.includes(item),
    );
    const urlFiltered = filteredLaunchers.filter((item: LauncherCommand) =>
      urlLaunchers.includes(item),
    );

    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1, gap: 20 }}
      >
        {uiModalFiltered.length > 0 && (
          <CommandSection title={t.launcher.ui_modal} data={uiModalFiltered} />
        )}
        {routerFiltered.length > 0 && (
          <CommandSection title={t.launcher.router} data={routerFiltered} />
        )}
        {aiFiltered.length > 0 && <CommandSection title={t.launcher.ai} data={aiFiltered} />}
        {urlFiltered.length > 0 && <CommandSection title={t.launcher.url} data={urlFiltered} />}
      </ScrollView>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: '',
          headerTransparent: true,
          headerSearchBarOptions: {
            placeholder: t.launcher.ask_me_anything,
            hideWhenScrolling: false,
            onChangeText(e) {
              setSearchInput(e.nativeEvent.text);
            },
          },
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <View style={{ paddingBottom: 30, paddingHorizontal: 16 }}>
          <SegmentedControl
            values={[t.launcher.smart, t.launcher.command, t.launcher.file]}
            selectedIndex={selectedIndex}
            onChange={(event) => setSelectedIndex(event.nativeEvent.selectedSegmentIndex)}
            style={{ marginBottom: 10 }}
          />
          <RenderFilteredSections />
        </View>
      </SafeAreaView>
    </>
  );
}
