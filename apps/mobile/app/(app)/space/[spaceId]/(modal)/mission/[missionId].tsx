/* eslint-disable no-nested-ternary */
import { useApiCaller } from '@bika/api-caller';
import type {
  CommentRecordMission,
  MissionCreateRecord,
  MissionUpdateRecord,
  RedirectSpaceNodeMission,
  ReviewRecordMission,
} from '@bika/types/mission/bo';
import type { MissionVO } from '@bika/types/mission/vo';
import type { MemberVO } from '@bika/types/unit/vo';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import SweetSFSymbol from 'sweet-sfsymbols';
import { PreLoading } from '@/components/Loading';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { MarkdownRender } from '@/components/Render';
import { AvatarImage } from '@/components/UI/Avatar';
import { FullSeparator } from '@/components/UI/Separator';
import { StatusTag } from '@/components/UI/Tag';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { rgbaToHex } from '@/utils/color';
import { formatDate } from '@/utils/date';

export default function MissionIdScreen() {
  const { missionId } = useLocalSearchParams();
  const { trpcQuery } = useApiCaller();

  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const [mission, setMission] = useState<MissionVO | null>(null);

  const [loading, setLoading] = useState<boolean>(true);

  const spaceId = useSpaceId();

  const utils = trpcQuery.useUtils();

  const getMission = async () => {
    setLoading(true);
    await trpc.mission.info
      .query({
        id: missionId as string,
      })
      .then((res) => {
        setMission(res);
        setLoading(false);
      });
  };

  useEffect(() => {
    getMission();
  }, []);

  console.log('1111 mission', mission);

  const onGoToMission = () => {
    switch (mission?.type) {
      case 'INVITE_MEMBER':
        return router.replace(`/(app)/space/${spaceId}/(modal)/account/invite`);
      case 'UI_LAUNCHER':
        return router.replace(`/(app)/space/${spaceId}/(modal)/launcher`);
      case 'READ_MARKDOWN':
      case 'SET_SPACE_NAME':
        return router.replace(`/(app)/space/${spaceId}/(modal)/space/settings`);
      case 'CREATE_RECORD':
        return router.replace({
          pathname: `/(app)/space/${spaceId}/(modal)/record/create`,
          params: {
            databaseId: (mission.bo as MissionCreateRecord).databaseId,
          },
        });
      case 'UPDATE_RECORD': {
        const { databaseId, recordId } = mission.bo as MissionUpdateRecord;
        return router.replace({
          pathname: `/(app)/space/${spaceId}/(modal)/record/update`,
          params: {
            databaseId,
            recordId,
          },
        });
      }
      case 'COMMENT_RECORD': {
        const { databaseId, recordId } = mission.bo as CommentRecordMission;
        return router.replace({
          pathname: `/(app)/space/${spaceId}/(modal)/record/comment`,
          params: {
            databaseId,
            recordId,
          },
        });
      }
      case 'REVIEW_RECORD': {
        const { databaseId, recordId } = mission.bo as ReviewRecordMission;
        return router.replace({
          pathname: `/(app)/space/${spaceId}/(modal)/record/detail`,
          params: {
            databaseId,
            recordId,
          },
        });
      }
      case 'CREATE_TASK':
        return router.replace({
          pathname: `/(app)/space/${spaceId}/(modal)/create-anything`,
          params: {
            createIndex: 0,
          },
        });
      case 'REDIRECT_SPACE_NODE':
        return router.replace({
          pathname: `/(app)/space/${spaceId}/node/${(mission.bo as RedirectSpaceNodeMission).nodeId}`,
          params: {
            presentation: 'modal',
          },
        });
      default:
        COMING_SOON();
        break;
    }
  };

  const onManualComplete = async () => {
    await trpc.mission.complete
      .mutate({
        spaceId,
        id: missionId as string,
      })
      .then(() => {
        utils.my.todos.invalidate();

        router.back();
      });
  };

  const onTransfer = async () => {
    COMING_SOON();
  };

  const onDecline = async () => {
    await trpc.mission.reject
      .mutate({
        spaceId,
        id: missionId as string,
      })
      .then(() => {
        utils.my.todos.invalidate();

        router.back();
      });
  };

  if (loading) {
    return <PreLoading />;
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: mission?.name ?? '',
          headerLargeTitle: true,
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <View
            style={{
              padding: 16,
              borderRadius: 8,
              gap: 16,
            }}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                width: '100%',
                flexGrow: 1,
                gap: 24,
              }}
            >
              <VText
                variant="B2"
                color={colors.textCommonTertiary}
                style={{
                  width: '20%',
                }}
              >
                {t.mission.assignee}
              </VText>
              <View>
                {mission?.assignees?.map((assignee) => (
                  <Pressable
                    key={assignee.id}
                    className="flex flex-row items-center"
                    onPress={() => router.push(`/space/${spaceId}/(modal)/member/${assignee.id}`)}
                    style={{
                      backgroundColor: colors.bgTagDefault,
                      padding: 2,
                      paddingRight: 4,
                      borderRadius: 999,
                      gap: 4,
                      flexShrink: 1,
                    }}
                  >
                    <AvatarImage
                      size={24}
                      content={(assignee as MemberVO)?.avatar}
                      alt={(assignee as MemberVO)?.name}
                    />
                    <VText variant="B2" color={colors.textCommonPrimary}>
                      {mission?.assignees?.map((assignee) => assignee.name).join(', ') ??
                        'No Receiver'}
                    </VText>
                  </Pressable>
                )) ?? 'No Receiver'}
              </View>
            </View>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                width: '100%',
                flexGrow: 1,
                gap: 24,
              }}
            >
              <VText
                variant="B2"
                color={colors.textCommonTertiary}
                style={{
                  width: '20%',
                }}
              >
                {t.mission.createAt}
              </VText>
              <VText
                variant="B2"
                color={colors.textCommonPrimary}
                style={{
                  width: '80%',
                  flexShrink: 1,
                }}
              >
                {formatDate(mission?.createAt ?? '').toLocaleString() ?? 'No Create At'}
              </VText>
            </View>
            {mission?.dueAt && (
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '100%',
                  flexGrow: 1,
                  gap: 24,
                }}
              >
                <VText
                  variant="B2"
                  color={colors.textCommonTertiary}
                  style={{
                    width: '20%',
                  }}
                >
                  {t.mission.dueAt}
                </VText>
                <VText
                  variant="B2"
                  color={colors.textCommonPrimary}
                  style={{
                    width: '80%',
                    flexShrink: 1,
                  }}
                >
                  {formatDate(mission?.dueAt ?? '').toLocaleString() ?? 'No Create At'}
                </VText>
              </View>
            )}
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                width: '100%',
                flexGrow: 1,
                gap: 24,
              }}
            >
              <VText
                variant="B2"
                color={colors.textCommonTertiary}
                style={{
                  width: '20%',
                }}
              >
                {t.mission.progress}
              </VText>
              {mission?.status && <StatusTag status={mission.status} />}
            </View>
            {mission?.initiator && (
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  width: '100%',
                  flexGrow: 1,
                  gap: 24,
                }}
              >
                <VText
                  variant="B2"
                  color={colors.textCommonTertiary}
                  style={{
                    width: '20%',
                  }}
                >
                  {t.mission.initiator}
                </VText>
                <View>
                  <TouchableOpacity
                    className="flex flex-row items-center"
                    style={{
                      backgroundColor: colors.bgTagDefault,
                      padding: 2,
                      paddingRight: 4,
                      borderRadius: 999,
                      gap: 4,
                      flexShrink: 1,
                    }}
                  >
                    <AvatarImage
                      size={24}
                      content={mission?.initiator?.avatar}
                      alt={mission?.initiator?.name}
                    />
                    <VText variant="B2" color={colors.textCommonPrimary}>
                      {mission?.initiator ? mission?.initiator.name : 'No Initiator'}
                    </VText>
                  </TouchableOpacity>
                </View>
              </View>
            )}
            <View className="gap-1">
              <VText variant="H4" color={colors.textCommonPrimary}>
                {t.mission.description}
              </VText>
              <View>
                <MarkdownRender content={mission?.description ?? 'No Description'} />
                <View style={{ height: 100 }} />
              </View>
            </View>
          </View>
        </ScrollView>
        <View
          style={{
            position: 'absolute',
            bottom: 30,
            width: '100%',
            gap: 16,
            backgroundColor: colors.bgCommonDefault,
            paddingTop: 8,
          }}
        >
          <TouchableOpacity
            style={{
              minHeight: 40,
              backgroundColor:
                mission?.status !== 'COMPLETED' ? colors.bgBrandDefault : colors.bgBrandDisabled,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 8,
              marginHorizontal: 16,
              width: '90%',
              alignSelf: 'center',
            }}
            onPress={onGoToMission}
            disabled={mission?.status === 'COMPLETED'}
          >
            <VText variant="B2" color={colors.textStaticPrimary}>
              {mission?.status === 'COMPLETED'
                ? t.action.completed
                : iStringParse(mission?.bo.buttonText) === ''
                  ? t.mission.go_to_mission
                  : iStringParse(mission?.bo.buttonText)}
            </VText>
          </TouchableOpacity>
          <FullSeparator />
          {mission?.status !== 'COMPLETED' && (
            <View
              style={{
                marginHorizontal: 16,
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
              }}
            >
              {mission?.bo.canCompleteManually && (
                <>
                  <TouchableOpacity
                    style={{
                      alignItems: 'center',
                      alignSelf: 'center',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignContent: 'center',
                      gap: 8,
                      width: '35%',
                    }}
                    onPress={onManualComplete}
                  >
                    <SweetSFSymbol
                      name="checkmark.circle"
                      colors={[rgbaToHex(colors.textCommonPrimary)]}
                      size={16}
                    />
                    <VText variant="B2" color={colors.textCommonPrimary}>
                      {t.action.manual_complete}
                    </VText>
                  </TouchableOpacity>
                  {mission?.bo.canTransfer && (
                    <View
                      style={{
                        width: 1,
                        height: 20,
                        backgroundColor: colors.borderCommonDefault,
                      }}
                    />
                  )}
                </>
              )}
              {mission?.bo.canTransfer && (
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                    alignSelf: 'center',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignContent: 'center',
                    gap: 8,
                    width: '35%',
                  }}
                  onPress={onTransfer}
                >
                  <SweetSFSymbol
                    name="person.fill.and.arrow.left.and.arrow.right"
                    colors={[rgbaToHex(colors.textCommonPrimary)]}
                    size={16}
                  />
                  <VText variant="B2" color={colors.textCommonPrimary}>
                    {t.action.transfer}
                  </VText>
                </TouchableOpacity>
              )}
              {mission?.bo.canReject && (
                <>
                  {mission?.bo.canTransfer && (
                    <View
                      style={{
                        width: 1,
                        height: 20,
                        backgroundColor: colors.borderCommonDefault,
                      }}
                    />
                  )}
                  <TouchableOpacity
                    style={{
                      alignItems: 'center',
                      alignSelf: 'center',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignContent: 'center',
                      gap: 8,
                      width: '35%',
                    }}
                    onPress={onDecline}
                  >
                    <SweetSFSymbol
                      name="xmark.circle"
                      colors={[rgbaToHex(colors.textDangerDefault)]}
                      size={16}
                    />
                    <VText variant="B2" color={colors.textDangerDefault}>
                      {t.action.decline}
                    </VText>
                  </TouchableOpacity>
                </>
              )}
            </View>
          )}
        </View>
      </SafeAreaView>
    </>
  );
}
