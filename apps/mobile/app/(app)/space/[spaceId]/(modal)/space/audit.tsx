import type { AuditEventLogVO } from '@bika/types/system';
import type { MemberVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import { useEffect, useState } from 'react';
import { FlatList, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function SpaceAuditScreen() {
  const colors = useColor();

  const spaceId = useSpaceId();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const [audits, setAudits] = useState<AuditEventLogVO[]>([]);

  const [members, setMembers] = useState<MemberVO[] | null>(null);

  const [loading, setLoading] = useState(false);

  const getAudits = async () => {
    setLoading(true);
    await trpc.space.auditLogs
      .query({
        spaceId,
      })
      .then((data) => {
        setAudits(data.data);
        setLoading(false);
      });
  };

  const getMembers = async () => {
    setLoading(true);
    await trpc.member.list
      .query({
        spaceId,
      })
      .then((res) => {
        setMembers(res.data);
        setLoading(false);
      });
  };

  useEffect(() => {
    getAudits();

    getMembers();
  }, []);

  if (loading) {
    return <PreLoading />;
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView
        contentInsetAdjustmentBehavior="automatic"
        contentContainerStyle={{
          flexGrow: 1,
          marginHorizontal: 16,
          backgroundColor: colors.bgControlsDefault,
          borderRadius: 10,
        }}
      >
        <FlatList
          data={audits}
          renderItem={({ item }) => (
            <TouchableOpacity
              className="flex flex-row items-center justify-between"
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
              }}
            >
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: 20,
                  alignItems: 'center',
                }}
              >
                <View className="gap-2">
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {item.actor.name}
                  </VText>
                  <VText variant="B3" color={colors.textCommonTertiary}>
                    {new Date(item.createdAt ?? '').toLocaleString('zh-CN', {
                      hour12: false,
                    })}
                  </VText>
                </View>
              </View>
              <View>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </View>
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={TextSeparator}
          scrollEnabled={false}
        />
      </ScrollView>
    </SafeAreaView>
  );
}
