import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { SafeAreaView, ScrollView, TextInput, View } from 'react-native';
import { HeaderButton } from '@/components/UI/Button';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function IntegrationTelegramScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [name, setName] = useState('');

  const [botToken, setBotToken] = useState('');

  const [emptyError, setEmptyError] = useState(false);

  const handleSubmit = async () => {
    if (!name || !botToken) {
      setEmptyError(true);
      return;
    }

    await trpc.integration.create
      .mutate({
        spaceId,
        data: {
          type: 'TELEGRAM',
          name,
          token: botToken,
        },
      })
      .then(() => {
        router.back();
      });
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t.integration.telegram.title,
          headerRight: () => <HeaderButton title={t.action.submit} onPress={handleSubmit} />,
        }}
      />
      <SafeAreaView style={{ flex: 1, margin: 16 }}>
        <ScrollView contentContainerStyle={{ gap: 16 }}>
          <View
            className="flex flex-row items-center justify-between"
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
              padding: 16,
            }}
          >
            <VText
              variant="B1"
              color={emptyError && !name ? colors.textDangerDefault : colors.textCommonPrimary}
            >
              {t.integration.general.note}
            </VText>
            <TextInput
              placeholder={t.integration.telegram.title}
              value={name}
              onChangeText={setName}
            />
          </View>
          <View
            className="flex flex-col"
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View
              className="flex flex-row items-center justify-between"
              style={{ padding: 16, minHeight: 54 }}
            >
              <VText
                variant="B1"
                color={
                  emptyError && !botToken ? colors.textDangerDefault : colors.textCommonPrimary
                }
              >
                {t.integration.telegram.field_bot_token}
              </VText>
              <TextInput
                placeholder={t.integration.telegram.field_bot_token}
                value={botToken}
                onChangeText={setBotToken}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
