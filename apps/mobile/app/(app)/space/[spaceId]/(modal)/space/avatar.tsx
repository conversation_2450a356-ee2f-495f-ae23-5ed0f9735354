import type { AttachmentVO } from '@bika/types/attachment/vo';
import type { SpaceRenderVO } from '@bika/types/space/vo';
import { AntDesign, Entypo } from '@expo/vector-icons';
import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { Stack, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, TouchableOpacity, View } from 'react-native';
import { AvatarImage } from '@/components/UI/Avatar';
import { HeaderButton } from '@/components/UI/Button';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { uploadAttachment } from '@/utils/file';

export default function SpaceAvatarScreen() {
  const router = useRouter();

  const spaceId = useSpaceId();

  const colors = useColor();

  const [permission, requestPermission] = Camera.useCameraPermissions();

  const [newImage, setNewImage] = useState<ImagePicker.ImagePickerResult | null>(null);

  const [uploadResponse, setUploadResponse] = useState<AttachmentVO | null>(null);

  const isModalPresented = router.canGoBack();

  const [space, setSpace] = useState<SpaceRenderVO | null>(null);

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const getSpaceInfo = async () => {
    await trpc.space.info
      .query({
        identify: spaceId,
      })
      .then((res) => {
        setSpace(res);
      });
  };

  useEffect(() => {
    getSpaceInfo();
  }, []);

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync(imagePickerConfig);

    if (!result.canceled) {
      setNewImage(result);
      onUploadAttachment(result);
    }
  };

  const takePhoto = async () => {
    if (!permission?.granted) requestPermission();

    const result = await ImagePicker.launchCameraAsync(imagePickerConfig);

    if (!result.canceled) {
      setNewImage(result);
      onUploadAttachment(result);
    }
  };

  const onUploadAttachment = async (image: ImagePicker.ImagePickerResult) => {
    if (!image) return;

    const asset = image.assets?.[0];

    const fileExt = asset?.uri.split('.').pop();

    const { path, presignedPutUrl } = await trpc.attachment.getPresignedPut.mutate({
      // fileExt: `.${fileExt}`,
      spaceId,
      contentType: asset && asset.mimeType,
      size: asset?.fileSize ?? 0,
    });
    const uploadAttachmentResponse = await uploadAttachment(
      presignedPutUrl,
      asset?.uri as string,
      fileExt as string,
      asset?.mimeType || 'image/png',
      asset?.fileSize ?? 0,
    );

    if (!uploadAttachmentResponse.ok) {
      console.error('error when uploading attachment');
    }

    await trpc.attachment.create
      .mutate({
        path,
        prefix: 'avatar',
      })
      .then((response) => {
        setUploadResponse(response);

        console.log('Attachment created:', response);
      });
  };

  const pickEmoji = async () => {
    // showActionSheetWithOptions(
    //   {
    //     options: ['😀', '😂', '😎', '😍', '😜', '😡', '😭', '😱', '😳', '😴'],
    //     cancelButtonIndex: 0,
    //   },
    //   (buttonIndex) => {
    //     if (buttonIndex !== 0) {
    //       setNewImage({
    //         uri: `https://emojicdn.elk.sh/${buttonIndex}`,
    //       });
    //     }
    //   },
    // );
  };

  console.log('newImage', newImage);

  const onSave = async () => {
    if (!newImage || !uploadResponse) return;

    try {
      await trpc.space.update
        .mutate({
          id: spaceId,
          data: {
            logo: {
              type: 'ATTACHMENT',
              attachmentId: uploadResponse.id,
              relativePath: uploadResponse.path,
            },
          },
        })
        .then(() => {
          router.back();
        });
    } catch (error) {
      console.error('Error uploading image:', error);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <HeaderButton
              onPress={onSave}
              title={newImage && !uploadResponse ? t.action.loading : t.action.save}
              disabled={!newImage || !uploadResponse}
            />
          ),
          headerLeft: () =>
            isModalPresented && (
              <HeaderButton onPress={() => router.back()} title={t.action.cancel} />
            ),
        }}
      />
      <SafeAreaView
        style={{
          alignItems: 'center',
          flex: 1,
          gap: 30,
        }}
      >
        {newImage ? (
          <AvatarImage size={96} local localUrl={newImage?.assets?.[0].uri} />
        ) : (
          <AvatarImage size={96} content={space?.logo} alt={space?.name} />
        )}
        <View className="flex flex-row items-center gap-6">
          <TouchableOpacity
            style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: colors.rainbowPurple1,
              borderWidth: 1,
              borderColor: colors.rainbowPurple3,
              justifyContent: 'center',
              width: 72,
              height: 72,
              borderRadius: 9999,
            }}
            onPress={takePhoto}
          >
            <AntDesign name="camerao" size={24} color={colors.textBrandDefault} />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: colors.rainbowPurple1,
              borderWidth: 1,
              borderColor: colors.rainbowPurple3,
              justifyContent: 'center',
              width: 72,
              height: 72,
              borderRadius: 9999,
            }}
            onPress={pickImage}
          >
            <Entypo name="image" size={24} color={colors.textBrandDefault} />
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: colors.rainbowPurple1,
              borderWidth: 1,
              borderColor: colors.rainbowPurple3,
              justifyContent: 'center',
              width: 72,
              height: 72,
              borderRadius: 9999,
            }}
            onPress={pickEmoji}
          >
            <Entypo
              name="emoji-happy"
              size={24}
              color={colors.textBrandDefault}
            />
          </TouchableOpacity> */}
        </View>
      </SafeAreaView>
    </>
  );
}

const imagePickerConfig: ImagePicker.ImagePickerOptions = {
  mediaTypes: ImagePicker.MediaTypeOptions.All,
  allowsEditing: true,
  aspect: [4, 3],
  quality: 1,
};
