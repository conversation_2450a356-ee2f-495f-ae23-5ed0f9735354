import type { SpaceRenderVO } from '@bika/types/space/vo';
import type { MemberVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import Checkbox from 'expo-checkbox';
import { Stack } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, SafeAreaView, View } from 'react-native';
import { HeaderButton } from '@/components/UI/Button';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function TeamAndMembersScreen() {
  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [spaces, setSpaces] = useState<SpaceRenderVO[] | null>(null);

  const [members, setMembers] = useState<MemberVO[] | null>(null);

  const getSpaces = async () => {
    await trpc.space.list.query({}).then((res) => {
      setSpaces(res);
    });
  };

  const getMembers = async () => {
    await trpc.member.list
      .query({
        spaceId,
      })
      .then((res) => {
        setMembers(res.data);
      });
  };

  useEffect(() => {
    getSpaces();

    getMembers();
  }, []);

  console.log('spaces', spaces);

  console.log('members', members);

  return (
    <>
      <Stack.Screen
        options={{
          title: t.space.members_and_teams,
          headerTransparent: true,
          headerRight: () => {
            return <HeaderButton onPress={() => Alert.alert('save')} title={t.action.save} />;
          },
          headerSearchBarOptions: {
            placeholder: 'Search',
          },
        }}
      />
      <SafeAreaView
        style={{
          flex: 1,
          margin: 16,
          gap: 20,
        }}
      >
        <VText variant="B2" color={colors.textCommonTertiary}>
          {t.space.teams}
        </VText>
        {spaces?.map((space) => {
          return (
            <View
              key={space.id}
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 16,
                }}
              >
                <Checkbox
                  value={false}
                  onValueChange={() => {}}
                  color={colors.textCommonTertiary}
                  style={{
                    borderRadius: 9999,
                    borderWidth: 1.5,
                    width: 20,
                    height: 20,
                  }}
                />
                <View
                  style={{
                    backgroundColor: '#907FF033',
                    borderRadius: 5,
                    padding: 8,
                    borderWidth: 1,
                    borderColor: '#FFFFFF1F',
                  }}
                >
                  <AntDesign name="team" size={20} color={colors.textBrandDefault} />
                </View>
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {space.name}
                </VText>
              </View>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: 8,
                  alignItems: 'center',
                }}
              >
                <VText variant="B1" color={colors.textCommonTertiary}>
                  {members?.length}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </View>
            </View>
          );
        })}
      </SafeAreaView>
    </>
  );
}
