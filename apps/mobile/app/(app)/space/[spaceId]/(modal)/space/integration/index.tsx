import type { IntegrationVO } from '@bika/types/integration/vo';
import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, ScrollView, View } from 'react-native';
import { IconSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

export default function SpaceIntegrationScreen() {
  const colors = useColor();

  const router = useRouter();

  const spaceId = useSpaceId();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const [integrations, setIntegrations] = useState<IntegrationVO[] | null>(null);

  const fetchIntegrations = async () => {
    await trpc.integration.list
      .query({
        spaceId,
      })
      .then((data) => {
        setIntegrations(data.integrations);
      });
  };

  useEffect(() => {
    fetchIntegrations();
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView
        contentInsetAdjustmentBehavior="automatic"
        contentContainerStyle={{
          gap: 16,
        }}
      >
        {integrations && integrations.length > 0 && (
          <VContext title={t.integration.integration} titleIndent>
            <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                marginHorizontal: 16,
              }}
            >
              <FlashList
                items={integrations}
                renderItem={(item) => {
                  return (
                    <Pressable
                      style={{
                        padding: 10,
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        minHeight: 54,
                      }}
                    >
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          gap: 16,
                          marginLeft: 4,
                          alignItems: 'center',
                        }}
                      >
                        <View>
                          <VText variant="B1" color={colors.textCommonPrimary}>
                            {item.name}
                          </VText>
                        </View>
                      </View>
                      <View>
                        <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                      </View>
                    </Pressable>
                  );
                }}
              />
            </View>
          </VContext>
        )}
        <View
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
            marginHorizontal: 16,
          }}
        >
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/smtp`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/email.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.smtp.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.smtp.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
          <IconSeparator />
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/x`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/x.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.twitter.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.twitter.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
          <IconSeparator />
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/dingtalk`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/ding_talk.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.dingtalk.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.dingtalk.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
          <IconSeparator />
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/lark`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/feishu.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.feishu.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.feishu.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
          <IconSeparator />
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/slack`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/slack.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.slack.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.slack.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
          <IconSeparator />
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/telegram`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/telegram.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.telegram.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.telegram.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
          <IconSeparator />
          <Pressable
            style={{
              padding: 10,
              marginRight: 16,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              minHeight: 54,
            }}
            onPress={() => router.push(`(app)/space/${spaceId}/(modal)/space/integration/wecom`)}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
                marginLeft: 4,
                alignItems: 'center',
              }}
            >
              <Image
                source={{
                  uri: `${getBaseUrl()}/assets/icons/automation/we_com.png`,
                }}
                style={{ width: 32, height: 32, borderRadius: 6 }}
              />
              <View className="shrink">
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.integration.wecom.title}
                </VText>
                <VText variant="B4" color={colors.textCommonTertiary}>
                  {t.integration.wecom.description}
                </VText>
              </View>
            </View>
            <View>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </Pressable>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
