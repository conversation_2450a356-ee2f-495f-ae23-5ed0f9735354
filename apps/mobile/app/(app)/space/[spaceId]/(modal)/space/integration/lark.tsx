import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { SafeAreaView, ScrollView, TextInput, View } from 'react-native';
import { HeaderButton } from '@/components/UI/Button';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function IntegrationLarkScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [name, setName] = useState('');

  const [webhook, setWebhook] = useState('');

  const [emptyError, setEmptyError] = useState(false);

  const handleSubmit = async () => {
    if (!name || !webhook) {
      setEmptyError(true);
      return;
    }

    await trpc.integration.create
      .mutate({
        spaceId,
        data: {
          type: 'FEI_SHU',
          name,
          webHookUrl: webhook,
        },
      })
      .then(() => {
        router.back();
      });
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t.integration.feishu.title,
          headerRight: () => <HeaderButton title={t.action.submit} onPress={handleSubmit} />,
        }}
      />
      <SafeAreaView style={{ flex: 1, margin: 16 }}>
        <ScrollView contentContainerStyle={{ gap: 16 }}>
          <View
            className="flex flex-row items-center justify-between"
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
              padding: 16,
            }}
          >
            <VText
              variant="B1"
              color={emptyError && !name ? colors.textDangerDefault : colors.textCommonPrimary}
            >
              {t.integration.general.note}
            </VText>
            <TextInput
              placeholder={t.integration.feishu.title}
              value={name}
              onChangeText={setName}
            />
          </View>
          <View
            className="flex flex-col"
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View
              className="flex flex-row items-center justify-between"
              style={{ padding: 16, minHeight: 54 }}
            >
              <VText
                variant="B1"
                color={emptyError && !webhook ? colors.textDangerDefault : colors.textCommonPrimary}
              >
                {t.integration.feishu.form_item_1_label}
              </VText>
              <TextInput
                placeholder={t.integration.feishu.form_item_1_label}
                value={webhook}
                onChangeText={setWebhook}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
