import type { SpaceRenderVO } from '@bika/types/space/vo';
import { AntDesign } from '@expo/vector-icons';
import { type iString, iStringParse } from 'basenext/i18n';
import { Stack, useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  Switch,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import SweetSFSymbol from 'sweet-sfsymbols';
import { AvatarImage } from '@/components/UI/Avatar';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useAction } from '@/context/action/provider';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';
import { rgbaToHex } from '@/utils/color';

export default function SpaceSettingsScreen() {
  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { reloadApp } = useAction();

  const [spaceDetails, setSpaceDetails] = useState<SpaceRenderVO | null>(null);

  const [isEnabledWatermark, setIsEnabledWatermark] = useState<boolean>(false);
  const [announcement, setAnnouncement] = useState<iString>('');

  const [newName, setNewName] = useState<string>('');
  const [newSlug, setNewSlug] = useState<string>('');
  const [newEmail1, setNewEmail1] = useState<string>('');
  const [newEmail2, setNewEmail2] = useState<string>('');
  const [newEmail3, setNewEmail3] = useState<string>('');

  const toggleSwitch = () => setIsEnabledWatermark((previousState) => !previousState);

  const getSpaceInfo = async () => {
    await trpc.space.info
      .query({
        identify: spaceId,
      })
      .then((res) => {
        setSpaceDetails(res);

        setNewName(res?.name ?? '');

        setNewSlug(res?.slug ?? '');

        setAnnouncement(res?.settings?.announcement ?? '');

        setNewEmail1(res?.settings?.allowEmailDomains?.[0] ?? '');
        setNewEmail2(res?.settings?.allowEmailDomains?.[1] ?? '');
        setNewEmail3(res?.settings?.allowEmailDomains?.[2] ?? '');

        setIsEnabledWatermark(res?.settings.watermark ?? false);
      });
  };

  const onSaveSpaceSettings = async () => {
    await trpc.space.update
      .mutate({
        id: spaceId,
        data: {
          name: newName,
          slug: newSlug,
          settings: {
            onboarding: 'DONE',
            announcement,
            watermark: isEnabledWatermark,
            allowEmailDomains: [newEmail1, newEmail2, newEmail3],
          },
        },
      })
      .then(() => {
        router.back();
      });
  };

  useEffect(() => {
    if (focused) {
      getSpaceInfo();
    }
  }, [focused]);

  console.log('222222 spaceDetails', spaceDetails);

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 20,
              }}
            >
              <TouchableOpacity onPress={() => onSaveSpaceSettings()}>
                <SweetSFSymbol
                  name="checkmark"
                  colors={[rgbaToHex(colors.textCommonPrimary)]}
                  size={20}
                />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}
        >
          <ScrollView contentContainerStyle={{ flexGrow: 1, gap: 25, margin: 16 }}>
            <View style={{ gap: 4 }}>
              <VContext title={t.settings.general} titleIndent>
                <View
                  style={{
                    backgroundColor: colors.bgControlsDefault,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      padding: 16,
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignContent: 'center',
                      height: 54,
                    }}
                  >
                    <VText
                      variant="B1"
                      color={colors.textCommonPrimary}
                      style={{
                        alignSelf: 'center',
                      }}
                    >
                      {t.space.space_logo}
                    </VText>
                    <Pressable
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 6,
                      }}
                      onPress={() => router.push('./avatar')}
                    >
                      <AvatarImage
                        size={32}
                        content={spaceDetails?.logo}
                        alt={spaceDetails?.name}
                      />
                      <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                    </Pressable>
                  </View>
                  <TextSeparator />
                  <View
                    style={{
                      padding: 16,
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      height: 54,
                    }}
                  >
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {t.space.space_name}
                    </VText>
                    <TextInput
                      style={{
                        color: colors.textCommonPrimary,
                        fontSize: 16,
                        fontWeight: '500',
                        padding: 4,
                      }}
                      placeholder={newName}
                      value={newName}
                      onChangeText={(value) => setNewName(value)}
                    />
                  </View>
                  <TextSeparator />
                  <View
                    style={{
                      padding: 16,
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignContent: 'center',
                      height: 54,
                    }}
                  >
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {t.space.space_subdomain}
                    </VText>
                    <TextInput
                      style={{
                        color: colors.textCommonPrimary,
                        fontSize: 16,
                        fontWeight: '500',
                        padding: 4,
                      }}
                      placeholder={newSlug}
                      value={newSlug}
                      onChangeText={(value) => setNewSlug(value)}
                    />
                  </View>
                </View>
              </VContext>
              {spaceDetails?.slug && (
                <VText variant="B3" color={colors.textCommonTertiary}>
                  {`${t.space.you_will_be_assigned_a_subdomain} ` +
                    `https://${spaceDetails.slug}.${getBaseUrl()}`}
                </VText>
              )}
            </View>
            <View style={{ gap: 4 }}>
              <VContext title={t.space.advanced} titleIndent>
                <View
                  style={{
                    backgroundColor: colors.bgControlsDefault,
                    borderRadius: 10,
                    minHeight: 100,
                    padding: 10,
                  }}
                >
                  <TextInput
                    style={{
                      color: colors.textCommonPrimary,
                      fontSize: 16,
                      fontWeight: '500',
                      padding: 4,
                    }}
                    multiline
                    placeholder={t.space.announcement}
                    placeholderTextColor={colors.textCommonTertiary}
                    value={iStringParse(announcement)}
                    onChangeText={(value) => setAnnouncement(value)}
                  />
                </View>
              </VContext>
            </View>
            <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
              }}
            >
              <View
                style={{
                  padding: 16,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.space.email_domain} 1
                </VText>
                <TextInput
                  style={{
                    color: colors.textCommonPrimary,
                    fontSize: 16,
                    fontWeight: '500',
                    padding: 4,
                  }}
                  multiline
                  placeholder={t.space.email_domain}
                  placeholderTextColor={colors.textCommonTertiary}
                  value={newEmail1}
                  onChangeText={(value) => setNewEmail1(value)}
                />
              </View>
              <TextSeparator />
              <View
                style={{
                  padding: 16,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.space.email_domain} 2
                </VText>
                <TextInput
                  style={{
                    color: colors.textCommonPrimary,
                    fontSize: 16,
                    fontWeight: '500',
                    padding: 4,
                  }}
                  multiline
                  placeholder={t.space.email_domain}
                  placeholderTextColor={colors.textCommonTertiary}
                  value={newEmail2}
                  onChangeText={(value) => setNewEmail2(value)}
                />
              </View>
              <TextSeparator />
              <View
                style={{
                  padding: 16,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.space.email_domain} 3
                </VText>
                <TextInput
                  style={{
                    color: colors.textCommonPrimary,
                    fontSize: 16,
                    fontWeight: '500',
                    padding: 4,
                  }}
                  multiline
                  placeholder={t.space.email_domain}
                  placeholderTextColor={colors.textCommonTertiary}
                  value={newEmail3}
                  onChangeText={(value) => setNewEmail3(value)}
                />
              </View>
            </View>
            <View style={{ gap: 4 }}>
              <View
                style={{
                  backgroundColor: colors.bgControlsDefault,
                  borderRadius: 10,
                }}
              >
                <View
                  style={{
                    padding: 10,
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignContent: 'center',
                    height: 54,
                  }}
                >
                  <VText
                    variant="B1"
                    color={colors.textCommonPrimary}
                    style={{
                      alignSelf: 'center',
                    }}
                  >
                    {t.space.show_watermark}
                  </VText>
                  <Switch
                    trackColor={{
                      false: '#767577',
                      true: colors.textBrandDefault,
                    }}
                    ios_backgroundColor={colors.bgCommonDefault}
                    onValueChange={toggleSwitch}
                    value={isEnabledWatermark}
                    style={{
                      alignSelf: 'center',
                    }}
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
}
