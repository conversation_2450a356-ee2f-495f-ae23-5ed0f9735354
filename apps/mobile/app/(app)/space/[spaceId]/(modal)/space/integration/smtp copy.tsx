import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { SafeAreaView, ScrollView, TextInput, View } from 'react-native';
import { HeaderButton } from '@/components/UI/Button';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function IntegrationSMTPScreen() {
  const router = useRouter();

  const [name, setName] = useState('');

  const [server, setServer] = useState('');
  const [port, setPort] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const [emptyError, setEmptyError] = useState(false);

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const handleSubmit = async () => {
    if (!name || !server || !port || !username || !password) {
      setEmptyError(true);
      return;
    }

    await trpc.integration.create
      .mutate({
        spaceId,
        data: {
          type: 'SMTP_EMAIL_ACCOUNT',
          name,
          host: server,
          port: Number(port),
          username,
          password,
        },
      })
      .then(() => {
        router.back();
      });
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t.integration.smtp.title,
          headerRight: () => <HeaderButton title={t.action.submit} onPress={handleSubmit} />,
        }}
      />
      <SafeAreaView style={{ flex: 1, margin: 16 }}>
        <ScrollView contentContainerStyle={{ gap: 16 }}>
          <View
            className="flex flex-row items-center justify-between"
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
              padding: 16,
            }}
          >
            <VText
              variant="B1"
              color={emptyError && !name ? colors.textDangerDefault : colors.textCommonPrimary}
            >
              {t.integration.general.note}
            </VText>
            <TextInput placeholder={t.integration.smtp.title} value={name} onChangeText={setName} />
          </View>
          <View
            className="flex flex-col"
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View
              className="flex flex-row items-center justify-between"
              style={{ padding: 16, minHeight: 54 }}
            >
              <VText
                variant="B1"
                color={emptyError && !server ? colors.textDangerDefault : colors.textCommonPrimary}
              >
                {t.integration.smtp.server_label}
              </VText>
              <TextInput
                placeholder={t.integration.smtp.server_label}
                value={server}
                onChangeText={setServer}
              />
            </View>
            <TextSeparator />
            <View
              className="flex flex-row items-center justify-between"
              style={{ padding: 16, minHeight: 54 }}
            >
              <VText
                variant="B1"
                color={emptyError && !port ? colors.textDangerDefault : colors.textCommonPrimary}
              >
                {t.integration.smtp.port_label}
              </VText>
              <TextInput
                placeholder={t.integration.smtp.port_label}
                value={port}
                onChangeText={setPort}
              />
            </View>
            <TextSeparator />
            <View
              className="flex flex-row items-center justify-between"
              style={{ padding: 16, minHeight: 54 }}
            >
              <VText
                variant="B1"
                color={
                  emptyError && !username ? colors.textDangerDefault : colors.textCommonPrimary
                }
              >
                {t.integration.smtp.user_name_label}
              </VText>
              <TextInput
                placeholder={t.integration.smtp.user_name_label}
                value={username}
                onChangeText={setUsername}
              />
            </View>
            <TextSeparator />
            <View
              className="flex flex-row items-center justify-between"
              style={{ padding: 16, minHeight: 54 }}
            >
              <VText
                variant="B1"
                color={
                  emptyError && !password ? colors.textDangerDefault : colors.textCommonPrimary
                }
              >
                {t.integration.smtp.password_label}
              </VText>
              <TextInput
                placeholder={t.integration.smtp.password_label}
                value={password}
                onChangeText={setPassword}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
