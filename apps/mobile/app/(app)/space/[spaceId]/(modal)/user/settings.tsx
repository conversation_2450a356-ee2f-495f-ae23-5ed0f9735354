import type { LocaleType } from 'basenext/i18n';
import { useRouter } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import {
  Alert,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import ContextMenu from 'react-native-context-menu-view';
import SweetSFSymbol from 'sweet-sfsymbols';
import { AvatarImage } from '@/components/UI/Avatar';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useAction } from '@/context/action/provider';
import { useGlobalContext } from '@/context/global';
import { Theme } from '@/context/global/types';
import { useColor } from '@/hooks/useColor';
import { i18n, Locales, usei18n } from '@/hooks/usei18n';
import { getKeyByValue, getValueByKey } from '@/utils';
import { rgbaToHex } from '@/utils/color';

export default function UserSettingsScreen() {
  const { reloadApp } = useAction();

  const router = useRouter();

  const colors = useColor();

  const { trpc, theme, locale, auth } = useGlobalContext();

  const { t } = locale;

  const { me } = auth;

  const { setCurrentLocale } = usei18n();

  const [systemLocale, setSystemLocale] = useState<string | null>(null);

  const getSystemLocale = () => {
    const storedLocale = SecureStore.getItem('locale');

    if (storedLocale) {
      setSystemLocale(storedLocale);
    }
  };

  const onChangeUserInfo = async (newName: string) => {
    await trpc.user.updateUserInfo.mutate({
      name: newName,
    });
  };

  const onChangeTheme = async (theme: Theme) => {
    console.log('new Theme', theme);

    await SecureStore.setItemAsync('theme', theme).then(() => reloadApp());
  };

  const handleChangeName = () => {
    Alert.prompt(
      'Change Name',
      'Enter your new name:',
      (text) => {
        onChangeUserInfo(text);
      },
      undefined,
      me?.user.name,
    );
  };

  const onChangeLocale = async (newLocale: LocaleType) => {
    await trpc.user.updateUserSettings
      .mutate({
        locale: newLocale,
      })
      .then(() => {
        setCurrentLocale(newLocale);

        reloadApp();
      });
  };

  useEffect(() => {
    getSystemLocale();
  }, []);

  console.log('systemLocale', systemLocale);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={Platform.OS === 'ios' ? 'light' : 'auto'} />
      <View style={styles.innerContainer}>
        <VContext title={t.user.profile} titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
              display: 'flex',
              width: '100%',
            }}
          >
            <View style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.user.avatar}
              </VText>
              <Pressable style={styles.itemContent} onPress={() => router.push('./avatar')}>
                <AvatarImage size={32} content={me?.user.avatar} alt={me?.user.name} />
                <SweetSFSymbol
                  name="chevron.right"
                  colors={[rgbaToHex(colors.textCommonTertiary)]}
                  size={16}
                />
              </Pressable>
            </View>
            <TextSeparator />
            <Pressable onPress={handleChangeName} style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.user.name}
              </VText>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {me?.user.name ?? t.user.no_name}
              </VText>
            </Pressable>
            <TextSeparator />
            <View style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.user.email}
              </VText>
              <Pressable style={styles.itemContent} onPress={() => router.push('./email')}>
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {me?.user.email ?? t.user.no_email}
                </VText>
                <SweetSFSymbol
                  name="chevron.right"
                  colors={[rgbaToHex(colors.textCommonTertiary)]}
                  size={16}
                />
              </Pressable>
            </View>
          </View>
        </VContext>
        <VContext title={t.user.preference} titleIndent>
          <View
            style={{
              alignItems: 'center',
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
              display: 'flex',
              flexDirection: 'row',
              flexGrow: 1,
              flexWrap: 'wrap',
              justifyContent: 'center',
              width: '100%',
            }}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ flexGrow: 1 }}
            >
              <View style={styles.headerItem}>
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.user.theme}
                </VText>
                <ContextMenu
                  actions={[
                    { title: t.theme.system },
                    { title: t.theme.dark },
                    { title: t.theme.light },
                  ]}
                  dropdownMenuMode={true}
                  style={styles.itemContent}
                  onPress={(e) => {
                    let newTheme: Theme = Theme.System;
                    switch (e.nativeEvent.index) {
                      case 1:
                        newTheme = Theme.Dark;
                        break;
                      case 2:
                        newTheme = Theme.Light;
                        break;
                      case 0:
                      default:
                        newTheme = Theme.System;
                        break;
                    }
                    onChangeTheme(newTheme);
                  }}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {i18n.t(`theme.${theme}`)}
                  </VText>
                  <SweetSFSymbol
                    name="chevron.down"
                    colors={[rgbaToHex(colors.textCommonTertiary)]}
                    size={16}
                  />
                </ContextMenu>
              </View>
              <TextSeparator />
              <View style={styles.headerItem}>
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.user.language}
                </VText>
                <ContextMenu
                  actions={[
                    { title: 'English' },
                    { title: '简体中文' },
                    { title: '繁体中文' },
                    { title: '日本語' },
                  ]}
                  dropdownMenuMode={true}
                  style={styles.itemContent}
                  onPress={(e) => {
                    console.log(getKeyByValue(Locales, e.nativeEvent.name));

                    onChangeLocale(getKeyByValue(Locales, e.nativeEvent.name)! as LocaleType);
                  }}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {getValueByKey(Locales, systemLocale!)}
                  </VText>
                  <SweetSFSymbol
                    name="chevron.down"
                    colors={[rgbaToHex(colors.textCommonTertiary)]}
                    size={16}
                  />
                </ContextMenu>
              </View>
              <TextSeparator />
              <View style={styles.headerItem}>
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.user.timezone}
                </VText>
                <ContextMenu
                  actions={[{ title: 'UTC+8' }]}
                  dropdownMenuMode={true}
                  style={styles.itemContent}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    UTC+8
                  </VText>
                  <SweetSFSymbol
                    name="chevron.down"
                    colors={[rgbaToHex(colors.textCommonTertiary)]}
                    size={16}
                  />
                </ContextMenu>
              </View>
            </ScrollView>
          </View>
        </VContext>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
  },
  headerItem: {
    alignItems: 'center',
    borderRadius: 10,
    flexDirection: 'row',
    height: 54,
    justifyContent: 'space-between',
    padding: 16,
    width: '100%',
  },
  innerContainer: {
    gap: 16,
    padding: 16,
    width: '100%',
  },
  itemContent: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 6,
  },
});
