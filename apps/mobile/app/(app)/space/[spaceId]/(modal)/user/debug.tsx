import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Application from 'expo-application';
import * as Clipboard from 'expo-clipboard';
import Constants from 'expo-constants';
import * as Notifications from 'expo-notifications';
import { useRouter } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { usePostHog } from 'posthog-react-native';
import {
  Alert,
  Button,
  Platform,
  SafeAreaView,
  ScrollView,
  useColorScheme,
  View,
} from 'react-native';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useAction } from '@/context/action/provider';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { schedulePushNotification, usePushNotification } from '@/hooks/useNotification';
import { baseUrl } from '@/utils/base';

export default function DebugScreen() {
  const { expoPushToken, notification } = usePushNotification();

  const router = useRouter();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { expoConfig } = Constants;
  const { nativeApplicationVersion } = Application;

  const { showAlert, showPrompt, showSnackbar } = useAction();

  const colorScheme = useColorScheme();

  const posthog = usePostHog();

  const clearAsyncStorage = async () => {
    await AsyncStorage.clear().then(() => {
      Alert.alert('Async Storage cleared');
    });
  };

  const clearSecureStorage = async () => {
    await SecureStore.deleteItemAsync('session_cookie').then(() => {
      Alert.alert('Secure Storage cleared');
    });
  };

  const copyExpoPushTokenToClipboard = async () => {
    await Clipboard.setStringAsync(
      expoPushToken ? (expoPushToken as unknown as string) : 'No Expo Push Token',
    ).then(() => {
      Alert.alert(expoPushToken ? (expoPushToken as unknown as string) : 'No Expo Push Token');
    });
  };

  const cleanAllNotificationBadge = async () => {
    await Notifications.setBadgeCountAsync(0);
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1, margin: 16 }}>
        <VContext title="Notification" titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              padding: 10,
              borderRadius: 8,
              marginBottom: 10,
            }}
          >
            <Button title="clean all notifications badge" onPress={cleanAllNotificationBadge} />
            <Button
              title={expoPushToken ? 'Copy Expo Push Token' : 'No Expo Push Token'}
              onPress={copyExpoPushTokenToClipboard}
            />
            <Button
              title="Press to schedule a notification"
              onPress={async () => {
                await schedulePushNotification({
                  title: "You've got mail! 📬",
                  body: 'Here is the notification body',
                  data: {
                    data: 'goes here',
                  },
                });
              }}
            />
            <VText variant="B1" color={colors.textCommonPrimary}>
              Title: {notification?.request.content.title}
            </VText>
            <VText variant="B1" color={colors.textCommonPrimary}>
              Body: {notification?.request.content.body}
            </VText>
            <VText variant="B1" color={colors.textCommonPrimary}>
              Data: {notification && JSON.stringify(notification.request.content.data)}
            </VText>
          </View>
        </VContext>
        <VContext title="Async & Secure Storage" titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              padding: 10,
              borderRadius: 8,
              marginBottom: 10,
            }}
          >
            <Button title="Reset Async Storage" onPress={clearAsyncStorage} />
            <Button title="Reset Secure Storage" onPress={clearSecureStorage} />
          </View>
        </VContext>
        <VContext title="Action" titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              padding: 10,
              borderRadius: 8,
              marginBottom: 10,
            }}
          >
            <Button
              title="Show Alert"
              onPress={() => {
                showAlert('Hello, world!');
              }}
            />
            <Button
              title="Show Prompt"
              onPress={() => {
                showPrompt('Hello, world!');
              }}
            />
            <Button
              title="Show Snackbar"
              onPress={() => {
                showSnackbar('Hello, world!');
              }}
            />
          </View>
        </VContext>
        <VContext title="Wizard" titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              padding: 8,
              borderRadius: 8,
              marginBottom: 10,
            }}
          >
            <Button
              title="Onboarding"
              onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/(wizard)/onboarding`)}
            />
            <Button
              title="Binding Account"
              onPress={() =>
                router.push(`/(app)/space/${spaceId}/(modal)/(wizard)/account-binding`)
              }
            />
          </View>
        </VContext>
        <VContext title="Tracking" titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              padding: 8,
              borderRadius: 8,
              marginBottom: 10,
            }}
          >
            <Button
              title="Trigger PostHog Action"
              onPress={() => {
                posthog.capture('MyComponent loaded', { foo: 'bar' });
              }}
            />
          </View>
        </VContext>
        <VContext title="App" titleIndent>
          <View
            style={{
              padding: 8,
              borderRadius: 8,
              backgroundColor: colors.bgControlsDefault,
            }}
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              Current Version: {nativeApplicationVersion}(
              {Platform.OS === 'ios'
                ? expoConfig?.ios?.buildNumber
                : expoConfig?.android?.versionCode}
              )
            </VText>
            <VText variant="B1" color={colors.textCommonPrimary}>
              Current Space Id: {spaceId}
            </VText>
            <VText variant="B1" color={colors.textCommonPrimary}>
              Current Host: {baseUrl}
            </VText>
            <VText variant="B1" color={colors.textCommonPrimary}>
              Current Theme: {colorScheme}
            </VText>
          </View>
        </VContext>
        <View style={{ minHeight: 100 }} />
      </ScrollView>
    </SafeAreaView>
  );
}
