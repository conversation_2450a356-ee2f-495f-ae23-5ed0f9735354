import { Stack, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import { StaticLoading } from '@/components/Loading';
import { HeaderButton } from '@/components/UI/Button';
import { VInput, VInputWithButton } from '@/components/UI/Input';
import { useGlobalContext } from '@/context/global';

export default function UserSettingsEmailScreen() {
  const router = useRouter();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const isModalPresented = router.canGoBack();

  const [newEmail, setNewEmail] = useState('');
  const [verifyCode, setVerifyCode] = useState('');

  const [loading, setLoading] = useState(false);
  const [isCodeSent, setIsCodeSent] = useState(false);

  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    let interval: any = null;

    if (countdown > 0) {
      interval = setInterval(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [countdown]);

  const sendVerifyCode = async () => {
    setIsCodeSent(true);
    setCountdown(60);

    setLoading(true);

    await trpc.email.sendVerificationMail.mutate({ email: newEmail }).then((res) => {
      console.log('res', res);
      setLoading(false);
    });
  };

  const onSave = async () => {
    await trpc.user.bindEmail
      .mutate({
        email: newEmail,
        verificationCode: verifyCode,
      })
      .then((res) => {
        if (res === true) {
          if (isModalPresented) router.back();
        }
      });
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => <HeaderButton onPress={onSave} title={t.action.save} />,
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <StaticLoading loading={loading} />
        <View
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: 16,
            justifyContent: 'center',
            padding: 16,
            // width: '100%',
          }}
        >
          <VInput
            type="emailAddress"
            title={t.user.new_email}
            value={newEmail}
            onChangeText={setNewEmail}
          />
          <VInputWithButton
            title={t.user.verification_code}
            buttonTitle={
              // eslint-disable-next-line no-nested-ternary
              countdown > 0 ? String(countdown) : isCodeSent ? 'wait' : t.action.send
            }
            value={verifyCode}
            onChangeText={setVerifyCode}
            onPress={sendVerifyCode}
          />
        </View>
      </SafeAreaView>
    </>
  );
}
