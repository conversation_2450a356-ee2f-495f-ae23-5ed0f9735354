import type { <PERSON><PERSON>, RoleVO, TeamVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import { useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { AvatarImage } from '@/components/UI/Avatar';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { usePermission, useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function MemberScreen() {
  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const { memberId } = useLocalSearchParams();

  // console.log('memberId', memberId);

  const spaceId = useSpaceId();

  const permission = usePermission();

  const [member, setMember] = useState<MemberVO | null>(null);
  const [teams, setTeams] = useState<TeamVO[] | null>(null);
  const [roles, setRoles] = useState<RoleVO[] | null>(null);

  const [loading, setLoading] = useState<boolean>(true);

  const getMemberById = async () => {
    setLoading(true);
    await trpc.member.info
      .query({
        spaceId,
        id: memberId as string,
      })
      .then((res) => {
        setMember(res);
      })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      .catch((error) => {
        // member not found
        setMember(null);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTeams = async () => {
    setLoading(true);
    await trpc.member.getTeams
      .query({
        spaceId,
        memberId: memberId as string,
      })
      .then((res) => {
        setTeams(res);
        setLoading(false);
      });
  };

  const getRoles = async () => {
    setLoading(true);
    await trpc.member.getRoles
      .query({
        spaceId,
        memberId: memberId as string,
      })
      .then((res) => {
        setRoles(res);
        setLoading(false);
      });
  };

  const kickMember = async () => {
    Alert.alert(t.space.removal_from_space, t.space.removal_from_space_description, [
      {
        text: t.action.cancel,
        style: 'cancel',
      },
      {
        text: t.action.ok,
        onPress: () => kickMemberAction(),
      },
    ]);
  };

  const kickMemberAction = async () => {
    setLoading(true);
    await trpc.member.kickMembers
      .mutate({
        spaceId,
        memberIds: [memberId as string],
      })
      .then(() => {
        setLoading(false);
      })
      .finally(() => {
        router.back();
      });
  };

  useEffect(() => {
    if (focused) {
      getMemberById();

      getTeams();

      getRoles();
    }
  }, [focused]);

  if (loading && !member) {
    return <PreLoading />;
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={styles.avatar}>
        <AvatarImage size={64} content={member?.avatar} alt={member?.name} withBorder />
        <View style={styles.avatarText}>
          <VText variant="H5" color={colors.textCommonPrimary}>
            {member?.name ?? t.user.no_name}
          </VText>
          <VText variant="B2" color={colors.textCommonTertiary}>
            {member?.email ?? t.user.no_email}
          </VText>
        </View>
      </View>
      <View
        style={{
          backgroundColor: colors.bgControlsDefault,
          borderRadius: 10,
          margin: 16,
        }}
      >
        <TouchableOpacity
          style={{
            alignItems: 'center',
            borderRadius: 10,
            display: 'flex',
            flexDirection: 'row',
            height: 54,
            justifyContent: 'space-between',
            padding: 16,
            width: '100%',
          }}
          onPress={() =>
            router.push({
              pathname: `/(app)/space/${spaceId}/(modal)/team-and-members/index`,
              params: {
                mode: 'add',
                memberId: member?.id,
              },
            })
          }
        >
          <VText variant="B1" color={colors.textCommonPrimary}>
            {t.team.teams}
          </VText>
          <View
            style={{
              alignItems: 'center',
              display: 'flex',
              flexDirection: 'row',
              gap: 8,
            }}
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {teams?.length ?? 0}
            </VText>
            <AntDesign name="right" size={16} color={colors.textCommonPrimary} />
          </View>
        </TouchableOpacity>
        <TextSeparator />
        <TouchableOpacity
          style={{
            alignItems: 'center',
            borderRadius: 10,
            display: 'flex',
            flexDirection: 'row',
            height: 54,
            justifyContent: 'space-between',
            padding: 16,
            width: '100%',
          }}
          onPress={() =>
            router.push({
              pathname: `/(app)/space/${spaceId}/(modal)/role/index`,
              params: {
                mode: 'add',
                memberId: member?.id,
              },
            })
          }
        >
          <VText variant="B1" color={colors.textCommonPrimary}>
            {t.role.roles}
          </VText>
          <View
            style={{
              alignItems: 'center',
              display: 'flex',
              flexDirection: 'row',
              gap: 8,
            }}
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {roles?.length ?? 0}
            </VText>
            <AntDesign name="right" size={16} color={colors.textCommonPrimary} />
          </View>
        </TouchableOpacity>
      </View>
      {permission.updateMember && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            bottom: 50,
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 8,
            height: 48,
            width: '90%',
            alignSelf: 'center',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={kickMember}
        >
          <VText variant="B1" color={colors.textDangerDefault}>
            {t.space.removal_from_space}
          </VText>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  avatar: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    gap: 10,
    justifyContent: 'center',
    marginBottom: 15,
    marginTop: 20,
  },
  avatarText: {
    alignItems: 'center',
    display: 'flex',
    gap: 4,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
  },
});
