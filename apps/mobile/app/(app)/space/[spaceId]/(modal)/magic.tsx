import type { AIIntentUIResolveAIO } from '@bika/types/ai/bo';
import type { AIWizardVO } from '@bika/types/ai/vo';
import { MaterialIcons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import { Image } from 'expo-image';
import { useGlobalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  TextInput,
  View,
} from 'react-native';
import { IntentUIRender } from '@/components/IntentUI/Render';
import { SystemMessage, UserMessage } from '@/components/UI/Message';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';
import { iStringParse } from '@/hooks/usei18n';
import { useVoiceRecognition } from '@/hooks/useVoiceRecognition';

Audio.setAudioModeAsync({
  allowsRecordingIOS: false,
  staysActiveInBackground: false,
  playsInSilentModeIOS: true,
  shouldDuckAndroid: true,
  playThroughEarpieceAndroid: false,
});

export default function MagicModalScreen() {
  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { spaceId } = useGlobalSearchParams();

  const router = useRouter();

  const colors = useColor();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [wizard, setWizard] = useState<AIWizardVO | undefined>(undefined);

  const [isRecording, setIsRecording] = useState<boolean>(false);

  // console.log('wizard', wizard);

  const [humanMessage, setHumanMessage] = useState<string>('');
  const [aiMessage, setAiMessage] = useState<string>('');

  const [isInputText, setIsInputText] = useState(true);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const [isVoiceInputPressed, setIsVoiceInputPressed] = useState<boolean>(false);

  const setDialogAndMessage = (dialog: AIWizardVO) => {
    setWizard(dialog);
    setAiMessage(iStringParse(dialog.lastAiMessage!.text));

    setIsLoading(false);
  };

  useEffect(() => {
    getWizard();
  }, []);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () =>
      setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () =>
      setKeyboardVisible(false),
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const getWizard = async () => {
    setIsLoading(true);

    await trpc.ai.newWizard
      .mutate({
        spaceId: spaceId as string,
        intent: {
          type: 'SEARCH',
        },
      })
      .then((res) => {
        setWizard(res);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const sendUI = async (uiResolve: AIIntentUIResolveAIO) => {
    setAiMessage('');
    setIsLoading(true);

    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId: wizard!.id,
      resolve: {
        type: 'UI',
        uiResolve,
      },
    });

    setIsLoading(false);

    setDialogAndMessage(refreshDialog);
    setAiMessage(iStringParse(refreshDialog.lastAiMessage!.text));
  };

  const sendMessageToAI = async (humanSay: string) => {
    setAiMessage('');
    setIsLoading(true);

    const refreshDialog = await trpc.ai.resolveWizard.mutate({
      wizardId: wizard!.id,
      resolve: {
        type: 'MESSAGE',
        message: humanSay,
      },
    });

    setIsLoading(false);

    setDialogAndMessage(refreshDialog);
    setAiMessage(iStringParse(refreshDialog.lastAiMessage!.text));

    if (refreshDialog.resolutionStatus === 'NOT_STARTED') {
      sendMessageToAI('好的呀');
    }
  };

  const closeModal = () => {
    const isPresented = router.canGoBack();

    if (isPresented) {
      router.back();
    } else {
      router.push('/');
    }
  };

  const { state, startRecognizing, stopRecognizing, destroyRecognizer } = useVoiceRecognition();

  const onVoiceInputSubmit = async () => {
    console.log('submitting voice input');

    if (state.partialResults.length) {
      console.log('Sending message:', state.partialResults[0]);

      sendMessageToAI(state.partialResults[0]);

      setHumanMessage('');
    }
  };

  const onKeyboardInputSubmit = async () => {
    console.log('submitting keyboard input');

    if (humanMessage.trim()) {
      console.log('Sending message:', humanMessage);

      sendMessageToAI(humanMessage);

      setHumanMessage('');
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 45 : 0}
    >
      <View style={{ flex: 1, marginTop: 50 }}>
        <ScrollView style={styles.container}>
          <View
            style={{
              alignItems: 'flex-start',
              display: 'flex',
              justifyContent: 'flex-start',
              flexDirection: 'row',
              gap: 8,
              width: '100%',
              alignSelf: 'center',
            }}
          >
            <Image
              source={require('@/assets/images/icon.png')}
              style={{
                width: 40,
                height: 40,
                borderRadius: 9999,
                borderWidth: 1,
                borderColor: colors.borderCommonDefault,
              }}
            />
            <View
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 8,
                width: '85%',
              }}
            >
              <SystemMessage message={iStringParse(wizard?.lastAiMessage?.text)} />
              <IntentUIRender
                wizard={wizard}
                sendUI={sendUI}
                sendMessage={sendMessageToAI}
                close={closeModal}
              />
            </View>
          </View>
          {wizard?.lastHumanMessage?.text && (
            <View
              style={{
                alignItems: 'flex-start',
                display: 'flex',
                justifyContent: 'flex-end',
                flexDirection: 'row',
                gap: 8,
                width: '85%',
                alignSelf: 'flex-end',
                marginTop: 10,
              }}
            >
              <UserMessage message={iStringParse(wizard?.lastHumanMessage?.text)} />
              <Image
                source={require('@/assets/images/avatar.png')}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 9999,
                  borderWidth: 1,
                  borderColor: colors.borderCommonDefault,
                }}
              />
            </View>
          )}
        </ScrollView>
        <View
          style={{
            alignItems: 'center',
            backgroundColor: colors.bgControlsDefault,
            flexDirection: 'row',
            height: 80,
            paddingBottom: 20,
            paddingHorizontal: 20,
          }}
        >
          <Pressable
            onPress={() => {
              setIsInputText(!isInputText);
            }}
            style={styles.footerStart}
          >
            {isInputText ? (
              <MaterialIcons name="keyboard-voice" size={24} color={colors.textCommonPrimary} />
            ) : (
              <MaterialIcons name="keyboard" size={24} color={colors.textCommonPrimary} />
            )}
          </Pressable>
          <View style={styles.footerCenter}>
            {isInputText ? (
              <TextInput
                style={{
                  alignItems: 'center',
                  backgroundColor: colors.bgCommonDefault,
                  borderRadius: 5,
                  color: colors.textCommonPrimary,
                  display: 'flex',
                  flexDirection: 'row',
                  height: 40,
                  justifyContent: 'center',
                  padding: 10,
                  width: '100%',
                }}
                value={humanMessage}
                onChangeText={(text) => setHumanMessage(text)}
                returnKeyType="send"
                onSubmitEditing={() => onKeyboardInputSubmit()}
              />
            ) : (
              <Pressable
                onPressIn={() => {
                  setIsVoiceInputPressed(true);
                  startRecognizing();
                }}
                onPressOut={() => {
                  setIsVoiceInputPressed(false);
                  stopRecognizing();
                  onVoiceInputSubmit();
                  destroyRecognizer();
                }}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: 10,
                  height: 40,
                  borderRadius: 5,
                  backgroundColor: isVoiceInputPressed
                    ? colors.textBrandActive
                    : colors.textBrandDefault,
                }}
              >
                <MaterialIcons name="keyboard-voice" size={24} color={colors.textCommonPrimary} />
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {state.isRecording ? t.ai.voice_release_to_send : t.ai.voice_hold_to_speak}
                </VText>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 10,
  },
  footerCenter: {
    flex: 1,
    marginHorizontal: 3,
  },
  footerStart: {
    width: 40,
  },
});
