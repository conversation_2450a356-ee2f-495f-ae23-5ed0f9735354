import SegmentedControl from '@react-native-segmented-control/segmented-control';
import { useGlobalSearchParams } from 'expo-router';
import { useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import { CreateData } from '@/components/Create/Data';
import { CreateMission } from '@/components/Create/Mission';
import { CreateNode } from '@/components/Create/Node';
import { useGlobalContext } from '@/context/global';

export default function CreateAnythingScreen() {
  const { createIndex } = useGlobalSearchParams();

  const { locale } = useGlobalContext();

  const { t } = locale;

  const [selectedIndex, setSelectedIndex] = useState<number>(
    parseInt(createIndex as string, 10) || 0,
  );

  return (
    <SafeAreaView style={{ flex: 1, marginVertical: 16 }}>
      <View style={{ paddingHorizontal: 10, flex: 1 }}>
        <SegmentedControl
          values={[
            t.mission.mission,
            // t.agenda.agenda,
            t.data.data,
            t.node.node,
          ]}
          selectedIndex={selectedIndex}
          onChange={(event) => setSelectedIndex(event.nativeEvent.selectedSegmentIndex)}
          style={{ marginBottom: 16 }}
        />

        <View
          style={{
            flex: 1,
          }}
        >
          <CreateRender value={selectedIndex} />
        </View>
      </View>
    </SafeAreaView>
  );
}

const CreateRender = ({ value }: { value: number }) => {
  switch (value) {
    case 0:
      return <CreateMission />;
    // case 1:
    // return <CreateAgenda />;
    case 1:
      return <CreateData />;
    case 2:
      return <CreateNode />;
    default:
      return null;
  }
};
