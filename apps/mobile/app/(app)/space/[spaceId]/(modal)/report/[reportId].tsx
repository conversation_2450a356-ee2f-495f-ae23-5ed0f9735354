import type { ReportDetailVO } from '@bika/types/report/vo';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, ScrollView, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { HTMLRender } from '@/components/Render';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';
import { useDwellTime } from '@/utils/time';

export default function ReportIdScreen() {
  const router = useRouter();

  const colors = useColor();

  const { reportId } = useLocalSearchParams();

  const dwellTimeSeconds = useDwellTime();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { showSnackbar } = useAction();

  const [reportDetail, setReportDetail] = useState<ReportDetailVO | null>(null);

  const [loading, setLoading] = useState<boolean>(true);

  const getReportDetail = async () => {
    setLoading(true);
    await trpc.report.fetchReport
      .query({
        reportId: reportId as string,
      })
      .then((res) => {
        setReportDetail(res);
        setLoading(false);
      });
  };

  const markAsRead = async () => {
    try {
      trpc.report.markRead.mutate({
        reportId: reportId as string,
        dwellTime: dwellTimeSeconds,
      });
      router.back();
    } catch (error) {
      showSnackbar(t.error.error, ActionType.ERROR);
    }
  };

  useEffect(() => {
    getReportDetail();
  }, []);

  return (
    <>
      <Stack.Screen
        options={{
          title: reportDetail?.subject ?? ' ',
        }}
      />
      {loading ? (
        <PreLoading />
      ) : (
        <SafeAreaView style={{ flex: 1 }}>
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              marginHorizontal: 16,
              paddingBottom: 60,
            }}
          >
            <HTMLRender content={reportDetail?.body} />
          </ScrollView>
          <View
            style={{
              position: 'absolute',
              bottom: 10,
              width: '100%',
              padding: 16,
            }}
          >
            <Pressable
              style={{
                borderWidth: reportDetail?.read ? 1 : 0,
                borderColor: reportDetail?.read ? colors.borderBrandDefault : 'transparent',
                backgroundColor: reportDetail?.read
                  ? colors.bgCommonDefault
                  : colors.bgBrandDefault,
                width: '100%',
                height: 48,
                borderRadius: 8,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={markAsRead}
              disabled={reportDetail?.read}
            >
              <VText
                variant="B1"
                color={reportDetail?.read ? colors.textCommonPrimary : colors.textStaticPrimary}
              >
                {reportDetail?.read ? t.report.read : t.report.mark_as_read}
              </VText>
            </Pressable>
          </View>
        </SafeAreaView>
      )}
    </>
  );
}
