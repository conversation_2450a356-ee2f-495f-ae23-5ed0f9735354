import * as Notifications from 'expo-notifications';
import { useNavigation, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { Platform, SafeAreaView, StyleSheet, Switch, View } from 'react-native';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';

type NotificationType = 'SMS' | 'email' | 'push';

interface NotificationSettings {
  SMS: boolean;
  email: boolean;
  push: boolean;
}

export default function NotificationSettingsScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const [isSystemNotificationEnabled, setIsSystemNotificationEnabled] = useState<boolean>(true);

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings | null>(
    null,
  );

  const getNotificationSettings = async () => {
    await trpc.user.getUserSettings.query().then((data) => {
      return setNotificationSettings(data?.notification as NotificationSettings);
    });
  };

  const getSystemNotificationStatus = async () => {
    await Notifications.getPermissionsAsync().then((data) => {
      setIsSystemNotificationEnabled(data.granted);
    });
  };

  const changeNotificationSettings = async (type: NotificationType) => {
    if (notificationSettings) {
      const newNotificationSettings = {
        ...notificationSettings,
        [type]: !notificationSettings[type],
      };
      setNotificationSettings(newNotificationSettings);

      await trpc.user.updateUserSettings.mutate({
        notification: newNotificationSettings,
      });
    }
  };

  useEffect(() => {
    if (focused) {
      getNotificationSettings();

      getSystemNotificationStatus();
    }
  }, [focused]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={Platform.OS === 'ios' ? 'light' : 'auto'} />
      <View
        style={{
          paddingHorizontal: 16,
          width: '100%',
          gap: 10,
        }}
      >
        <VContext title={t.notification.app_notification} titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.notification.app_push_notification}
              </VText>
              <View style={styles.itemContent}>
                <Switch
                  trackColor={{
                    false: colors.textCommonDisabled,
                    true: colors.textBrandDefault,
                  }}
                  ios_backgroundColor={colors.bgCommonDefault}
                  value={isSystemNotificationEnabled}
                  onChange={() => router.push('app-settings://')}
                />
              </View>
            </View>
          </View>
        </VContext>
        <VContext title={t.notification.notification_type} titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.notification.mail_notification}
              </VText>
              <View style={styles.itemContent}>
                <Switch
                  trackColor={{
                    false: colors.textCommonDisabled,
                    true: colors.textBrandDefault,
                  }}
                  ios_backgroundColor={colors.bgCommonDefault}
                  value={notificationSettings?.email ?? false}
                  onChange={() => changeNotificationSettings('email')}
                />
              </View>
            </View>
            <TextSeparator />
            <View style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.notification.sms_notification}
              </VText>
              <View style={styles.itemContent}>
                <Switch
                  trackColor={{
                    false: colors.textCommonDisabled,
                    true: colors.textBrandDefault,
                  }}
                  ios_backgroundColor={colors.bgCommonDefault}
                  value={notificationSettings?.SMS ?? false}
                  onChange={() => changeNotificationSettings('SMS')}
                />
              </View>
            </View>
            <TextSeparator />
            <View style={styles.headerItem}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.notification.browser_notification}
              </VText>
              <View style={styles.itemContent}>
                <Switch
                  trackColor={{
                    false: colors.textCommonDisabled,
                    true: colors.textBrandDefault,
                  }}
                  ios_backgroundColor={colors.bgCommonDefault}
                  value={notificationSettings?.push ?? false}
                  onChange={() => changeNotificationSettings('push')}
                />
              </View>
            </View>
          </View>
        </VContext>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
  },
  headerItem: {
    alignItems: 'center',
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    minHeight: 54,
    paddingHorizontal: 16,
    width: '100%',
  },
  itemContent: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
});
