import type { NotificationVO, SpaceNotificationVO } from '@bika/types/notification/vo';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import { Badge } from '@rneui/themed';
import { FlashList } from '@shopify/flash-list';
import { Stack, useNavigation, useRouter } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { useEffect, useState } from 'react';
import {
  Alert,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { PreLoading } from '@/components/Loading';
import { FullSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { formatDate } from '@/utils/date';
import { handleRedirect } from '@/utils/notification';

export default function NotificationScreen() {
  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { showPrompt } = useAction();

  const [selectedIndex, setSelectedIndex] = useState(0);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const [notifications, setNotifications] = useState<NotificationVO[] | null>(null);

  const getNotifications = async () => {
    setLoading(true);
    await trpc.notification.list.query().then((res) => {
      setNotifications(res);

      setLoading(false);
    });
  };

  useEffect(() => {
    if (focused) {
      getNotifications();
    }
  }, [focused]);

  const isUnreadNotifications = notifications?.filter((notification) => !notification.read);

  const doRefresh = () => {
    setRefreshing(true);
    getNotifications().then(() => {
      setRefreshing(false);
    });
  };

  const markOneAsRead = async (notification: NotificationVO) => {
    await trpc.notification.read.mutate({
      notificationIds: [notification.id],
    });
  };

  const markAllAsRead = async () => {
    await trpc.notification.markAllAsRead
      .mutate({
        spaceId,
      })
      .then(() => {
        doRefresh();

        showPrompt(t.notification.all_notifications_marked_as_read);
      });
  };

  const onReadPress = async () => {
    Alert.alert(
      t.notification.clean_all_notifications,
      t.notification.confirm_to_clean_all_notifications,
      [
        { text: t.action.cancel, style: 'cancel' },
        {
          text: t.action.ok,
          onPress: () => {
            markAllAsRead();
          },
        },
      ],
    );
  };

  const onNotificationPress = async (notification: NotificationVO) => {
    markOneAsRead(notification);

    if ((notification as any).space.id !== spaceId) {
      await AsyncStorage.setItem('spaceId', (notification as any).space.id).then(() => {
        SecureStore.setItem('next_app_action', JSON.stringify(notification));
      });
    }

    return router.push(handleRedirect(notification) as any);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <View className="flex flex-row" style={{ gap: 20 }}>
              <TouchableOpacity onPress={onReadPress}>
                <MaterialCommunityIcons name="read" size={20} color={colors.textCommonPrimary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  router.push(`/(app)/space/${spaceId}/(modal)/notification/settings`);
                }}
              >
                <Feather name="settings" size={20} color={colors.textCommonPrimary} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <View
          style={{
            paddingHorizontal: 16,
            paddingVertical: 10,
            flex: 1,
          }}
        >
          <SegmentedControl
            values={[
              `${t.notification.all} (${notifications?.length ?? 0})`,
              `${t.notification.unread} (${notifications?.filter((n) => !n.read)?.length ?? 0})`,
            ]}
            selectedIndex={selectedIndex}
            onChange={(event) => {
              setSelectedIndex(event.nativeEvent.selectedSegmentIndex);
            }}
          />
          {loading && !notifications ? (
            <PreLoading />
          ) : (
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ flexGrow: 1, marginHorizontal: 4 }}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={doRefresh} />}
            >
              <FlashList
                data={selectedIndex === 0 ? notifications : isUnreadNotifications}
                estimatedItemSize={200}
                keyExtractor={(item) => item.id}
                ItemSeparatorComponent={FullSeparator}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    className="flex w-full flex-col"
                    style={{
                      paddingVertical: 8,
                      marginVertical: 4,
                      borderRadius: 8,
                      gap: 8,
                    }}
                    onPress={() => onNotificationPress(item)}
                  >
                    {!item.read && (
                      <Badge
                        status="error"
                        containerStyle={{
                          position: 'absolute',
                          top: 5,
                          right: 2,
                        }}
                      />
                    )}
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {item.title}
                    </VText>
                    <View className="flex flex-row justify-between">
                      <VText variant="B4" color={colors.textCommonTertiary}>
                        {(item as SpaceNotificationVO)?.space?.name}
                      </VText>
                      <VText variant="B4" color={colors.textCommonTertiary}>
                        {formatDate(item.createdAt)}
                      </VText>
                    </View>
                  </TouchableOpacity>
                )}
              />
            </ScrollView>
          )}
        </View>
      </SafeAreaView>
    </>
  );
}
