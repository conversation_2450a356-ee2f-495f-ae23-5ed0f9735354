import type { SessionVO } from '@bika/types/user/vo';
import { FlashList } from '@shopify/flash-list';
import { useNavigation } from 'expo-router';
import { useEffect, useState } from 'react';
import { RefreshControl, SafeAreaView, ScrollView, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { FullSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';

export default function AccountLoginScreen() {
  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const [loading, setLoading] = useState(true);

  const [sessions, setSessions] = useState<SessionVO[] | null>(null);

  const getSessions = async () => {
    setLoading(true);
    await trpc.user.getSessions.query().then((res) => {
      setSessions(res);
      setLoading(false);
    });
  };

  useEffect(() => {
    if (focused) {
      getSessions();
    }
  }, [focused]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          paddingHorizontal: 16,
          paddingVertical: 10,
          flex: 1,
        }}
      >
        {loading && !sessions ? (
          <PreLoading />
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1, marginHorizontal: 4 }}
            refreshControl={<RefreshControl refreshing={loading} onRefresh={() => getSessions()} />}
          >
            <FlashList
              data={sessions}
              estimatedItemSize={200}
              keyExtractor={(item) => item.id}
              ItemSeparatorComponent={FullSeparator}
              renderItem={({ item }) => (
                <View
                  className="flex w-full flex-col"
                  style={{
                    paddingVertical: 8,
                    marginVertical: 4,
                    borderRadius: 8,
                    gap: 8,
                  }}
                >
                  <View className="flex flex-row items-center justify-between">
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {item?.hostname}
                    </VText>
                    {(item as any)?.isCurrent && (
                      <VText variant="B1" color={colors.textSuccessDefault}>
                        {t.action.current}
                      </VText>
                    )}
                  </View>
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {item?.ip}
                  </VText>
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {new Date(item?.expiresAt).toLocaleString()}
                  </VText>
                </View>
              )}
            />
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}
