import type { UserCoinsVO, UserCoinTransactionVO } from '@bika/types/user/vo';
import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, ScrollView, View } from 'react-native';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { IconSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';
import { copyText, shareText } from '@/utils/text';

export default function AccountReferralScreen() {
  const router = useRouter();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const spaceId = useSpaceId();

  const [loading, setLoading] = useState(true);

  const [coins, setCoins] = useState<UserCoinsVO | null>(null);

  const [referralCode, setReferralCode] = useState<string>('');

  const [coinTransactions, setCoinTransactions] = useState<UserCoinTransactionVO[] | null>(null);

  const getCoins = async () => {
    setLoading(true);
    await trpc.user.coins.query().then((res) => {
      setCoins(res);
      setLoading(false);
    });
  };

  const getReferralCode = async () => {
    setLoading(true);
    await trpc.user.referralCode.query().then((res) => {
      setReferralCode(res.userReferralCode);
      setLoading(false);
    });
  };

  const getCoinTransactions = async () => {
    setLoading(true);
    await trpc.user.coinTransactions.query().then((res) => {
      setCoinTransactions(res);
      setLoading(false);
    });
  };

  useEffect(() => {
    if (focused) {
      getCoins();

      getReferralCode();

      getCoinTransactions();
    }
  }, [focused]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          paddingHorizontal: 16,
          flex: 1,
        }}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
            marginHorizontal: 4,
            gap: 10,
          }}
        >
          <View style={{ gap: 5 }}>
            <VText variant="H5" color={colors.textCommonPrimary}>
              {t.referral.your_bika_coins}
            </VText>
            <View className="flex flex-row items-center gap-2">
              <Image
                source={require('@/assets/images/coin.png')}
                style={{ width: 30, height: 30 }}
              />
              <VText variant="H4" color={colors.rainbowYellow5}>
                {coins?.balance}
              </VText>
            </View>
            <VText variant="B3" color={colors.textCommonTertiary}>
              {t.referral.bika_coins_description}
            </VText>
          </View>
          <View
            className="flex flex-row justify-between"
            style={{
              backgroundColor: colors.rainbowBrown1,
              padding: 16,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: colors.rainbowBrown2,
            }}
          >
            <View style={{ gap: 2 }}>
              <VText variant="B4" color={colors.textCommonTertiary}>
                {t.referral.current_space_plan}
              </VText>
              <VText variant="H7" color={colors.textCommonPrimary}>
                FREE
              </VText>
            </View>
            <View style={{ gap: 10 }}>
              <Pressable
                className="flex items-center justify-center"
                style={{
                  backgroundColor: colors.rainbowBrown4,
                  padding: 8,
                  borderRadius: 9999,
                }}
                // onPress={() =>
                //   router.push(`/(app)/space/${spaceId}/(modal)/account/top-up`)
                // }
                onPress={COMING_SOON}
              >
                <VText variant="B3" color={colors.textStaticPrimary}>
                  {t.upgrade.upgrade}
                </VText>
              </Pressable>
              <Pressable
                className="flex items-center justify-center"
                style={{
                  borderWidth: 1,
                  borderColor: colors.borderCommonDefault,
                  padding: 8,
                  borderRadius: 9999,
                }}
                onPress={COMING_SOON}
              >
                <VText variant="H8" color={colors.textCommonTertiary}>
                  {t.referral.check_usage}
                </VText>
              </Pressable>
            </View>
          </View>
          <View style={{ gap: 10 }}>
            <VText variant="H5" color={colors.textCommonPrimary}>
              {t.referral.earn_bika_coins}
            </VText>
            <View style={{ gap: 6 }}>
              <View className="flex flex-row items-center gap-2">
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {t.referral.method_1}
                </VText>
                <View className="flex flex-row items-center space-x-1" style={{ gap: 3 }}>
                  <Image
                    source={require('@/assets/images/coin.png')}
                    style={{ width: 20, height: 20 }}
                  />
                  <VText variant="H4" color={colors.rainbowYellow5}>
                    +1,000
                  </VText>
                </View>
              </View>
              <View
                style={{
                  backgroundColor: colors.bgControlsDefault,
                  padding: 16,
                  borderRadius: 8,
                }}
                className="flex flex-row items-center justify-between gap-2"
              >
                <VText variant="H7" color={colors.textCommonPrimary}>
                  {`${getBaseUrl()}/${referralCode}`}
                </VText>
                <Pressable onPress={() => shareText(`${getBaseUrl()}/${referralCode}`)}>
                  <AntDesign name="sharealt" size={16} color={colors.textBrandDefault} />
                </Pressable>
              </View>
            </View>
            <View style={{ gap: 6 }}>
              <View className="flex flex-row items-center gap-2">
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {t.referral.method_2}
                </VText>
                <View className="flex flex-row items-center space-x-1" style={{ gap: 3 }}>
                  <Image
                    source={require('@/assets/images/coin.png')}
                    style={{ width: 20, height: 20 }}
                  />
                  <VText variant="H4" color={colors.rainbowYellow5}>
                    +1,000
                  </VText>
                </View>
              </View>
              <View
                style={{
                  backgroundColor: colors.bgControlsDefault,
                  padding: 16,
                  borderRadius: 8,
                }}
                className="flex flex-row items-center justify-between gap-2"
              >
                <VText variant="H7" color={colors.textCommonPrimary}>
                  {referralCode}
                </VText>
                <Pressable onPress={() => copyText(referralCode)}>
                  <AntDesign name="sharealt" size={16} color={colors.textBrandDefault} />
                </Pressable>
              </View>
            </View>
            <View style={{ gap: 6 }}>
              <View className="flex flex-row items-center gap-2">
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {t.referral.method_3}
                </VText>
                <View className="flex flex-row items-center space-x-1" style={{ gap: 3 }}>
                  <Image
                    source={require('@/assets/images/coin.png')}
                    style={{ width: 20, height: 20 }}
                  />
                  <VText variant="H4" color={colors.rainbowYellow5}>
                    +1,000
                  </VText>
                </View>
              </View>
              <Pressable
                style={{
                  backgroundColor: colors.bgControlsDefault,
                  padding: 16,
                  borderRadius: 8,
                }}
                onPress={() => {
                  router.push(`/(app)/space/${spaceId}/(modal)/account/invite`);
                }}
              >
                <VText variant="H7" color={colors.textBrandDefault}>
                  {t.invite.invite_members}
                </VText>
              </Pressable>
            </View>
            <View style={{ gap: 6 }}>
              <View className="flex flex-row items-center gap-2">
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {t.referral.method_4}
                </VText>
                <View className="flex flex-row items-center space-x-1" style={{ gap: 3 }}>
                  <Image
                    source={require('@/assets/images/coin.png')}
                    style={{ width: 20, height: 20 }}
                  />
                  <VText variant="H4" color={colors.rainbowYellow5}>
                    +1,000
                  </VText>
                </View>
              </View>
              <Image
                source={require('@/assets/images/app-store-download.png')}
                style={{ width: 140, height: 40 }}
              />
            </View>
          </View>
          <View style={{ gap: 6 }}>
            <View className="flex flex-row items-center justify-between">
              <VText variant="H5" color={colors.textCommonPrimary}>
                {t.referral.reward_history}
              </VText>
              <VText variant="B3" color={colors.textCommonPrimary}>
                {t.referral.total}
                {coinTransactions
                  ?.map((coinTransaction) => coinTransaction.amount)
                  .reduce((a, b) => a + b, 0)}
              </VText>
            </View>
            <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                paddingHorizontal: 16,
                borderRadius: 8,
                flex: 1,
              }}
            >
              <FlashList
                data={coinTransactions}
                ItemSeparatorComponent={IconSeparator}
                renderItem={({ item }) => (
                  <View
                    style={{ minHeight: 54 }}
                    className="flex flex-row items-center justify-between"
                  >
                    <View
                      style={{
                        padding: 8,
                        backgroundColor: colors.bgControlsActive,
                        borderRadius: 9999,
                        borderWidth: 1,
                        borderColor: colors.borderCommonDefault,
                      }}
                    >
                      <AntDesign name="adduser" size={20} color={colors.textCommonTertiary} />
                    </View>
                    <View className="flex w-10/12 flex-row items-center justify-between">
                      <View>
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {/* {item.type} */}
                          {t.settings.coin_rewards.invite_member_rewards_coin}
                        </VText>
                        <VText variant="B4" color={colors.textCommonTertiary}>
                          {new Date(item.createdAt).toLocaleString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </VText>
                      </View>
                      <View>
                        <VText variant="H7" color={colors.textCommonSecondary}>
                          +{item.amount}
                        </VText>
                      </View>
                    </View>
                  </View>
                )}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}
