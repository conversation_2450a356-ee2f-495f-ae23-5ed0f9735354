/* eslint-disable no-nested-ternary */
import type { SpaceLinkInvitationVO, SpaceRenderVO } from '@bika/types/space/vo';
import type { RoleVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, Pressable, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { AvatarImage } from '@/components/UI/Avatar';
import { FullSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceContext, useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';
import { copyText, shareText } from '@/utils/text';

export default function InviteScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const { rootTeam } = useSpaceContext();

  const localParams = useLocalSearchParams();

  const { withTeam, withRole } = localParams;

  const [spaceDetail, setSpaceDetail] = useState<SpaceRenderVO | null>(null);

  const [invitations, setInvitations] = useState<SpaceLinkInvitationVO[] | null>(null);

  const [referralCode, setReferralCode] = useState<string | null>(null);

  const getSpaceDetail = async () => {
    await trpc.space.info
      .query({
        identify: spaceId,
      })
      .then((res) => {
        setSpaceDetail(res);
      });
  };

  const getInvitationLink = async () => {
    await trpc.linkInvitation.list
      .query({
        spaceId,
      })
      .then((res) => {
        setInvitations(res);
      });
  };

  const onLinkCreate = async () => {
    if (withTeam) {
      await trpc.linkInvitation.create
        .mutate({
          spaceId,
          teamId: JSON.parse(JSON.parse(JSON.stringify(withTeam))).id,
          roleIds: JSON.parse(JSON.parse(JSON.stringify(withRole))).map((role: RoleVO) => role.id),
        })
        .then(() => {
          getInvitationLink();
        });
    } else {
      await trpc.linkInvitation.create
        .mutate({
          spaceId,
          teamId: rootTeam!.id,
        })
        .then(() => {
          getInvitationLink();
        });
    }
  };

  const deleteLink = (token: string) => {
    Alert.alert(t.delete.confirm_deletion, t.delete.confirm_to_delete_this_link, [
      { text: t.cancel, style: 'cancel' },
      {
        text: t.confirm,
        style: 'destructive',
        onPress: () => {
          confirmDelete(token);
        },
      },
    ]);
  };

  const confirmDelete = async (token: string) => {
    await trpc.linkInvitation.delete
      .mutate({
        spaceId,
        inviteToken: token,
      })
      .then(() => {
        getInvitationLink();
      });
  };

  const getReferralCode = async () => {
    await trpc.user.referralCode.query().then((res) => {
      setReferralCode(res.userReferralCode);
    });
  };

  useEffect(() => {
    getSpaceDetail();

    getInvitationLink();

    getReferralCode();
  }, []);

  console.log('invitations', invitations);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        margin: 16,
        gap: 20,
      }}
    >
      <View className="gap-1">
        <VText variant="H5" color={colors.textCommonPrimary}>
          {t.invite.create_public_invitation_link}
        </VText>
        <VText variant="B2" color={colors.textCommonTertiary}>
          {t.invite.create_invite_description} 1,000
        </VText>
      </View>
      <View
        style={{
          backgroundColor: colors.bgControlsDefault,
          borderRadius: 10,
          padding: 16,
          gap: 16,
        }}
      >
        <View
          style={{
            gap: 16,
          }}
        >
          <TouchableOpacity
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
            onPress={() =>
              router.push({
                pathname: `/(app)/space/${spaceId}/(modal)/team-and-members/index`,
                params: {
                  mode: 'select',
                  withRole: withRole ?? null,
                },
              })
            }
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.team.teams}
            </VText>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 4,
                alignItems: 'center',
              }}
            >
              <VText variant="B1" color={colors.textCommonTertiary}>
                {withTeam
                  ? JSON.parse(JSON.parse(JSON.stringify(withTeam))).name
                  : spaceId
                    ? spaceDetail?.name
                    : t.team.select_team}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </TouchableOpacity>
          <FullSeparator />
          <TouchableOpacity
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
            onPress={() =>
              router.push({
                pathname: `/(app)/space/${spaceId}/(modal)/role/index`,
                params: {
                  mode: 'select',
                  withTeam: withTeam ?? null,
                },
              })
            }
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.role.role}
            </VText>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 4,
                alignItems: 'center',
              }}
            >
              <VText variant="B1" color={colors.textCommonTertiary}>
                {(withRole &&
                  JSON.parse(JSON.parse(JSON.stringify(withRole))).map(
                    (role: RoleVO) => `${role.name}, `,
                  )) ??
                  t.role.select_role}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={{
            backgroundColor: colors.textBrandDefault,
            height: 40,
            borderRadius: 4,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={onLinkCreate}
        >
          <VText variant="B1" color={colors.textStaticPrimary}>
            {t.action.create}
          </VText>
        </TouchableOpacity>
      </View>
      {invitations && invitations.length > 0 && (
        <GestureHandlerRootView style={{ flex: 1 }}>
          <View className="gap-1">
            <VText variant="H5" color={colors.textCommonPrimary}>
              {t.invite.created_public_invitation_link}
            </VText>
            <VText variant="B2" color={colors.textCommonTertiary}>
              {t.invite.invite_description} 1,000
            </VText>
          </View>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
            }}
          >
            <FlashList
              data={invitations}
              renderItem={({ item }) => (
                <View className="my-2 gap-1">
                  <View className="flex flex-row items-center gap-1">
                    <Pressable
                      className="flex flex-row items-center"
                      onPress={() =>
                        router.push(`/space/${spaceId}/(modal)/member/${item.member.id}`)
                      }
                      style={{
                        backgroundColor: colors.bgTagDefault,
                        padding: 2,
                        paddingRight: 4,
                        borderRadius: 999,
                        gap: 4,
                        flexShrink: 1,
                      }}
                    >
                      <AvatarImage
                        size={24}
                        content={item.member?.avatar}
                        alt={item.member?.name}
                      />

                      <VText variant="B2" color={colors.textCommonPrimary}>
                        {item.member.name}
                      </VText>
                    </Pressable>
                    <VText variant="B2" color={colors.textCommonPrimary}>
                      {t.invite.invite_link_created_by}
                    </VText>
                  </View>
                  <Swipeable
                    renderRightActions={() => (
                      <TouchableOpacity
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                        onPress={() => deleteLink(item.token)}
                      >
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {t.action.delete}
                        </VText>
                      </TouchableOpacity>
                    )}
                  >
                    <TouchableOpacity
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        minHeight: 54,
                        paddingHorizontal: 16,
                        backgroundColor: colors.bgControlsDefault,
                        borderRadius: 8,
                      }}
                      onPress={() => copyText(`${getBaseUrl()}/space/join/${item.token}`)}
                    >
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          width: '90%',
                          gap: 2,
                        }}
                      >
                        <VText variant="B3" color={colors.textCommonPrimary}>
                          {`${getBaseUrl()}/space/join/${item.token}?referralCode=${referralCode}`}
                        </VText>
                      </View>
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          gap: 16,
                        }}
                      >
                        <Pressable
                          onPress={() => shareText(`${getBaseUrl()}/space/join/${item.token}`)}
                        >
                          <AntDesign name="sharealt" size={20} color={colors.textBrandDefault} />
                        </Pressable>
                      </View>
                    </TouchableOpacity>
                  </Swipeable>
                </View>
              )}
              keyExtractor={(item) => item.token}
              scrollEnabled={false}
              estimatedItemSize={54}
            />
          </ScrollView>
        </GestureHandlerRootView>
      )}
    </SafeAreaView>
  );
}
