import { AntDesign, Ionicons } from '@expo/vector-icons';
import { useNavigation, useRouter } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useEffect, useState } from 'react';
import { RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { useGlobalContext } from '@/context/global';
import { getSessionCookie } from '@/context/global/utils/session';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

WebBrowser.maybeCompleteAuthSession();

export default function AccountBindingScreen() {
  const router = useRouter();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const sessionToken = getSessionCookie();

  const spaceId = useSpaceId();

  const { showAlert } = useAction();

  const [loading, setLoading] = useState(true);

  const [accountBinding, setAccountBinding] = useState<any | null>(null);

  const getAccountBinding = async () => {
    setLoading(true);
    await trpc.user.getLinkedExternalAccounts.query().then((res) => {
      setAccountBinding(res);
      setLoading(false);
    });
  };

  const onAuthPress = async (provider: string) => {
    try {
      const result = await WebBrowser.openAuthSessionAsync(
        `${getBaseUrl()}/api/auth/${provider}?linkExternalUser=1&sessionToken=${sessionToken}&redirect=bika://space/${spaceId}`,
      );

      console.log('result', result);

      if (result.type !== 'success') {
        showAlert(t.account.account_binding_error, t.account.account_binding_error_description);
      } else {
        showAlert(t.account.account_binding_success);

        router.back();
      }
    } catch (err) {
      showAlert(t.account.account_binding_error);
    }
  };

  useEffect(() => {
    if (focused) {
      getAccountBinding();
    }
  }, [focused]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          paddingHorizontal: 16,
          paddingVertical: 10,
          flex: 1,
        }}
      >
        {loading && !accountBinding ? (
          <PreLoading />
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
              marginHorizontal: 4,
              gap: 10,
            }}
            refreshControl={
              <RefreshControl refreshing={loading} onRefresh={() => getAccountBinding()} />
            }
          >
            <View
              className="flex flex-row items-center justify-between"
              style={{
                backgroundColor: colors.bgControlsDefault,
                padding: 16,
                borderRadius: 8,
              }}
            >
              <View className="flex flex-row items-center" style={{ gap: 10 }}>
                <AntDesign name="apple1" size={24} color={colors.textCommonPrimary} />
                <VText variant="H6" color={colors.textCommonPrimary}>
                  Apple
                </VText>
              </View>
              <TouchableOpacity
                style={{
                  backgroundColor: accountBinding.apple
                    ? colors.bgControlsHover
                    : colors.bgControlsActive,
                  borderRadius: 8,
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                }}
                onPress={() => onAuthPress('apple')}
                disabled={accountBinding.apple}
              >
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {accountBinding.apple ? t.account.bound : t.account.bind_now}
                </VText>
              </TouchableOpacity>
            </View>
            <View
              className="flex flex-row items-center justify-between"
              style={{
                backgroundColor: colors.bgControlsDefault,
                padding: 16,
                borderRadius: 8,
              }}
            >
              <View className="flex flex-row items-center" style={{ gap: 10 }}>
                <AntDesign name="google" size={24} color={colors.textCommonPrimary} />
                <VText variant="H6" color={colors.textCommonPrimary}>
                  Google
                </VText>
              </View>
              <TouchableOpacity
                style={{
                  backgroundColor: accountBinding.google
                    ? colors.bgControlsHover
                    : colors.bgControlsActive,
                  borderRadius: 8,
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                }}
                onPress={() => onAuthPress('google')}
                disabled={accountBinding.google}
              >
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {accountBinding.google ? t.account.bound : t.account.bind_now}
                </VText>
              </TouchableOpacity>
            </View>
            <View
              className="flex flex-row items-center justify-between"
              style={{
                backgroundColor: colors.bgControlsDefault,
                padding: 16,
                borderRadius: 8,
              }}
            >
              <View className="flex flex-row items-center" style={{ gap: 10 }}>
                <AntDesign name="github" size={24} color={colors.textCommonPrimary} />
                <VText variant="H6" color={colors.textCommonPrimary}>
                  GitHub
                </VText>
              </View>
              <TouchableOpacity
                style={{
                  backgroundColor: accountBinding.github
                    ? colors.bgControlsHover
                    : colors.bgControlsActive,
                  borderRadius: 8,
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                }}
                onPress={() => onAuthPress('github')}
                disabled={accountBinding.github}
              >
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {accountBinding.github ? t.account.bound : t.account.bind_now}
                </VText>
              </TouchableOpacity>
            </View>
            <View
              className="flex flex-row items-center justify-between"
              style={{
                backgroundColor: colors.bgControlsDefault,
                padding: 16,
                borderRadius: 8,
              }}
            >
              <View className="flex flex-row items-center" style={{ gap: 10 }}>
                <Ionicons name="logo-microsoft" size={24} color={colors.textCommonPrimary} />
                <VText variant="H6" color={colors.textCommonPrimary}>
                  Microsoft
                </VText>
              </View>
              <TouchableOpacity
                style={{
                  backgroundColor: accountBinding.github
                    ? colors.bgControlsHover
                    : colors.bgControlsActive,
                  borderRadius: 8,
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                }}
                onPress={() => onAuthPress('microsoft')}
                disabled={accountBinding.microsoft}
              >
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {accountBinding.microsoft ? t.account.bound : t.account.bind_now}
                </VText>
              </TouchableOpacity>
            </View>
            <View
              className="flex flex-row items-center justify-between"
              style={{
                backgroundColor: colors.bgControlsDefault,
                padding: 16,
                borderRadius: 8,
              }}
            >
              <View className="flex flex-row items-center" style={{ gap: 10 }}>
                <Ionicons name="logo-wechat" size={24} color={colors.textCommonPrimary} />
                <VText variant="H6" color={colors.textCommonPrimary}>
                  WeChat
                </VText>
              </View>
              <TouchableOpacity
                style={{
                  backgroundColor: accountBinding.github
                    ? colors.bgControlsHover
                    : colors.bgControlsActive,
                  borderRadius: 8,
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                }}
                onPress={() => onAuthPress('wechat')}
                disabled={accountBinding.wechat}
              >
                <VText variant="B2" color={colors.textCommonPrimary}>
                  {accountBinding.wechat ? t.account.bound : t.account.bind_now}
                </VText>
              </TouchableOpacity>
            </View>
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}
