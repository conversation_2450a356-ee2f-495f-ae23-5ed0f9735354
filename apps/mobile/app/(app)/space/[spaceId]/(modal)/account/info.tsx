import type { MemberVO } from '@bika/types/unit/vo';
import { AntDesign } from '@expo/vector-icons';
import { useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { PreLoading } from '@/components/Loading';
import { AvatarImage } from '@/components/UI/Avatar';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useAction } from '@/context/action/provider';
import { useGlobalContext } from '@/context/global';
import { destroySessionCookie, resetTheme } from '@/context/global/utils/session';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';
import { copyText } from '@/utils/text';

export default function AccountInfoScreen() {
  const { auth, trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { refetchMe, logout } = auth;

  const { reloadApp } = useAction();

  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const spaceId = useSpaceId();

  const [loading, setLoading] = useState<boolean>(true);

  const [referralCode, setReferralCode] = useState<string>('');

  const [myInfo, setMyInfo] = useState<MemberVO | null>(null);

  const fetchMember = async () => {
    await trpc.my.info
      .query({
        spaceId,
      })
      .then((res) => {
        setMyInfo(res);
      });
  };

  const getReferralCode = async () => {
    setLoading(true);
    await trpc.user.referralCode.query().then((res) => {
      setReferralCode(res.userReferralCode);
      setLoading(false);
    });
  };

  const updateNickname = async (newName: string) => {
    await trpc.user.updateUserInfo.mutate({
      name: newName,
    });
  };

  const updateMemberName = async (newName: string) => {
    await trpc.my.update.mutate({
      spaceId,
      name: newName,
    });
  };

  const handleChangeNickname = () => {
    Alert.prompt(
      'Change Nickname',
      'Enter your new nickname:',
      (text) => {
        updateNickname(text);
      },
      undefined,
      myInfo?.name,
    );
  };

  const handleChangeMemberName = () => {
    Alert.prompt(
      'Change Member Name',
      'Enter your new member name:',
      (text) => {
        updateMemberName(text);
      },
      undefined,
      myInfo?.name,
    );
  };

  const onLogoutPress = async () => {
    logout().then(() => {
      console.log('logout');

      destroySessionCookie();

      resetTheme();
    });

    await refetchMe().then(() => {
      console.log('refreshMe');
    });

    router.push('/welcome');
  };

  const onDeleteAccount = async () => {
    await trpc.user.destroy.mutate().then(() => {
      destroySessionCookie();

      resetTheme();

      reloadApp();
    });
  };

  const onDeleteAccountPrompt = async () => {
    Alert.alert(
      t.account.delete_account,
      t.account.delete_account_description,
      [
        {
          text: t.action.cancel,
          style: 'cancel',
        },
        {
          text: t.action.confirm,
          onPress: onDeleteAccount,
          style: 'destructive',
        },
      ],
      { cancelable: true },
    );
  };

  useEffect(() => {
    if (focused) {
      fetchMember();

      getReferralCode();
    }
  }, [focused]);

  console.log('my info', myInfo);

  if (loading && !myInfo) {
    return <PreLoading />;
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={{ marginHorizontal: 16, gap: 16 }}>
        <View style={styles.avatar}>
          <AvatarImage size={64} content={myInfo?.avatar} alt={myInfo?.name} withBorder />
          <View style={styles.avatarText}>
            <VText variant="H5" color={colors.textCommonPrimary}>
              {myInfo?.name ?? t.user.no_name}
            </VText>
            <VText variant="B2" color={colors.textCommonTertiary}>
              {myInfo?.email ?? t.user.no_email}
            </VText>
          </View>
        </View>
        <View
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
          }}
        >
          <TouchableOpacity style={styles.itemStyle} onPress={() => handleChangeMemberName()}>
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.user.member_name}
            </VText>
            <View
              style={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'row',
                gap: 6,
              }}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {myInfo?.name ?? t.user.no_name}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </TouchableOpacity>
          <TextSeparator />
          {/* <TouchableOpacity
            style={styles.itemStyle}
            onPress={() =>
              router.push({
                pathname: `/(app)/space/${spaceId}/(modal)/team-and-members/index`,
                params: {
                  mode: 'add',
                  memberId: me?.id as string,
                },
              })
            }
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.team.teams}
            </VText>
            <View
              style={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'row',
                gap: 6,
              }}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {teams?.length ?? 0}
              </VText>
              <AntDesign
                name="right"
                size={16}
                color={colors.textCommonTertiary}
              />
            </View>
          </TouchableOpacity>
          <TextSeparator /> */}
          <TouchableOpacity style={styles.itemStyle} onPress={() => handleChangeNickname()}>
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.user.nickname}
            </VText>
            <View
              style={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'row',
                gap: 6,
              }}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {myInfo?.name ?? t.user.no_name}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </TouchableOpacity>
          <TextSeparator />
          <TouchableOpacity
            style={styles.itemStyle}
            onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/user/email`)}
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.user.email}
            </VText>
            <View
              style={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'row',
                gap: 6,
              }}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {myInfo?.email ?? t.user.no_email}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </View>
          </TouchableOpacity>
          <TextSeparator />
          <TouchableOpacity style={styles.itemStyle} onPress={() => copyText(referralCode)}>
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.referral.referral_code}
            </VText>
            <View
              style={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'row',
                gap: 6,
              }}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {referralCode}
              </VText>
              <AntDesign name="copy1" size={16} color={colors.textCommonTertiary} />
            </View>
          </TouchableOpacity>
        </View>
        <View
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
          }}
        >
          <TouchableOpacity
            style={styles.itemStyle}
            onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/settings`)}
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.user.settings}
            </VText>
            <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
          </TouchableOpacity>
          <TextSeparator />
          <TouchableOpacity style={styles.itemStyle} onPress={() => router.push(getBaseUrl())}>
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.settings.about.help_center}
            </VText>
            <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
          </TouchableOpacity>
          <TextSeparator />
          <TouchableOpacity
            style={styles.itemStyle}
            onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/about`)}
          >
            <VText variant="B1" color={colors.textCommonPrimary}>
              {t.settings.about.about_brand}
            </VText>
            <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          onPress={onDeleteAccountPrompt}
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
            width: '100%',
          }}
        >
          <View style={styles.itemStyle}>
            <VText variant="B2" color={colors.textDangerDefault}>
              {t.account.delete_account}
            </VText>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onLogoutPress}
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
            width: '100%',
          }}
        >
          <View style={styles.itemStyle}>
            <VText variant="B2" color={colors.textDangerDefault}>
              {t.auth.logout}
            </VText>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  avatar: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    gap: 10,
    justifyContent: 'center',
    marginBottom: 15,
    marginTop: 20,
  },
  avatarText: {
    alignItems: 'center',
    display: 'flex',
    gap: 4,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
  },
  itemStyle: {
    alignItems: 'center',
    borderRadius: 10,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'space-between',
    marginVertical: 2,
    paddingHorizontal: 16,
    width: '100%',
  },
});
