/* eslint-disable no-nested-ternary */
import { AntDesign } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, SafeAreaView, ScrollView, TextInput, TouchableOpacity, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { DateTimePicker } from '@/components/UI/DateTimePicker';
import { FullSeparator, TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { copyText } from '@/utils/text';

export type apiExpiration = '1day' | '3day' | '7day' | '30day' | '180day' | '1year' | 'never';

const apiExpirationMap: Record<apiExpiration, string> = {
  '1day': 'api.e1day',
  '3day': 'api.e3days',
  '7day': 'api.e7days',
  '30day': 'api.e30days',
  '180day': 'api.e180days',
  '1year': 'api.e1year',
  never: 'api.never',
};

export default function AccountAPIScreen() {
  const router = useRouter();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const localParams = useLocalSearchParams();

  const [apis, setApis] = useState<any[] | null>(null);

  const [expiredDate, setExpiredDate] = useState<Date>(new Date());

  const [apiName, setApiName] = useState<string>('');

  const getInvitationLink = async () => {
    await trpc.user.getDeveloperTokens.query().then((res) => {
      setApis(res);
    });
  };

  const onLinkCreate = async () => {
    await trpc.user.createDeveloperToken
      .mutate({
        expirationDate: expiredDate.toISOString(),
      })
      .then(() => {
        getInvitationLink();
      });
  };

  const deleteLink = (token: string) => {
    Alert.alert(t.delete.confirm_deletion, t.delete.confirm_to_delete_this_link, [
      { text: t.cancel, style: 'cancel' },
      {
        text: t.confirm,
        style: 'destructive',
        onPress: () => confirmDelete(token),
      },
    ]);
  };

  const confirmDelete = async (token: string) => {
    await trpc.user.deleteDeveloperToken.mutate(token).then(() => {
      getInvitationLink();
    });
  };

  useEffect(() => {
    getInvitationLink();
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        margin: 16,
        gap: 20,
      }}
    >
      <View className="gap-2">
        <VText variant="B1" color={colors.textCommonPrimary}>
          {t.api.create_token}
        </VText>
        <VText variant="B2" color={colors.textCommonTertiary}>
          {t.api.create_token_description}
        </VText>
      </View>
      <View
        style={{
          backgroundColor: colors.bgControlsDefault,
          borderRadius: 8,
          paddingHorizontal: 16,
        }}
      >
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            minHeight: 54,
          }}
        >
          <VText variant="B1" color={colors.textCommonPrimary}>
            {t.api.name}
          </VText>
          <View>
            <TextInput
              style={{
                flex: 1,
                padding: 0,
                color: colors.textCommonPrimary,
              }}
              placeholder={t.api.name}
              value={apiName}
              onChangeText={setApiName}
            />
          </View>
        </View>
        <FullSeparator />
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            minHeight: 54,
          }}
        >
          <VText variant="B1" color={colors.textCommonPrimary}>
            {t.api.expiration}
          </VText>
          <DateTimePicker date={expiredDate} setDate={setExpiredDate} mode="date" />
        </View>
      </View>
      <TouchableOpacity
        style={{
          backgroundColor: colors.textBrandDefault,
          height: 40,
          borderRadius: 8,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPress={onLinkCreate}
      >
        <VText variant="B1" color={colors.textStaticPrimary}>
          {t.action.create}
        </VText>
      </TouchableOpacity>
      {apis && apis.length > 0 && (
        <GestureHandlerRootView style={{ flex: 1 }}>
          <VContext title={t.api.created_public_api_token} titleIndent>
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                flexGrow: 1,
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
              }}
            >
              <FlashList
                data={apis}
                renderItem={({ item }) => (
                  <Swipeable
                    renderRightActions={() => (
                      <TouchableOpacity
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                        onPress={() => deleteLink(item.token)}
                      >
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {t.action.delete}
                        </VText>
                      </TouchableOpacity>
                    )}
                  >
                    <TouchableOpacity
                      className="items-center"
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        gap: 2,
                        minHeight: 54,
                        paddingHorizontal: 16,
                        paddingVertical: 8,
                      }}
                      onPress={() => copyText(item.token)}
                    >
                      <View className="flex flex-col gap-1">
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {item.token}
                        </VText>
                        <VText variant="B2" color={colors.textCommonTertiary}>
                          {new Date(item.expiration).toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })}
                        </VText>
                      </View>
                      {/* <VText variant="B1" color={colors.textCommonPrimary}>
                        {item.userId}
                      </VText> */}
                      <View>
                        <AntDesign name="copy1" size={20} color={colors.textBrandDefault} />
                      </View>
                    </TouchableOpacity>
                  </Swipeable>
                )}
                keyExtractor={(item) => item.token}
                ItemSeparatorComponent={TextSeparator}
                scrollEnabled={false}
                estimatedItemSize={54}
              />
            </ScrollView>
          </VContext>
        </GestureHandlerRootView>
      )}
    </SafeAreaView>
  );
}
