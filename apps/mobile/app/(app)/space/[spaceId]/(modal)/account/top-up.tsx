import type { UserCoinsVO } from '@bika/types/user/vo';
import Checkbox from 'expo-checkbox';
import { Image } from 'expo-image';
import { useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, View } from 'react-native';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { getSessionCookie } from '@/context/global/utils/session';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function AccountReferralScreen() {
  const router = useRouter();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const sessionToken = getSessionCookie();

  const spaceId = useSpaceId();

  const [loading, setLoading] = useState(true);

  const [acceptTerms, setAcceptTerms] = useState(false);

  const [chooseAmount, setChooseAmount] = useState<number | null>(null);

  const [coins, setCoins] = useState<UserCoinsVO | null>(null);

  const getCoins = async () => {
    setLoading(true);
    await trpc.user.coins.query().then((res) => {
      setCoins(res);
      setLoading(false);
    });
  };

  useEffect(() => {
    getCoins();
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          paddingHorizontal: 16,
          paddingVertical: 10,
          flex: 1,
        }}
      >
        <View className="gap-6">
          <View className="gap-1">
            <VText variant="H5" color={colors.textCommonPrimary}>
              {t.top_up.your_bika_coins}
            </VText>
            <View className="flex flex-row items-center gap-2">
              <Image
                source={require('@/assets/images/coin.png')}
                style={{ width: 30, height: 30 }}
              />
              <VText variant="H4" color={colors.rainbowYellow5}>
                {coins?.balance}
              </VText>
            </View>
          </View>
          <View className="gap-3">
            <VText variant="H5" color={colors.textCommonPrimary}>
              {t.top_up.choose_top_up_amount}
            </VText>
            <View className="flex w-full flex-row flex-wrap items-center justify-between gap-3">
              <Pressable
                className="flex items-center"
                style={{
                  width: '31%',
                  padding: 16,
                  borderRadius: 6,
                  backgroundColor: colors.bgControlsDefault,
                  borderColor: chooseAmount === 0 ? colors.rainbowYellow5 : '',
                  borderWidth: chooseAmount === 0 ? 1 : 0,
                }}
                onPress={() => setChooseAmount(0)}
              >
                <VText variant="H6" color={colors.textCommonPrimary}>
                  900
                </VText>
                <VText variant="B4" color={colors.textCommonPrimary}>
                  10 USD
                </VText>
              </Pressable>
              <Pressable
                className="flex items-center"
                style={{
                  width: '31%',
                  padding: 16,
                  borderRadius: 6,
                  backgroundColor: colors.bgControlsDefault,
                  borderColor: chooseAmount === 1 ? colors.rainbowYellow5 : '',
                  borderWidth: chooseAmount === 1 ? 1 : 0,
                }}
                onPress={() => setChooseAmount(1)}
              >
                <VText variant="H6" color={colors.textCommonPrimary}>
                  900
                </VText>
                <VText variant="B4" color={colors.textCommonPrimary}>
                  10 USD
                </VText>
              </Pressable>
              <Pressable
                className="flex items-center"
                style={{
                  width: '31%',
                  padding: 16,
                  borderRadius: 6,
                  backgroundColor: colors.bgControlsDefault,
                  borderColor: chooseAmount === 2 ? colors.rainbowYellow5 : '',
                  borderWidth: chooseAmount === 2 ? 1 : 0,
                }}
                onPress={() => setChooseAmount(2)}
              >
                <VText variant="H6" color={colors.textCommonPrimary}>
                  900
                </VText>
                <VText variant="B4" color={colors.textCommonPrimary}>
                  10 USD
                </VText>
              </Pressable>
              <Pressable
                className="flex items-center"
                style={{
                  width: '31%',
                  padding: 16,
                  borderRadius: 6,
                  backgroundColor: colors.bgControlsDefault,
                  borderColor: chooseAmount === 3 ? colors.rainbowYellow5 : '',
                  borderWidth: chooseAmount === 3 ? 1 : 0,
                }}
                onPress={() => setChooseAmount(3)}
              >
                <VText variant="H6" color={colors.textCommonPrimary}>
                  900
                </VText>
                <VText variant="B4" color={colors.textCommonPrimary}>
                  10 USD
                </VText>
              </Pressable>
              <Pressable
                className="flex items-center"
                style={{
                  width: '31%',
                  padding: 16,
                  borderRadius: 6,
                  backgroundColor: colors.bgControlsDefault,
                  borderColor: chooseAmount === 4 ? colors.rainbowYellow5 : '',
                  borderWidth: chooseAmount === 4 ? 1 : 0,
                }}
                onPress={() => setChooseAmount(4)}
              >
                <VText variant="H6" color={colors.textCommonPrimary}>
                  900
                </VText>
                <VText variant="B4" color={colors.textCommonPrimary}>
                  10 USD
                </VText>
              </Pressable>
              <Pressable
                className="flex items-center"
                style={{
                  width: '31%',
                  padding: 16,
                  borderRadius: 6,
                  backgroundColor: colors.bgControlsDefault,
                  borderColor: chooseAmount === 5 ? colors.rainbowYellow5 : '',
                  borderWidth: chooseAmount === 5 ? 1 : 0,
                }}
                onPress={() => setChooseAmount(5)}
              >
                <VText variant="H6" color={colors.textCommonPrimary}>
                  900
                </VText>
                <VText variant="B4" color={colors.textCommonPrimary}>
                  10 USD
                </VText>
              </Pressable>
            </View>
          </View>
          <View className="gap-3">
            <View className="flex flex-row items-center gap-2">
              <Checkbox
                value={acceptTerms}
                onValueChange={setAcceptTerms}
                style={{ borderRadius: 9999, width: 16, height: 16 }}
                color={colors.textCommonTertiary}
              />
              <VText variant="B3" color={colors.textCommonTertiary}>
                {t.top_up.read_and_accept_toc}
              </VText>
            </View>
            <Pressable
              className="flex flex-row items-center justify-center gap-2"
              style={{
                padding: 12,
                minHeight: 48,
                borderRadius: 6,
                backgroundColor: acceptTerms ? colors.bgBrandDefault : colors.bgBrandDisabled,
              }}
              disabled={!acceptTerms || chooseAmount === null}
              onPress={COMING_SOON}
            >
              <VText
                variant="B1"
                color={acceptTerms ? colors.textStaticPrimary : colors.textStaticDisabled}
              >
                {t.top_up.top_up}
              </VText>
            </Pressable>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
