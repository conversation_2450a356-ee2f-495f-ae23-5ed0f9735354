import { AntDesign } from '@expo/vector-icons';
import * as Application from 'expo-application';
import { Linking, SafeAreaView, TouchableOpacity, View } from 'react-native';
import { AvatarImage } from '@/components/UI/Avatar';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl, isChinaVisitor } from '@/utils/base';

export default function AboutScreen() {
  const { nativeApplicationVersion } = Application;

  const isFromChina = isChinaVisitor();

  const colors = useColor();

  const { locale } = useGlobalContext();

  const { t } = locale;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          flex: 1,
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <View>
          <View
            style={{
              gap: 16,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <AvatarImage size={72} local localUrl={require('@/assets/images/icon.png')} alt="AI" />
            <View
              style={{
                gap: 4,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <VText variant="H5" color={colors.textCommonPrimary}>
                {t.brand.brand}
              </VText>
              <VText variant="B2" color={colors.textCommonTertiary}>
                v{nativeApplicationVersion}
              </VText>
            </View>
          </View>
          <View>
            <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                margin: 16,
              }}
            >
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(`${getBaseUrl()}/zh-TW/blog/what-is-bika-ai`);
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.about.release_notes}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
              <TextSeparator />
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(getBaseUrl());
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.about.license}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
              <TextSeparator />
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(getBaseUrl());
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.about.privacy_policy}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
              <TextSeparator />
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(getBaseUrl());
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.about.permission}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
              <TextSeparator />
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(getBaseUrl());
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.website.help_center}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
              <TextSeparator />
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL('https://apps.apple.com/app/bika-ai/id6477366601');
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.about.rate_us}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
            </View>
            {/* <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                margin: 16,
              }}
            >
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(getBaseUrl());
                }}
                className="flex flex-row items-center justify-between"
                style={{
                  height: 44,
                  paddingHorizontal: 16,
                }}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.about.safety}
                </VText>
                <AntDesign
                  name="right"
                  size={16}
                  color={colors.textCommonTertiary}
                />
              </TouchableOpacity>
            </View> */}
          </View>
        </View>
        <View style={{ alignSelf: 'center', gap: 8 }}>
          {isFromChina && (
            <VText variant="B2" color={colors.textCommonTertiary}>
              {t.about.cn_license}
            </VText>
          )}
          <VText
            variant="B2"
            color={colors.textCommonTertiary}
            style={{
              alignSelf: 'center',
            }}
          >
            {t.about.copyright}
          </VText>
        </View>
      </View>
    </SafeAreaView>
  );
}
