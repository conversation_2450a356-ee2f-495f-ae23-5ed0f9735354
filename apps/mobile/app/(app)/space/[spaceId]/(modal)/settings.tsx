import type { SpaceRenderVO } from '@bika/types/space/vo';
import { AntDesign } from '@expo/vector-icons';
import * as Application from 'expo-application';
import Constants from 'expo-constants';
import { useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { PreLoading } from '@/components/Loading';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { AvatarImage } from '@/components/UI/Avatar';
import { TextSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { VContext } from '@/components/UI/Typography';
import { useGlobalContext } from '@/context/global';
import { destroySessionCookie, resetTheme } from '@/context/global/utils/session';
import { usePermission, useSpaceContext, useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

export default function SettingsScreen() {
  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const { expoConfig } = Constants;
  const { nativeApplicationVersion } = Application;

  const spaceId = useSpaceId();

  const { rootTeam } = useSpaceContext();

  const permission = usePermission();

  const { auth, trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { refetchMe, logout, me } = auth;

  const [space, setSpace] = useState<SpaceRenderVO | null>(null);

  const [pressCount, setPressCount] = useState(0);

  const [loading, setLoading] = useState(true);

  const getSpace = async () => {
    setLoading(true);
    await trpc.space.info.query({ identify: spaceId }).then((res) => {
      console.log('space', res);

      setSpace(res);

      setLoading(false);
    });
  };

  const onVersionPress = () => {
    const newCount = pressCount + 1;
    setPressCount(newCount);

    if (newCount >= 10) {
      router.push(`/(app)/space/${spaceId}/(modal)/user/debug`);
      setPressCount(0);
    }
  };

  const onLogoutPress = async () => {
    logout().then(() => {
      console.log('logout');

      destroySessionCookie();

      resetTheme();
    });

    await refetchMe().then(() => {
      console.log('refreshMe');

      router.push('/welcome');
    });
  };

  useEffect(() => {
    if (focused) {
      getSpace();
    }
  }, [focused]);

  const styles = StyleSheet.create({
    itemStyle: {
      alignItems: 'center',
      borderRadius: 10,
      flexDirection: 'row',
      height: 40,
      justifyContent: 'space-between',
      marginVertical: 2,
      paddingHorizontal: 16,
      width: '100%',
    },
  });

  if (loading && !space) {
    return <PreLoading />;
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexGrow: 1,
          margin: 16,
          gap: 16,
        }}
      >
        <VContext title={t.settings.space.space} titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View
              className="flex flex-row items-center"
              style={{
                paddingHorizontal: 16,
                paddingVertical: 10,
                gap: 8,
              }}
            >
              <AvatarImage size={40} content={space?.logo} alt={space?.name} />
              <View>
                <VText variant="H5" color={colors.textCommonPrimary}>
                  {space?.name}
                </VText>
              </View>
            </View>
            {permission?.updateSpaceInfo && (
              <>
                <TouchableOpacity
                  style={styles.itemStyle}
                  onPress={() => router.push('./space/settings')}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {t.settings.space.space_settings}
                  </VText>
                  <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                </TouchableOpacity>
                <TextSeparator />
              </>
            )}
            {permission?.invite && (
              <>
                <TouchableOpacity
                  style={styles.itemStyle}
                  onPress={() => router.push('./account/invite')}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {t.settings.space.invite_members}
                  </VText>
                  <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                </TouchableOpacity>
                <TextSeparator />
              </>
            )}
            {permission?.readTeam && (
              <>
                <TouchableOpacity
                  style={styles.itemStyle}
                  onPress={() => router.push(`./team-and-members/${rootTeam.id}`)}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {t.settings.space.member_management}
                  </VText>
                  <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                </TouchableOpacity>
                <TextSeparator />
              </>
            )}
            {permission?.readMember && (
              <>
                <TouchableOpacity
                  style={styles.itemStyle}
                  onPress={() => router.push('./role/index')}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {t.settings.space.role_management}
                  </VText>
                  <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                </TouchableOpacity>
                <TextSeparator />
              </>
            )}
            {permission?.readIntegration && (
              <>
                <TouchableOpacity
                  style={styles.itemStyle}
                  onPress={() => router.push('./space/integration')}
                >
                  <VText variant="B1" color={colors.textCommonPrimary}>
                    {t.settings.space.third_party_integration}
                  </VText>
                  <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                </TouchableOpacity>
                <TextSeparator />
              </>
            )}
            <TouchableOpacity style={styles.itemStyle} onPress={COMING_SOON}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.space.usage}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
            <TextSeparator />
            {permission?.readAuditLog && (
              <TouchableOpacity
                style={styles.itemStyle}
                onPress={() => router.push('./space/audit')}
              >
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.settings.space.space_audits}
                </VText>
                <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
              </TouchableOpacity>
            )}
          </View>
        </VContext>
        <VContext title={t.settings.account.account} titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <View
              className="flex flex-row items-center"
              style={{
                paddingHorizontal: 16,
                paddingVertical: 10,
                gap: 8,
              }}
            >
              <AvatarImage size={40} content={me?.user.avatar} alt={me?.user.name} />
              <View>
                <VText variant="H5" color={colors.textCommonPrimary}>
                  {me?.user.name ?? t.user.no_name}
                </VText>
                <VText variant="B2" color={colors.textCommonTertiary}>
                  {me?.user.email ?? t.user.no_email}
                </VText>
              </View>
            </View>
            <TouchableOpacity
              style={styles.itemStyle}
              onPress={() => router.push(`./user/settings`)}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.account.account_information}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
            <TextSeparator />
            <TouchableOpacity
              style={styles.itemStyle}
              onPress={() => router.push('./account/binding')}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.account.connected_account}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
            <TextSeparator />
            <TouchableOpacity
              style={styles.itemStyle}
              onPress={() => router.push('./notification/settings')}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.account.notification}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
            <TextSeparator />
            <TouchableOpacity
              style={styles.itemStyle}
              onPress={() => router.push('./account/login_record')}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.account.session_logs}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
            <TextSeparator />
            <TouchableOpacity style={styles.itemStyle} onPress={() => router.push('./account/api')}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.account.api}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} itemStyle />
            </TouchableOpacity>
            <TextSeparator />
            <TouchableOpacity
              style={styles.itemStyle}
              onPress={() => router.push('./account/referral')}
            >
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.account.referral}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
          </View>
        </VContext>
        <VContext title={t.about.about} titleIndent>
          <View
            style={{
              backgroundColor: colors.bgControlsDefault,
              borderRadius: 10,
            }}
          >
            <TouchableOpacity style={styles.itemStyle} onPress={() => router.push(getBaseUrl())}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.about.help_center}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
            <TextSeparator />
            <TouchableOpacity style={styles.itemStyle} onPress={() => router.push('./about')}>
              <VText variant="B1" color={colors.textCommonPrimary}>
                {t.settings.about.about_brand}
              </VText>
              <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
            </TouchableOpacity>
          </View>
        </VContext>
        <TouchableOpacity
          onPress={onLogoutPress}
          style={{
            backgroundColor: colors.bgControlsDefault,
            borderRadius: 10,
          }}
        >
          <View style={styles.itemStyle}>
            <VText variant="B2" color={colors.textDangerDefault}>
              {t.auth.logout}
            </VText>
          </View>
        </TouchableOpacity>
        <Pressable
          onPress={onVersionPress}
          style={{
            alignSelf: 'center',
          }}
        >
          <VText variant="B2" color={colors.textCommonPrimary}>
            {nativeApplicationVersion} (
            {Platform.OS === 'ios'
              ? expoConfig?.ios?.buildNumber
              : expoConfig?.android?.versionCode}
            )
          </VText>
        </Pressable>
        <View style={{ minHeight: 100 }} />
      </ScrollView>
    </SafeAreaView>
  );
}
