import type { NotificationVO } from '@bika/types/notification/vo';
import type { SpaceRenderVO } from '@bika/types/space/vo';
import { AntDesign, FontAwesome5, FontAwesome6, Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import { Badge } from '@rneui/themed';
import { Image } from 'expo-image';
import { useNavigation, useRouter } from 'expo-router';
import { Drawer } from 'expo-router/drawer';
import * as SecureStore from 'expo-secure-store';
import { useCallback, useEffect, useState } from 'react';
import {
  Platform,
  Pressable,
  RefreshControl,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import ContextMenu from 'react-native-context-menu-view';
import SweetSFSymbol from 'sweet-sfsymbols';
import { AvatarImage } from '@/components/UI/Avatar';
import { FullSeparator } from '@/components/UI/Separator';
import { VText } from '@/components/UI/Text';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { rgbaToHex } from '@/utils/color';
import { handleRedirect } from '@/utils/notification';

export default function HomeLayout() {
  const router = useRouter();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { locale } = useGlobalContext();

  const { t } = locale;

  return (
    <Drawer drawerContent={DrawerContent}>
      <Drawer.Screen
        name="index"
        options={{
          headerBackground: () => <View style={{ backgroundColor: 'transparent' }} />,
          headerRight: () =>
            Platform.OS === 'ios' && (
              <ContextMenu
                actions={[
                  { systemIcon: 'plus', title: t.mission.mission },
                  // { systemIcon: 'plus', title: t.agenda.agenda },
                  { systemIcon: 'plus', title: t.data.data },
                  { systemIcon: 'plus', title: t.resource.resource },
                ]}
                dropdownMenuMode={true}
                onPress={(e) =>
                  router.push({
                    pathname: `/(app)/space/${spaceId}/(modal)/create-anything`,
                    params: {
                      createIndex: e.nativeEvent.index,
                    },
                  })
                }
              >
                <View style={{ paddingHorizontal: 16 }}>
                  <SweetSFSymbol
                    name="plus.circle"
                    colors={[rgbaToHex(colors.textCommonPrimary)]}
                    size={20}
                  />
                </View>
              </ContextMenu>
            ),
        }}
      />
    </Drawer>
  );
}

export const DrawerContent = () => {
  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const { auth, trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [refreshing, setRefreshing] = useState<boolean>(false);

  const [hasUnread, setHasUnread] = useState<boolean>(false);

  const { me } = auth;

  const nextAppAction = SecureStore.getItem('next_app_action');

  const [spaces, setSpaces] = useState<SpaceRenderVO[]>([]);

  const currentSpace: SpaceRenderVO = spaces[
    spaces.findIndex((s) => s.id === spaceId) > -1 ? spaces.findIndex((s) => s.id === spaceId) : 0
  ] ?? {
    name: t.space.default_space_name,
    id: 'default',
  };

  const getSpaces = async () => {
    await trpc.space.list.query({}).then((res) => {
      setSpaces(res);
    });
  };

  const getNotification = async () => {
    await trpc.notification.list
      .query()
      .then((res) =>
        res.find((notification) => !notification.read) ? setHasUnread(true) : setHasUnread(false),
      );
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);

    getSpaces().then(() => {
      setRefreshing(false);
    });
  }, []);

  useEffect(() => {
    if (focused) {
      getSpaces();

      getNotification();
    }
  }, [focused]);

  useEffect(() => {
    if (nextAppAction) {
      console.log('next_app_action', nextAppAction);
      SecureStore.deleteItemAsync('next_app_action');

      return router.push(handleRedirect(JSON.parse(nextAppAction) as NotificationVO) as any);
    }
  }, [nextAppAction]);

  return (
    <View style={{ flex: 1 }}>
      <DrawerContentScrollView style={{ flex: 1 }} scrollEnabled={false}>
        <View
          className="flex flex-col justify-between"
          style={{
            flex: 1,
            marginHorizontal: 18,
            minHeight: '88%',
          }}
        >
          <View className="flex-1" style={{ gap: 10 }}>
            <View className="flex flex-row items-center justify-between">
              <View
                className="flex flex-row items-center"
                style={{
                  width: '80%',
                  gap: 10,
                }}
              >
                <Image
                  style={{
                    width: 36,
                    height: 36,
                    borderRadius: 6,
                  }}
                  source={require('@/assets/images/icon.png')}
                  alt="icon"
                />
                <View className="shrink">
                  <VText variant="H6" color={colors.textCommonPrimary}>
                    {currentSpace.name}
                  </VText>
                </View>
              </View>
              <Pressable
                className="flex flex-row"
                style={{
                  gap: 10,
                  padding: 10,
                  borderRadius: 8,
                  backgroundColor: colors.bgControlsDefault,
                }}
                onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/settings`)}
              >
                <Ionicons name="settings-outline" size={20} color={colors.textCommonPrimary} />
              </Pressable>
            </View>
            <FullSeparator />
            <ScrollView
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
              className="flex-1"
              style={{
                gap: 8,
              }}
            >
              {spaces.map((space) => (
                <TouchableOpacity
                  key={space.id}
                  className="flex flex-row items-center justify-between"
                  style={{
                    borderRadius: 10,
                    paddingRight: 10,
                    backgroundColor:
                      space === currentSpace ? colors.bgControlsDefault : 'transparent',
                  }}
                  onPress={async () => {
                    await AsyncStorage.setItem('spaceId', space.id);

                    router.replace({
                      pathname: `/(app)/app`,
                      params: {
                        redirectSpaceId: space.id,
                      },
                    });
                  }}
                >
                  <View
                    className="flex flex-row items-center"
                    style={{
                      gap: 10,
                      padding: 10,
                      borderRadius: 10,
                      width: '70%',
                    }}
                  >
                    <Image
                      source={require('@/assets/images/icon.png')}
                      style={{
                        borderRadius: 4,
                        height: 32,
                        width: 32,
                      }}
                    />
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {space.name}
                    </VText>
                  </View>
                  <View>
                    {space === currentSpace && (
                      <AntDesign name="check" size={20} color={colors.textBrandDefault} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
              <TouchableOpacity
                className="flex flex-row items-center justify-start"
                style={{
                  borderRadius: 10,
                  gap: 10,
                  padding: 10,
                }}
                onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/(wizard)/create-space`)}
              >
                <View
                  className="flex items-center justify-center"
                  style={{
                    backgroundColor: colors.bgControlsDefault,
                    borderRadius: 4,
                    height: 32,
                    width: 32,
                  }}
                >
                  <FontAwesome6 name="add" size={20} color={colors.textCommonPrimary} />
                </View>
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.space.new_space}
                </VText>
              </TouchableOpacity>
            </ScrollView>
          </View>
          <View className="flex flex-row items-center justify-between">
            <TouchableOpacity
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 8,
              }}
              onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/account/info`)}
            >
              <AvatarImage size={36} content={me?.user.avatar} alt={me?.user.name} />
              <VText variant="H6" color={colors.textCommonPrimary}>
                {me?.user.name ?? t.user.no_name}
              </VText>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/notification`)}
            >
              {hasUnread && (
                <Badge
                  status="error"
                  containerStyle={{ position: 'absolute', top: -5, right: -5 }}
                />
              )}
              <FontAwesome5 name="bell" size={20} color={colors.textCommonPrimary} />
            </TouchableOpacity>
          </View>
        </View>
      </DrawerContentScrollView>
    </View>
  );
};
