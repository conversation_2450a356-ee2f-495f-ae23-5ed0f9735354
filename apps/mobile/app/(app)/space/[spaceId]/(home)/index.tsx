import type { FolderVO } from '@bika/types/node/vo';
import type { SpaceMyRedDotsVO, SpaceRenderVO } from '@bika/types/space/vo';
import { AntDesign, Ionicons, MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FlashList } from '@shopify/flash-list';
import { useNavigation, useRouter } from 'expo-router';
import Drawer from 'expo-router/drawer';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Fab from '@/components/Button/Fab';
import { HomeHeader } from '@/components/HomeHeader';
import { RenderNodeResourceIcon } from '@/components/Render';
import { IconSeparator } from '@/components/UI/Separator';
import { ItemSkeleton } from '@/components/UI/Skeleton';
import { VText } from '@/components/UI/Text';
import { BubbleBox } from '@/components/Wizard/BubbleBox';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

export default function HomeScreen() {
  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const spaceId = useSpaceId();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const [spaceDetails, setSpaceDetails] = useState<SpaceRenderVO | null>(null);
  const [rootNode, setRootNode] = useState<FolderVO | null>(null);
  const [redDots, setRedDots] = useState<SpaceMyRedDotsVO | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const [isPricingShown, setIsPricingShown] = useState<boolean>(true);

  const [isLinkedExternalAccount, setIsLinkedExternalAccount] = useState<boolean>(true);

  const navigationOccurred = useRef(false);

  const fetchSpaceData = async () => {
    try {
      const spaceInfo = await trpc.space.info.query({ identify: spaceId });
      setSpaceDetails(spaceInfo);

      const rootNodeData = await trpc.node.root.query({ spaceId });
      if (rootNodeData?.space?.children?.length === 0 && !navigationOccurred.current) {
        router.push(`/(app)/space/${spaceId}/(modal)/(wizard)/onboarding`);
        navigationOccurred.current = true;
      }

      const nodeDetail = await trpc.node.detail.query({
        id: rootNodeData.space.id,
      });
      setRootNode(nodeDetail.resource as FolderVO);

      const redDots = await trpc.my.reddots.query({ spaceId });
      setRedDots(redDots);
    } catch (error) {
      console.error('Error fetching space data:', error);
    }
  };

  const fetchUserData = async () => {
    await trpc.user.hasLinkedAnyExternalAccount.query().then((res) => {
      if (!res) {
        setIsLinkedExternalAccount(false);
      }
    });
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchUserData().then(() => {
      fetchSpaceData().then(() => {
        setRefreshing(false);
      });
    });
  }, []);

  const getPricingShown = async () => {
    await AsyncStorage.getItem('pricing').then((res) => {
      setIsPricingShown(Boolean(res));
    });
  };

  useEffect(() => {
    if (focused) {
      fetchUserData();

      fetchSpaceData();

      getPricingShown();
    }
  }, [focused]);

  return (
    <>
      <Drawer.Screen
        options={{
          title: spaceDetails?.name ?? t.action.loading,
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.container}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
          <HomeHeader spaceId={spaceDetails?.id} redDots={redDots} />
          <View style={{ gap: 10, marginVertical: 10 }}>
            {!isLinkedExternalAccount && (
              <BubbleBox
                boxKey="account-binding"
                title={t.account.social_account_binding}
                description={t.account.social_account_binding_description}
                action={t.account.bind_now}
                actionOnPress={() =>
                  router.push(`/(app)/space/${spaceId}/(modal)/(wizard)/account-binding`)
                }
                closeable={true}
              />
            )}
            {/* {!isPricingShown && (
              <BubbleBox
                boxKey="pricing"
                title={t.account.advanced_features}
                description={t.account.use_advanced_features_description}
                action={t.account.subscribe_now}
                actionOnPress={() =>
                  router.push(
                    `/(app)/space/${spaceId}/(modal)/(wizard)/pricing`,
                  )
                }
                closeable={true}
              />
            )} */}
          </View>
          <View style={styles.contentContainer}>
            <View style={styles.sectionHeader}>
              <VText variant="H4" color={colors.textCommonPrimary}>
                {t.resource.resources}
              </VText>
            </View>
            <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                marginHorizontal: 16,
              }}
            >
              <FlashList
                data={rootNode?.children}
                ListEmptyComponent={<ItemSkeleton />}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    onPress={() => router.push(`/(app)/space/${spaceDetails?.id}/node/${item.id}`)}
                    style={styles.item}
                  >
                    <View
                      className="flex items-center justify-center"
                      style={{ width: 46, height: 46 }}
                    >
                      <RenderNodeResourceIcon type={item.type} size={26} />
                    </View>
                    <View style={styles.itemContent}>
                      <View style={styles.itemHeader}>
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {item.name}
                        </VText>
                      </View>
                      <View style={styles.itemRight}>
                        <AntDesign name="right" size={16} color={colors.textCommonTertiary} />
                      </View>
                    </View>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id}
                ItemSeparatorComponent={IconSeparator}
                scrollEnabled={false}
                estimatedItemSize={54}
              />
            </View>
          </View>
          <View style={styles.contentContainer}>
            <View style={styles.sectionHeader}>
              <VText variant="H4" color={colors.textCommonPrimary}>
                {t.explore.explore}
              </VText>
            </View>
            <View
              style={{
                backgroundColor: colors.bgControlsDefault,
                borderRadius: 10,
                marginHorizontal: 16,
              }}
            >
              <TouchableOpacity onPress={() => router.push('./template')} style={styles.item}>
                <View
                  className="flex items-center justify-center"
                  style={{ width: 46, height: 46 }}
                >
                  <Ionicons name="earth-outline" size={26} color={colors.textCommonPrimary} />
                </View>
                <View style={styles.itemContent}>
                  <View style={styles.itemHeader}>
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {t.template.template}
                    </VText>
                  </View>
                </View>
              </TouchableOpacity>
              <IconSeparator />
              <TouchableOpacity
                onPress={() => router.push(`/(app)/space/${spaceId}/(modal)/account/invite`)}
                style={styles.item}
              >
                <View
                  className="flex items-center justify-center"
                  style={{ width: 46, height: 46 }}
                >
                  <AntDesign name="adduser" size={26} color={colors.textCommonPrimary} />
                </View>
                <View style={styles.itemContent}>
                  <View style={styles.itemHeader}>
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {t.invite.invite}
                    </VText>
                  </View>
                </View>
              </TouchableOpacity>
              <IconSeparator />
              {/* <TouchableOpacity
                onPress={() =>
                  router.push(
                    `/(app)/space/${spaceId}/(modal)/(wizard)/pricing`,
                  )
                }
                style={styles.item}
              >
                <View
                  className="flex items-center justify-center"
                  style={{ width: 46, height: 46 }}
                >
                  <MaterialIcons
                    name="upgrade"
                    size={26}
                    color={colors.textCommonPrimary}
                  />
                </View>
                <View style={styles.itemContent}>
                  <View style={styles.itemHeader}>
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {t.upgrade.upgrade}
                    </VText>
                  </View>
                </View>
              </TouchableOpacity>
              <IconSeparator /> */}
              <TouchableOpacity onPress={() => router.push(getBaseUrl())} style={styles.item}>
                <View
                  className="flex items-center justify-center"
                  style={{ width: 46, height: 46 }}
                >
                  <MaterialIcons name="support-agent" size={26} color={colors.textCommonPrimary} />
                </View>
                <View style={styles.itemContent}>
                  <View style={styles.itemHeader}>
                    <VText variant="B1" color={colors.textCommonPrimary}>
                      {t.help.help_and_support}
                    </VText>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <Fab />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 10,
  },
  contentContainer: {
    gap: 4,
    marginVertical: 6,
    width: '100%',
  },
  item: {
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 8,
  },
  itemContent: {
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    height: 56,
    justifyContent: 'space-between',
    marginLeft: 8,
  },
  itemHeader: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
  },
  itemRight: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
  },
  sectionHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginVertical: 6,
  },
  separator: {
    height: 1,
    marginVertical: 30,
    width: '80%',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});
