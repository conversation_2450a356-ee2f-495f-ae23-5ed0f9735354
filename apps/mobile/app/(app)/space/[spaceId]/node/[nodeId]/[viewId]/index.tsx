import type { DatabaseVO, RecordPaginationVO, ViewVO } from '@bika/types/database/vo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import { Entypo, FontAwesome } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { Stack, useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import ContextMenu, { type ContextMenuAction } from 'react-native-context-menu-view';
import FabGroup from '@/components/Button/FabGroup';
import { PreLoading } from '@/components/Loading';
import { COMING_SOON } from '@/components/NOT_SUPPORTED';
import { VText } from '@/components/UI/Text';
import { ViewIconMap } from '@/constants/view';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';

export default function NodeViewIdScreen() {
  const { nodeId, viewId } = useLocalSearchParams();

  const router = useRouter();

  const navigation = useNavigation();
  const focused = navigation.isFocused();

  const colors = useColor();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const spaceId = useSpaceId();

  const [view, setView] = useState<ViewVO | null>(null);

  const [records, setRecords] = useState<RecordPaginationVO | null>(null);

  const [searchResults, setSearchResults] = useState<RecordPaginationVO | null>(null);

  const [loading, setLoading] = useState(true);

  const [nodeResource, setNodeResource] = useState<NodeDetailVO | null>(null);

  const [refreshing, setRefreshing] = useState(false);

  const getNodeDetails = async () => {
    setLoading(true);
    await trpc.node.detail
      .query({
        id: nodeId as string,
      })
      .then((res) => {
        setNodeResource(res);
        setLoading(false);
      });
  };

  const getView = async () => {
    setLoading(true);
    await trpc.database.getView
      .query({
        viewId: viewId as string,
      })
      .then((data) => {
        setView(data);
        setLoading(false);
      });
  };

  const listRecords = async () => {
    setLoading(true);
    await trpc.database.listRecords
      .query({
        databaseId: nodeId as string,
        viewId: viewId as string,
        startRow: 0,
        endRow: 20,
      })
      .then((data) => {
        setRecords(data);
        setLoading(false);
      });
  };

  const convertedViewsToContextMenu = (nodeResource?.resource as DatabaseVO)?.views.map((v) => {
    return {
      systemIcon: 'baseball.diamond.bases',
      title: v.name ?? t.node.node,
      id: v.id,
    };
  }) satisfies ContextMenuAction[];

  const findPrimaryKey = (obj: any) => {
    return Object.keys(obj).find((key) => obj[key].id === key) ?? 'Field Missing';
  };

  const onSearch = (text: string) => {
    if (text === '') {
      setSearchResults(null);
    } else if (records) {
      const searchResult = records.rows.filter((record) => {
        let resultString = '';

        for (const key in record.cells) {
          if (Object.hasOwn(record.cells, key)) {
            let { value } = record.cells[key];
            if (Array.isArray(value)) {
              value = value.join(', ');
            } else if (value === null || value === undefined) {
              value = '';
            }
            resultString += `${value}, `;
          }
        }

        return resultString.toLowerCase().includes(text.toLowerCase());
      });

      setSearchResults({
        rows: searchResult,
        total: searchResult.length,
      });
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await listRecords();
    setRefreshing(false);
  };

  useEffect(() => {
    if (focused) {
      getView();
      listRecords();
      getNodeDetails();
    }
  }, [focused]);

  const ViewIcon = ViewIconMap[view?.type ?? 'TABLE'];

  return (
    <>
      <Stack.Screen
        options={{
          title: view?.name ?? t.node.node,
          headerRight: () => (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 16,
              }}
            >
              {/* <TouchableOpacity
                onPress={() =>
                  router.push({
                    pathname: `/(app)/space/${spaceId}/(modal)/record/create`,
                    params: {
                      databaseId: nodeId,
                    },
                  })
                }
              >
                <SweetSFSymbol
                  name="plus"
                  colors={[rgbaToHex(colors.textCommonPrimary)]}
                  size={20}
                />
              </TouchableOpacity> */}
              <ContextMenu
                actions={[
                  {
                    systemIcon: 'square.and.pencil',
                    title: t.action.edit,
                  },
                  // {
                  //   systemIcon: 'square.and.pencil',
                  //   title: t.record.request_new_record,
                  // },
                  {
                    systemIcon: 'trash',
                    title: t.action.delete,
                    destructive: true,
                  },
                ]}
                dropdownMenuMode={true}
                onPress={(e) => COMING_SOON()}
              >
                <View>
                  <Entypo name="dots-three-horizontal" size={20} color={colors.textCommonPrimary} />
                </View>
              </ContextMenu>
            </View>
          ),
          // headerSearchBarOptions: {
          //   placeholder: t.action.search,
          //   cancelButtonText: t.action.cancel,
          //   onChangeText: (e) => {
          //     onSearch(e.nativeEvent.text);
          //   },
          // },
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <View style={{ paddingHorizontal: 16, gap: 8, flex: 1 }}>
          <View className="flex w-full flex-row items-center justify-between">
            <View
              className="rounded-md"
              style={{
                backgroundColor: colors.bgControlsDefault,
                paddingHorizontal: 16,
                paddingVertical: 6,
              }}
            >
              {/* <ContextMenu
                actions={convertedViewsToContextMenu}
                dropdownMenuMode={true}
                onPress={(e) =>
                  router.replace(
                    `/(app)/space/${spaceId}/node/${nodeId}/${(nodeResource?.resource as DatabaseVO).views[e.nativeEvent.index].id}`,
                  )
                }
              > */}
              <TouchableOpacity
                onPress={() =>
                  router.push({
                    pathname: `/(app)/space/${spaceId}/(modal)/node/view-list`,
                    params: {
                      databaseId: nodeId,
                      viewId,
                    },
                  })
                }
              >
                <View className="flex shrink flex-row items-center text-center" style={{ gap: 4 }}>
                  <ViewIcon size={16} color={colors.textCommonPrimary} />
                  <VText variant="H6" color={colors.textCommonPrimary} numberOfLines={1}>
                    {view?.name ?? t.node.node}
                  </VText>
                  <FontAwesome name="angle-down" size={16} color={colors.textCommonTertiary} />
                </View>
              </TouchableOpacity>

              {/* </ContextMenu> */}
            </View>
            {/* <Pressable
              onPress={COMING_SOON}
              className="rounded-md"
              style={{ backgroundColor: colors.bgControlsDefault, padding: 8 }}
            >
              <FontAwesome
                name="filter"
                size={16}
                color={colors.textCommonPrimary}
              />
            </Pressable> */}
          </View>
          {loading && !records ? (
            <PreLoading />
          ) : (
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                flexGrow: 1,
              }}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            >
              {records?.rows && records?.rows?.length > 0 ? (
                <FlashList
                  data={searchResults ? searchResults.rows : records?.rows}
                  estimatedItemSize={200}
                  renderItem={({ item }) => {
                    const fieldStrings = [];
                    const primaryKey = findPrimaryKey(item.cells);

                    for (const key in item.cells) {
                      if (key === primaryKey) continue;
                      if (fieldStrings.length >= 4) break;
                      if (Object.hasOwn(item.cells, key)) {
                        const { value } = item.cells[key];
                        // if (Array.isArray(value)) {
                        //   value = value.join(', ');
                        // } else if (value === null || value === undefined) {
                        //   value = '';
                        // }

                        if (typeof value === 'string') {
                          fieldStrings.push(value);
                        }
                      }
                    }

                    return (
                      <TouchableOpacity
                        style={{
                          backgroundColor: colors.bgControlsDefault,
                          marginVertical: 4,
                          paddingHorizontal: 16,
                          paddingVertical: 8,
                          borderRadius: 8,
                        }}
                        onPress={() =>
                          router.push({
                            pathname: `/(app)/space/${spaceId}/(modal)/record/detail`,
                            params: {
                              databaseId: nodeId,
                              recordId: item.id,
                            },
                          })
                        }
                      >
                        <VText variant="B1" color={colors.textCommonPrimary}>
                          {String(item.cells[findPrimaryKey(item.cells)].value ?? ' ')}
                        </VText>
                        <View className="flex flex-row justify-between gap-2">
                          {fieldStrings.map((fieldString, idx) => (
                            <View
                              key={`${item.id}-field-${idx}`}
                              className="overflow-hidden"
                              style={{
                                width: '24%',
                              }}
                            >
                              <VText
                                variant="B4"
                                color={colors.textCommonTertiary}
                                numberOfLines={1}
                              >
                                {fieldString}
                              </VText>
                            </View>
                          ))}
                        </View>
                      </TouchableOpacity>
                    );
                  }}
                />
              ) : (
                <VText variant="B1" color={colors.textCommonPrimary}>
                  {t.space.no_data}
                </VText>
              )}
            </ScrollView>
          )}
        </View>
        <FabGroup />
      </SafeAreaView>
    </>
  );
}
