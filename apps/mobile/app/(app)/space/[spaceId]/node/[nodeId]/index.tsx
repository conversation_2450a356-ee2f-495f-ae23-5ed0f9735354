import type { <PERSON><PERSON> } from '@bika/types/database/vo';
import type { FolderVO, NodeDetailVO } from '@bika/types/node/vo';
import { Entypo } from '@expo/vector-icons';
import { Redirect, Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import ContextMenu from 'react-native-context-menu-view';
import FabGroup from '@/components/Button/FabGroup';
import { PreLoading } from '@/components/Loading';
import { COMING_SOON, NOT_SUPPORTED_COMPONENT, ONLY_ON_WEB } from '@/components/NOT_SUPPORTED';
import { NodeAutomation } from '@/components/Node/Automation';
import { NodeFolder } from '@/components/Node/Folder';
import { useAction } from '@/context/action/provider';
import { ActionType } from '@/context/action/types';
import { useGlobalContext } from '@/context/global';
import { useSpaceId } from '@/context/space/provider';
import { useColor } from '@/hooks/useColor';
import { getBaseUrl } from '@/utils/base';

export default function NodeScreen() {
  const { nodeId } = useLocalSearchParams<{ nodeId: string }>();

  const { trpc, locale } = useGlobalContext();

  const { t } = locale;

  const { showSnackbar } = useAction();

  const colors = useColor();

  const router = useRouter();

  const [loading, setLoading] = useState(true);

  const spaceId = useSpaceId();

  const [nodeResource, setNodeResource] = useState<NodeDetailVO | null>(null);

  const getNodeDetails = async (_nodeId: string) => {
    try {
      await trpc.node.detail
        .query({
          id: _nodeId as string,
        })
        .then((node) => {
          setNodeResource(node);
          setLoading(false);
        });
    } catch (err: any) {
      showSnackbar(`${t.error.error}: ${err?.message}`, ActionType.ERROR);
    }
  };

  useEffect(() => {
    getNodeDetails(nodeId);
  }, [nodeId]);

  if (loading) {
    return <PreLoading />;
  }

  const renderTitle = nodeResource?.type !== 'TEMPLATE' ? nodeResource?.name || t.node.node : '';

  const RenderHeaderRight = () => {
    switch (nodeResource?.type) {
      case 'TEMPLATE':
        return (
          <ContextMenu
            actions={[
              {
                systemIcon: 'square.and.pencil',
                title: t.action.edit,
              },
              {
                systemIcon: 'link',
                title: t.template.check_original_template,
              },
              {
                systemIcon: 'trash',
                title: t.action.delete,
                destructive: true,
              },
            ]}
            dropdownMenuMode={true}
            onPress={(e) => {
              switch (e.nativeEvent.index) {
                case 0:
                  ONLY_ON_WEB({
                    type: 'EDITOR',
                    url: `${getBaseUrl()}/space/${spaceId}/node/${nodeId}`,
                  });
                  break;
                case 1: {
                  const templateId = (nodeResource?.resource as FolderVO)?.templateId;

                  if (!templateId) {
                    return router.push({
                      pathname: `/(app)/space/${spaceId}/template`,
                      params: {
                        error: 'TEMPLATE_NOT_FOUND',
                      },
                    });
                  }

                  return router.push(
                    `/(app)/space/${spaceId}/template/${encodeURIComponent(templateId)}`,
                  );
                }
                case 2:
                  COMING_SOON();
                  break;
                default:
                  break;
              }
            }}
          >
            <View>
              <Entypo name="dots-three-horizontal" size={20} color={colors.textCommonPrimary} />
            </View>
          </ContextMenu>
        );
      default:
        return null;
    }
  };

  const RenderNodeResource = () => {
    switch (nodeResource?.type) {
      case 'AUTOMATION':
        return (
          <NodeAutomation spaceId={spaceId} nodeId={nodeId as string} resources={nodeResource} />
        );
      case 'DATABASE':
        return (
          <Redirect
            href={`/(app)/space/${spaceId}/node/${nodeId}/${(nodeResource.resource as DatabaseVO).views[0].id}`}
          />
        );
      // case 'VIEW':
      //   return (
      //     <Redirect
      //       href={`/(app)/space/${spaceId}/node/${(nodeResource.resource as DatabaseVO).views[0].property.databaseId}/${(nodeResource.resource as DatabaseVO).views[0].id}`}
      //     />
      //   );
      case 'TEMPLATE':
      case 'FOLDER':
        return <NodeFolder nodeId={nodeId as string} resources={nodeResource} />;
      // case 'TEMPLATE':
      //   return (
      //     <Redirect
      //       href={`/(app)/space/${spaceId}/template/${encodeURIComponent(
      //         (nodeResource?.resource as FolderVO)?.templateId as string,
      //       )}`}
      //     />
      //   );
      default:
        return <NOT_SUPPORTED_COMPONENT title={nodeResource?.type} id={nodeResource?.id} />;
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: renderTitle,
          headerLargeTitle: nodeResource?.type === 'AUTOMATION',
          headerRight: () => <RenderHeaderRight />,
        }}
      />
      <SafeAreaView style={{ flex: 1 }}>
        <RenderNodeResource />
        <FabGroup />
      </SafeAreaView>
    </>
  );
}
