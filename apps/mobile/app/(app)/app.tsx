import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect } from 'react';
import { PreLoading } from '@/components/Loading';
import { useGlobalContext } from '@/context/global';
import { getSessionCookie } from '@/context/global/utils/session';

export default function AppScreen() {
  const router = useRouter();

  const { trpc } = useGlobalContext();

  const sessionCookie = getSessionCookie();

  console.log('sessionCookie', sessionCookie);

  const { redirectSpaceId } = useLocalSearchParams();

  const getSpace = async () => {
    await trpc.space.list.query({}).then(async (res) => {
      console.log('spaces', res);

      console.log('redirectSpaceId', redirectSpaceId);

      const storedSpaceId = await AsyncStorage.getItem('spaceId');

      if (res.length > 0) {
        if (redirectSpaceId) {
          console.log(1);
          return router.replace(`/(app)/space/${redirectSpaceId}/(home)/`);
        }

        console.log(2);

        if (storedSpaceId) {
          console.log(3);
          return router.replace(`/(app)/space/${storedSpaceId}/(home)/`);
        }

        console.log(4);
        return router.replace(`/(app)/space/${res[0].id}/(home)/`);
      }

      return router.push(`/(app)/space/${res[0].id}/(modal)/(wizard)/create-space`);
    });
  };

  useEffect(() => {
    getSpace();
  }, []);

  console.log('/(app)/app');

  return <PreLoading />;
}
