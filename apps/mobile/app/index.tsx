import { Image } from 'expo-image';
import { StyleSheet, View } from 'react-native';

export default function IndexScreen() {
  return (
    <View
      style={{
        backgroundColor: '#000000',
        flex: 1,
      }}
    >
      <Image style={styles.image} source={require('@/assets/images/splash.png')} />
    </View>
  );
}

const styles = StyleSheet.create({
  image: {
    flex: 1,
    width: '100%',
  },
});
