import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

const EmailOutlined = (props: SvgProps) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill={props.fill || '#000'} {...props}>
    <Path d="M11.6653 6.50545C11.9821 6.2385 12.0224 5.76534 11.7554 5.44862C11.4885 5.13191 11.0153 5.09156 10.6986 5.35851L8 7.63309L5.30138 5.35851C4.98466 5.09156 4.5115 5.13191 4.24455 5.44862C3.9776 5.76534 4.01794 6.2385 4.33466 6.50545L7.19441 8.91583C7.65983 9.30812 8.34017 9.30812 8.80559 8.91583L11.6653 6.50545Z" />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.25 2C1.55964 2 1 2.55964 1 3.25V12.75C1 13.4404 1.55964 14 2.25 14H13.75C14.4404 14 15 13.4404 15 12.75V3.25C15 2.55964 14.4404 2 13.75 2H2.25ZM2.5 12.5V3.5H13.5V12.5H2.5Z"
    />
  </Svg>
);
export default EmailOutlined;
