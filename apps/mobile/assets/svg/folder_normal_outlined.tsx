import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

const FolderNormalOutlined = (props: SvgProps) => (
  <Svg width={17} height={16} viewBox="0 0 17 16" fill={props.fill || '#000'} {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.39064 1.99988C5.67642 1.99988 5.95358 2.0978 6.17592 2.27734L7.38038 3.24988H14.25C14.9404 3.24988 15.5 3.80952 15.5 4.49988L15.5 12.7499C15.5 13.4402 14.9404 13.9999 14.25 13.9999H2.75C2.05964 13.9999 1.5 13.4402 1.5 12.7499V3.24988C1.5 2.55952 2.05964 1.99988 2.75 1.99988H5.39064ZM6.50677 4.47242L5.30231 3.49988H3V6H14V4.74988H7.29205C7.00627 4.74988 6.72912 4.65195 6.50677 4.47242ZM14 7.5H3V12.4999H14L14 7.5Z"
    />
  </Svg>
);
export default FolderNormalOutlined;
