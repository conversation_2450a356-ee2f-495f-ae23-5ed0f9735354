import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

const MirrorOutlined = (props: SvgProps) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill={props.fill || '#000'} {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.25767 1.627C2.43106 1.25961 1.5 1.86469 1.5 2.76926V13.2305C1.5 14.1351 2.43107 14.7401 3.25767 14.3728L6.75767 12.8172C7.20908 12.6166 7.5 12.1689 7.5 11.6749V4.32482C7.5 3.83083 7.20908 3.38318 6.75767 3.18255L3.25767 1.627ZM3 12.8458V3.15395L6 4.48728V11.5125L3 12.8458Z"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.7423 1.627C13.5689 1.25961 14.5 1.86469 14.5 2.76926V13.2305C14.5 14.1351 13.5689 14.7401 12.7423 14.3728L9.24233 12.8172C8.79092 12.6166 8.5 12.1689 8.5 11.6749V4.32482C8.5 3.83083 8.79092 3.38318 9.24233 3.18255L12.7423 1.627ZM13 12.8458V3.15395L10 4.48728V11.5125L13 12.8458Z"
    />
  </Svg>
);
export default MirrorOutlined;
