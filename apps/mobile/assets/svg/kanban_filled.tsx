import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

const KanbanFilled = (props: SvgProps) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill={props.fill || '#000'} {...props}>
    <Path d="M1.5 2.75C1.5 2.05964 2.05964 1.5 2.75 1.5H4.5V11.5H2.75C2.05964 11.5 1.5 10.9404 1.5 10.25V2.75Z" />
    <Path d="M6 1.5H10V13.25C10 13.9404 9.44036 14.5 8.75 14.5H7.25C6.55964 14.5 6 13.9404 6 13.25V1.5Z" />
    <Path d="M13.25 1.5H11.5V9.5H13.25C13.9404 9.5 14.5 8.94036 14.5 8.25V2.75C14.5 2.05964 13.9404 1.5 13.25 1.5Z" />
  </Svg>
);
export default KanbanFilled;
