import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

const KanbanOutlined = (props: SvgProps) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill={props.fill || '#000'} {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.5 2.75C1.5 2.05964 2.05964 1.5 2.75 1.5H13.25C13.9404 1.5 14.5 2.05964 14.5 2.75V8.25C14.5 8.94036 13.9404 9.5 13.25 9.5H10.75V13.25C10.75 13.9404 10.1904 14.5 9.5 14.5H6.5C5.80964 14.5 5.25 13.9404 5.25 13.25V11.5H2.75C2.05964 11.5 1.5 10.9404 1.5 10.25V2.75ZM9.25 3H6.75V13H9.25V3ZM10.75 8H13V3H10.75V8ZM3 3H5.25V10H3V3Z"
    />
  </Svg>
);
export default KanbanOutlined;
