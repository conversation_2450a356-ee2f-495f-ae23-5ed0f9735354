{"extends": "../../scripts/tsconfig/lib.tsconfig.json", "compilerOptions": {"target": "ES2019", "module": "CommonJS", "moduleResolution": "node", "skipLibCheck": true, "resolveJsonModule": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "declaration": false, "outDir": "dist"}, "include": ["src"], "exclude": ["./node_modules", "**/*.test.ts"]}