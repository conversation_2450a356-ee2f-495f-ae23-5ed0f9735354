/* eslint-disable @typescript-eslint/no-explicit-any */

import * as vm from 'node:vm';
import type { VercelRequest, VercelResponse } from '@vercel/node';
import axios from 'axios';
import lodash from 'lodash';
import fetch from 'node-fetch';

// 避免被 tree-shaking，以便eval能require
export const supportLibs = {
  fetch,
  axios,
  lodash,
  _: lodash,
};

// POST
export default async function handler(req: VercelRequest, res: VercelResponse) {
  const edgeToken = process.env.EDGE_TOKEN;
  const headerEdgeToken = req.headers['edge-token'];
  if (edgeToken !== headerEdgeToken) {
    res.status(500).json({
      error: 'Edge Token Auth fail',
      // header: req.headers,
      // edgeToken,
    });
    return;
  }

  const { body } = req;
  try {
    // eslint-disable-next-line no-eval
    const runResult = await vm.runInNewContext(body, { ...global, ...supportLibs });

    res.status(200).json({
      script: body,
      return: runResult,
    });
  } catch (e: any) {
    console.error(e);
    res.status(500).json({
      err: e.message,
    });
  }
}
