import type { IAIWriterContext } from '@bika/contents/config/client/ai/ai-writer/ai-writer';
import { AIWriterSO } from '@bika/domains/ai/server/ai-writer-so';
import { AuthController } from '@bika/domains/auth/apis';
import { UserSO } from '@bika/domains/user/server/user-so';
import { type AIWriter, AIWriterSchema } from '@bika/types/ai/bo';
import { CONST_PREFIX_AI_MESSAGE } from '@bika/types/database/vo';
import { createUIMessageStream, createUIMessageStreamResponse } from 'ai';
import assert from 'assert';
import { generateNanoID } from 'basenext/utils';
import { headers } from 'next/headers';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  const { prompt, writer, forceLocale, spaceId } = await req.json();
  const writerBO: AIWriter = AIWriterSchema.parse(writer);

  const auth = await AuthController.getAuthByNextRequest(await headers());
  assert(auth);
  const user = await UserSO.init(auth.user.id);

  // 获取请求的用户
  const locale = forceLocale || auth.user.settings?.locale || 'en';

  const context: IAIWriterContext = {
    locale,
    createdAt: new Date().toISOString(),
    userId: auth.user.id,
    spaceId,
  };

  return createUIMessageStreamResponse({
    headers: {
      'Content-Type': 'text/event-stream',
    },
    stream: createUIMessageStream({
      execute: async ({ writer }) => {
        await AIWriterSO.quickWrite(writerBO, prompt, context, user, writer);
      },
      onError: (error) => {
        return JSON.stringify(error);
      },
      generateId: () => generateNanoID(CONST_PREFIX_AI_MESSAGE),
    }),
  });
}
