import { errors, ServerError } from '@bika/contents/config/server/error';
import { AIChatSO } from '@bika/domains/ai/server';
import { createResumableStream } from '@bika/domains/ai/server/stream-context';
import { AuthController } from '@bika/domains/auth/apis';
import { createFetchRequestContext } from '@bika/server-orm/trpc';
import type { AIMessageBO } from '@bika/types/ai/bo';
import type { AIResolveVO } from '@bika/types/ai/vo';
import { createUIMessageStream, createUIMessageStreamResponse } from 'ai';
import assert from 'assert';
import type { Locale } from 'basenext/i18n';
import _ from 'lodash';
import { headers } from 'next/headers';

// Allow streaming responses up to 300 seconds
export const maxDuration = 300;

export async function POST(req: Request) {
  const {
    id: chatId,
    lastMessage,
    forceLocale,
    option: userOption,
    contexts = [],
  } = await req.json();

  assert(chatId, 'chatId is required');

  console.log('/api/ai/chat chat contexts', contexts);

  const auth = await AuthController.getAuthByNextRequest(await headers());
  assert(auth);

  const wizardSO = await AIChatSO.get(chatId);

  // 获取请求的用户
  const locale: Locale = forceLocale || auth.user.settings?.locale || 'en';

  const ctx = await createFetchRequestContext({ req, resHeaders: new Headers() });

  // const chatMessages = messages as ChatMessages;
  // const lastMessage = chatMessages[chatMessages.length - 1];
  const lastUIMessage = lastMessage as AIMessageBO;

  // 构造 AI Resolve DTO，为了兼容旧的 wizard 模式 + AI SDK
  // let resolve: AIResolveVO;
  // if (lastUIMessage.role === 'assistant') {
  //   // 理解为 tool result,这个是客户端执行的tool, 需要先放到消息历史中
  //   const lastMessageToolsInvokes = lastUIMessage.parts.filter((part) => isToolUIPart(part));
  //   const lastToolInvoke = lastMessageToolsInvokes?.[lastMessageToolsInvokes!.length - 1];
  //   assert(lastToolInvoke!.state === 'output-available', 'Tool result should be in result state');

  //   resolve = {
  //     type: 'TOOL',
  //     toolInvocation: lastToolInvoke!,
  //     option: userOption,
  //   };
  // } else {
  const resolve: AIResolveVO = {
    type: 'MESSAGE',
    message: lastUIMessage,
    option: userOption,
    contexts,
    // lastMessage: lastUIMessage,
  };
  // }

  return createUIMessageStreamResponse({
    stream: createUIMessageStream({
      execute: async ({ writer }) => {
        await wizardSO.resolve(ctx, resolve, 'en', writer);
      },
      onError: (error) => {
        console.error('ChatStreamError', error);
        // console.trace();
        if (
          error instanceof ServerError &&
          error.code === errors.billing.ai_credit_not_enough.code
        ) {
          return JSON.stringify(error);
        }
        throw error;

        // if (ToolExecutionError.isInstance(error)) {
        //   // tool execution error
        //   return JSON.stringify({
        //     name: 'AI_ToolExecutionError',
        //     message: (error as any).message,
        //     toolCallId: (error as any).toolCallId,
        //     errors: {
        //       [(error as any).toolCallId]: (error as any).message,
        //     },
        //   });
        // }
      },
    }),
    consumeSseStream: async ({ stream }) => {
      await createResumableStream(wizardSO.id, auth.user.id, stream);
    },
  });
}
