import { NodeSO } from '@bika/domains/node/server/node-so';
import { ShortURLSO } from '@bika/domains/node/server/short-url-so';
import { ShareSO } from '@bika/domains/permission/server/share-so';
import { StandalonePage } from '@bika/ui/components/standalone/index';
import { redirect } from 'next/navigation';

interface Props {
  params: Promise<{ shortURL: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ShortUrlPage(props: Props) {
  const params = await props.params;
  const searchParams = await props.searchParams;

  const s = await ShortURLSO.getById(params.shortURL);
  if (s) {
    // Convert searchParams object to query string
    const urlSearchParams = new URLSearchParams(searchParams as Record<string, string>);
    const queryString = urlSearchParams.toString();

    if (s.model.relationType === 'NODE_RESOURCE') {
      const node = await NodeSO.init(s.model.relationId);
      const redirectUrl = `/space/${node.spaceId}/node/${node.id}${queryString ? `?${queryString}` : ''}`;

      redirect(redirectUrl);
    } else if (s.model.relationType === 'AI_CHAT') {
      const share = await ShareSO.findUnique({
        resourceId: s.model.relationId,
        resourceType: 'AI_CHAT',
      });
      if (share) {
        const redirectUrl = `/share/${share.id}?${queryString}`;
        redirect(redirectUrl);
      }
    } else {
      return (
        <>
          TODO Renderer: {params.shortURL}, {s.model.relationType} {s.model.relationId}
        </>
      );
    }
  }

  return <StandalonePage>{params.shortURL} Not found</StandalonePage>;
}
