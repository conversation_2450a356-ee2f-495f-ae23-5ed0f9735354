'use client';

import { useTR<PERSON><PERSON>uery } from '@bika/api-caller/context';
import { errors } from '@bika/contents/config/server/error/errors';
import { AIReplay } from '@bika/domains/ai/client/ai-history/replay';
import { AINodeVORenderer } from '@bika/domains/node-resources/ai-agent/vo-renderer';
import { AccessNeedLogin } from '@bika/domains/space/client/access-need-login';
import { NodeTreeBody } from '@bika/domains/space/client/sidebar-tree/node-tree-view/node-tree-body';
import DragPage from '@bika/domains/website/client/layout/drag-page/index';
import { BikaWebsiteLeftSidebar } from '@bika/domains/website/client/website-left-sidebar';
import type { NodeTreeVO, ShareResourceVO, ShareVO } from '@bika/types/node/vo';
import { SpaceContext } from '@bika/types/space/context';
import type { SpaceVO } from '@bika/types/space/vo';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { Box } from '@bika/ui/layout-components';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// import style from './page.module.css';

interface RouteParams {
  lang: Locale;
  shareId: string;
}

export interface PageProps {
  params: Promise<RouteParams>;
}

function ShareSpaceProvider(props: {
  children: React.ReactNode;
  space: Pick<SpaceVO, 'id' | 'name' | 'logo'>;
}) {
  return (
    <SpaceContext.Provider
      value={{
        data: {
          ...props.space,
          settings: {},
          memberCount: 0,
          usersMembersCount: 0,
          aiMembersCount: 0,
        },
        refetch: async () => {},
        changeSpaceName: async () => {},
        myInfo: null,
        refetchMyInfo: () => {},
        rootTeam: {
          id: '',
          name: '',
          isGuest: false,
          type: 'Team',
        },
        guestRootTeam: {
          id: '',
          name: '',
          isGuest: false,
          type: 'Team',
        },
        redDots: {},
        permission: null,
        useShortcutsNode: () => ({
          shortcutNode: undefined,
          setShortcutNode: () => {},
          refetch: () => {},
          isLoading: false,
        }),
        useRootNode: () => ({
          rootNode: null!,
          setRootNode: () => {},
          refetch: () => {},
          isLoading: false,
          findNode: () => undefined,
          setSectionNodeTree: () => {},
        }),
        getUIModal: () => null,
        showUIModal: () => {},
        showUIDrawer: () => {},
        getUIDrawer: () => null,
        showAICopilot: () => {},
        getAICopilot: () => null,
      }}
    >
      {props.children}
    </SpaceContext.Provider>
  );
}

function ShareResourceProvider(props: { children: React.ReactNode; resource: ShareResourceVO }) {
  const { resource } = props;

  return <ShareSpaceProvider space={resource.space}>{props.children}</ShareSpaceProvider>;
}

interface ShareSideBarProps {
  spaceInfo?: SpaceVO;
  shareNodeTree: NodeTreeVO[];
}

const ShareSideBar = (props: ShareSideBarProps) => {
  const { spaceInfo, shareNodeTree } = props;
  const router = useRouter();

  return (
    <>
      <div className={'flex flex-col items-center mt-4 space-y-2 mb-2'}>
        <AvatarImg
          avatar={spaceInfo?.logo}
          name={spaceInfo?.name}
          customSize={AvatarSize.Size40}
          shape="SQUARE"
        />
        <div
          className={'bg-[--bg-controls] text-[--text-secondary] text-b4 px-2 py-1 rounded-full '}
        >
          {spaceInfo?.name}
        </div>
      </div>
      <div className={'px-4'}>
        <NodeTreeBody
          nodeChildren={shareNodeTree}
          handleNodeChange={() => {
            router.push('/space');
          }}
          globalSharing
        />
      </div>
    </>
  );
};

export default function SharePage() {
  const params = useParams<{ shareId: string; lang: string }>();

  const shareId = params.shareId;
  // const lang = params.lang;

  const trpcQuery = useTRPCQuery();

  const { data, isError, error } = trpcQuery.share.detailInfo.useQuery({ id: shareId });

  const [resource, setResource] = useState<ShareResourceVO | null>(null);

  const [share, setShare] = useState<ShareVO | null>(null);

  useEffect(() => {
    if (data) {
      setShare(data.share);
      setResource(data.resource);
    }
  }, [data]);

  const wizardId = share?.resourceId;
  const autoReplay = share?.settings?.autoReplay;

  if (isError) {
    console.error('Error loading share information:', error?.data?.code);
    if (error?.data?.code === errors.share.need_auth.code) {
      return <AccessNeedLogin />;
    }
  }

  // todo node share?
  return (
    resource &&
    resource.type === 'AI_CHAT' && (
      <ShareResourceProvider resource={resource}>
        <DragPage
          localId="chat-share-sidebar"
          left={
            <Box height="100vh">
              <BikaWebsiteLeftSidebar
                sideBarSlot={
                  <ShareSideBar
                    spaceInfo={resource.space as SpaceVO}
                    shareNodeTree={
                      resource.agent.type === 'node' ? [resource.agent.node as NodeTreeVO] : []
                    }
                  />
                }
              />
            </Box>
          }
          middle={
            <Box
              flex={1}
              display="flex"
              overflow="auto"
              sx={{
                flexDirection: 'column',
              }}
            >
              {wizardId && autoReplay ? (
                <AIReplay wizardId={wizardId} />
              ) : (
                <AINodeVORenderer value={resource.agent} disabled disabledOperation />
              )}
            </Box>
          }
          maxWidth={580}
          minWidth={280}
          defaultWidth={280}
          allowLess={false}
        />
      </ShareResourceProvider>
    )
  );
}
