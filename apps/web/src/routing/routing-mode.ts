import type { NextRequest } from 'next/server';

/**
 * 获取路由中转模式:
 *
 * - WEB_PAGE: 静态网页，通常是多语言开头的
 * - SPACE: 通常是/space/XX，就是空间站路由，需要登录
 * - APP_PAGE: 应用程序相关的独立页面，脱离空间站，如publc表单、mission独立页等等
 *
 * @param request
 */
export function getRoutingMode(request: NextRequest): 'WEB_PAGE' | 'SPACE' | 'APP_PAGE' {
  const { pathname } = request.nextUrl;

  // 动态路由，即应用程序相关的，通常都需要登录，没登录自动redirect到/auth
  // 如非动态路由，通常是静态网页，多语言切换, /en/XX开头
  if (pathname.startsWith('/space')) {
    return 'SPACE';
  }
  const isAppPage = [
    '/api',
    '/r/',
    '/bika-admin',
    '/mission',
    '/node',
    // TODO: 有个例外，/node/{nodeId}和/node/{nodeId}/{viewId}是不需要登录的

    '/report',
    '/space',
    '/share',
    // '/auth', // 在matcher设置
  ].some((path) => pathname.startsWith(path));
  if (isAppPage) return 'APP_PAGE';

  return 'WEB_PAGE';
}
