name: 'Build Web Server Docker'
description: 'Build Docker Images'
inputs:
  dockerfile:
    description: Where is the Dockerfile?
    default: 'scripts/Dockerfile.web'
  image:
    description: Image name, default "web"
    default: bika-web
  username:
    description: repository_owner?
    required: true
  password:
    description: 'Docker repo password'
    required: true
  edition:
    description: Image Edition, default "bika"?
    default: 'bika'
runs:
  using: 'composite'
  steps:
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        registry: docker.io
        username: ${{ inputs.username }}
        password: ${{ inputs.password }}
    # - name: Docker meta
    #   id: meta
    #   uses: docker/metadata-action@v5
    #   with:
    #     # list of Docker images to use as base name for tags
    #     images: |
    #       bika/web
    #       ghcr.io/bika/web
    #     # generate Docker tags based on the following events/attributes
    #     tags: |
    #       type=schedule
    #       type=ref,event=branch
    #       type=ref,event=pr
    #       type=semver,pattern={{version}}
    #       type=semver,pattern={{major}}.{{minor}}
    #       type=semver,pattern={{major}}
    #       type=sha
    # 提取lerna.json中的版本号到环境变量
    - name: Get Version Number
      id: get_version_step
      shell: bash
      run: |
        echo "version=$(grep -o '"version": *"[^"]*"' lerna.json | cut -d '"' -f 4)" >> $GITHUB_OUTPUT
        echo "prerelease=$(grep -o '"version": *"[^"]*"' lerna.json | cut -d '"' -f 4 | cut -d '.' -f 3 | cut -d '-' -f 2)"  >> $GITHUB_OUTPUT

    # ${{ steps.commit_message_step.outputs.version }}
    - name: Build and push web docker
      uses: docker/build-push-action@v5
      with:
        cache-from: type=gha
        cache-to: type=gha
        file: ${{ inputs.dockerfile }}
        context: .
        push: true
        # 三个标签，:latest,  :latest-alpha,  :0.5.3-alpha.1
        tags: docker.io/vikadata/${{ inputs.image }}:latest,docker.io/vikadata/${{ inputs.image }}:latest-selfhost-${{ steps.get_version_step.outputs.prerelease }},docker.io/vikadata/${{ inputs.image }}:${{ steps.get_version_step.outputs.version }}
        #cache-from: type=gha
        #cache-to: type=gha,mode=max
