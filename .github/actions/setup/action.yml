name: 'Setup Node.js Environment, optional install'
description: 'Setup NodeJS, PNPM, Install Node.js Packages, and restore cache'
inputs:
  cache:
    description: '是否恢复pnpm缓存？默认是不开，因为下载缓存也要花时间，加快速度，如需要，请填pnpm'
    required: false
    default: ''

# outputs:
#   random-number:
#     description: "Random number"
#     value: ${{ steps.random-number-generator.outputs.random-number }}
runs:
  using: 'composite'
  steps:
    - uses: oven-sh/setup-bun@v2
      name: Set up bun
      with:
        # 最新版本无 bun-darwin-x64-baseline.zip ,锁定旧版
        bun-version: 1.1.38

    - uses: pnpm/action-setup@v3
      name: Set up pnpm
      with:
        version: 9.15.4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: ${{ inputs.cache }}

    # - uses: actions/cache@v4
    #   name: bun cache
    #   with:
    #     path: |
    #       ~/.bun/install/cache
    #       ${{ github.workspace }}/apps/web/.next/cache
    #     key: ${{ runner.os }}-bun-${{ hashFiles('bun.lockb') }}
    #     restore-keys: |
    #       ${{ runner.os }}-bun-

    # - name: Get pnpm store directory
    #   shell: bash
    #   run: |
    #     echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
    # - uses: actions/cache@v3
    #   name: Setup pnpm cache
    #   with:
    #     path: |
    #       ${{ env.STORE_PATH }}
    #       ${{ github.workspace }}/apps/web/.next/cache
    #     key: ${{ runner.os }}-pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}
    #     restore-keys: |
    #       ${{ runner.os }}-pnpm-store-

    # - name: Install
    #   if: ${{ inputs.install == 'true' }}
    #   shell: bash
    #   run: |
    #     make init
    #     make install
