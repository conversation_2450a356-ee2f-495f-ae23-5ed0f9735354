name: 'Bump Version Commit'
description: 'Do Git Commit after Bump Version'
# inputs:
#   who-to-greet:  # id of input
#     description: 'Who to greet'
#     required: true
#     default: 'World'
# outputs:
#   random-number:
#     description: "Random number"
#     value: ${{ steps.random-number-generator.outputs.random-number }}
runs:
  using: 'composite'
  steps:
    # 提取package.json中的版本号到环境变量
    - name: Set commit message
      id: commit_message_step
      shell: bash
      run: |
        echo "commit_message=$(grep -o '"version": *"[^"]*"' apps/web/package.json | cut -d '"' -f 4)" >> $GITHUB_OUTPUT

    - uses: stefanzweifel/git-auto-commit-action@v5
      with:
        commit_message: 'ci(bump): Bump version to ${{ steps.commit_message_step.outputs.commit_message }}'
