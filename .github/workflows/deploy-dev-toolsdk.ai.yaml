name: Deploy dev.toolsdk.ai

env:
  GITHUB_BUILD_NUMBER: ${{ github.run_number }}

on:
  push:
    branches:
      - develop
    paths:
      - 'scripts/Dockerfile.toolsdk'
      - '**/*.tsx'
      - '**/*.ts'
      - '.github/**'
  workflow_dispatch:

# Using concurrency to cancel previous runs
concurrency:
  group: '${{ github.workflow }}@${{  github.head_ref || github.ref }}' # Grouping all runs by deploy
  cancel-in-progress: false # This cancels in-starting job within the same group

jobs:
  deploy-toolsdk-ai:
    name: Build and push docker web (dev.toolsdk.ai)
    runs-on: ubicloud-standard-4 # github-runners-set-k3s
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Env config
        shell: bash
        # Init .env.local on build & link /public
        run: |
          make env-develop
          make link-next-public

      # Docker toolsdk-web
      - name: Build Docker (toolsdk-web)
        uses: ./.github/actions/build-push-docker
        with:
          dockerfile: scripts/Dockerfile.toolsdk
          image: toolsdk
          edition: toolsdk
          username: vikadata
          password: ${{ secrets.CR_PAT }}
          # 仅在手动触发时推送到dockerhub
          pushDockerHub: ${{ github.event_name == 'workflow_dispatch' }}
