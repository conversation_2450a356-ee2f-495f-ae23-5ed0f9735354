name: Deploy (develop)

env:
  GITHUB_BUILD_NUMBER: ${{ github.run_number }}
  EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

on:
  push:
    branches:
      - develop
    paths:
      - '**/*.tsx'
      - '**/*.ts'
      - '.github/**'
  workflow_dispatch:

# Using concurrency to cancel previous runs
concurrency:
  group: '${{ github.workflow }}@${{  github.head_ref || github.ref }}' # Grouping all runs by deploy
  cancel-in-progress: false # This cancels in-starting job within the same group

jobs:
  deploy-db:
    name: Deploy DB Migration and Seed
    runs-on: ubicloud-standard-2 # github-runners-set-k3s
    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the changed files back to the repository.
      contents: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.CR_PAT }}

      - name: Setup Environment
        uses: ./.github/actions/setup
        with:
          cache: pnpm

      - name: Install Dependencies
        uses: ./.github/actions/install

      - name: Deploy DB Migrate
        uses: ./.github/actions/deploy-db-migrate
        with:
          minio: ${{ secrets.MINIO_URL }}
          postgres: ${{ secrets.PG_DATABASE_URL_DEV }}
          es: ${{ secrets.ES_URL_DEV }}

      - name: Bump Version
        uses: ./.github/actions/bump-version
        with:
          bump: prerelease --preid alpha

      - name: Commit new Bump Version
        uses: ./.github/actions/bump-version-commit

  # deploy-vercel-serverless:
  #   needs: deploy-db
  #   name: Deploy bika-dev.vercel.app
  #   runs-on: github-runners-set-k3s
  #   steps:
  #     - name: Checkout Code
  #       uses: actions/checkout@v4

  #     - name: Setup Environment
  #       uses: ./.github/actions/setup

  #     - name: Deploy to Vercel
  #       uses: ./.github/actions/deploy-vercel
  #       with:
  #         vercel_org_id: ${{ secrets.VERCEL_ORG_ID }}
  #         vercel_project_id: ${{ secrets.VERCEL_DEV_PROJECT_ID }}
  #         prod: true
  #         token: ${{ secrets.VERCEL_BIKA_DEV_TOKEN }}

  build-push-docker-web:
    needs: deploy-db
    name: Build and push docker web (dev.bika.ai)
    runs-on: ubicloud-standard-4 #github-runners-set-k3s
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Environment
        uses: ./.github/actions/setup

      # init container
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Env config
        shell: bash
        # Init .env.local on build
        run: |
          make env-develop
      # docker web (main)
      - name: Build Bika App Docker (dev.bika.ai) (init+web+server+doc)
        uses: ./.github/actions/build-push-docker
        with:
          dockerfile: scripts/Dockerfile.bika
          image: bika
          username: vikadata
          password: ${{ secrets.CR_PAT }}
          # 仅在手动触发时推送到dockerhub
          pushDockerHub: ${{ github.event_name == 'workflow_dispatch' }}

      # - name: Build Init Container (dev.bika.ai)
      #   uses: ./.github/actions/build-push-docker
      #   with:
      #     dockerfile: scripts/Dockerfile.init
      #     image: bika-init
      #     hostname: https://dev.bika.ai
      #     username: vikadata
      #     password: ${{ secrets.CR_PAT }}

      # # docker web
      # - name: Build Web Server Docker (dev.bika.ai)
      #   uses: ./.github/actions/build-push-docker
      #   with:
      #     dockerfile: scripts/Dockerfile.web
      #     image: bika-web
      #     hostname: https://dev.bika.ai
      #     username: vikadata
      #     password: ${{ secrets.CR_PAT }}

      # # Docker node server
      # - name: Build Node Server Docker (dev-api.bika.ai)
      #   uses: ./.github/actions/build-push-docker
      #   with:
      #     dockerfile: scripts/Dockerfile.server-node
      #     # Node Server
      #     image: bika-server
      #     hostname: https://dev-api.bika.ai
      #     username: vikadata
      #     password: ${{ secrets.CR_PAT }}

      # # Docker doc (hocuspouse) server
      # - name: Build Document Server Docker (dev-doc.bika.ai)
      #   uses: ./.github/actions/build-push-docker
      #   with:
      #     dockerfile: scripts/Dockerfile.server-doc
      #     # Node Server
      #     image: bika-server-doc
      #     hostname: https://dev-doc-ws.bika.ai
      #     username: vikadata
      #     password: ${{ secrets.CR_PAT }}

      # # Docker bun server
      # - name: Build Docker (dev-api-bun.bika.ai)
      #   uses: ./.github/actions/build-push-docker
      #   with:
      #     dockerfile: scripts/Dockerfile.server-bun
      #     image: bika-server-bun
      #     hostname: https://dev-api-bun.bika.ai
      #     username: vikadata
      #     password: ${{ secrets.CR_PAT }}

      # # Docker bun-exe server
      # - name: Build Docker (dev-api-bun-exe.bika.ai)
      #   uses: ./.github/actions/build-push-docker
      #   with:
      #     dockerfile: scripts/Dockerfile.server-bun-exe
      #     image: bika-server-bun-exe
      #     hostname: https://dev-api-bun-exe.bika.ai
      #     username: vikadata
      #     password: ${{ secrets.CR_PAT }}

      # 安装依赖，为了storybook
      - name: Install Dependencies
        uses: ./.github/actions/install

      # UI Storybook
      - name: Storybook build and pub
        uses: ./.github/actions/storybook
        with:
          CR_PAT: ${{ secrets.CR_PAT }}

      # 跑postman测试
      # - name: Run next task
      #   run: |
      #     echo "Running next task..."
      #     # 在这里添加您要执行的下一个任务

  # 开发环境，改成定时每3小时，避免老是中断
  # build-ios:
  #   name: Build and push iOS (dev.bika.ai)
  #   needs: deploy-db
  #   runs-on: imac
  #   concurrency: # ios编译只有一台机，所以不要并发
  #     group: build-ios
  #     cancel-in-progress: true
  #   steps:
  #     - name: Checkout Code
  #       uses: actions/checkout@v4
  #     - run: git pull

  #     - name: Setup Environment
  #       uses: ./.github/actions/setup

  #     - name: Install Dependencies
  #       uses: ./.github/actions/install

  #     - name: Build iOS
  #       uses: ./.github/actions/build-ios
  #       with:
  #         apple_app_connect_password: ${{ secrets.APPLE_APP_CONNECT_PASSWORD }}
