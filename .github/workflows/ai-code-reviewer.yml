name: AI Code Review

permissions:
  contents: read
  pull-requests: write

on:
  workflow_dispatch:

  # pull_request:
  #   types: [opened, synchronize, reopened]
  #   branches:
  #     - develop
  # pull_request_review_comment:
  #   types: [created]

concurrency:
  group: ${{ github.repository }}-${{ github.event.number || github.head_ref || github.sha }}-${{ github.workflow }}-${{ github.event_name == 'pull_request_review_comment' && 'pr_comment' || 'pr' }}
  cancel-in-progress: ${{ github.event_name != 'pull_request_review_comment' }}

jobs:
  ai-code-review:
    runs-on: github-runners-set-k3s
    steps:
      - uses: coderabbitai/ai-pr-reviewer@latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENAI_BASE_URL: ${{ secrets.OPENAI_BASE_URL }}
        with:
          debug: false
          review_simple_changes: false
          review_comment_lgtm: false
          openai_light_model: gpt-3.5-turbo-0125
          openai_heavy_model: gpt-3.5-turbo-0125
          language: zh-CN
