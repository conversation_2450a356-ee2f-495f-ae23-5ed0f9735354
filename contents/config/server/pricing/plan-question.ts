import type { Locale } from 'basenext/i18n';
import { getServerDictionary } from '../../../i18n/server';

export type SpacePlanQAType = { q: string; a: string };
export type SpacePlanQAArrayType = SpacePlanQAType[];

/**
 * Q&A常见问题内容
 *
 * 该配置被pricing页面、blog文章、landing page均可共享
 *
 */
export const SpacePlanQuestion: { [lang in Locale]: SpacePlanQAArrayType } = {
  en: [
    {
      q: 'Quick one-sentence introduction: What is Bika.ai?',
      a: 'Bika.ai is an AI automation tool that optimizes data management and task processing by combining tabular databases and AI technology, helping you execute workflows more efficiently.',
    },
    {
      q: 'What make Bika.ai so unique?',
      a: getServerDictionary('en').slogan.usp,
    },
    {
      q: 'The English abbreviation "BIKA" stands for what meaning?',
      a: 'B-I-K-A.ai stands for `Boss In Kingdom of Agents` or `Business Intelligence and Knowledge Automation with AI`. ',
    },
    {
      q: 'How does Bika.ai automate tasks with AI?',
      a: "Bika.ai starts with 'automation', not a 'chatbot', by setting automations and some triggering conditions, such as scheduling and new data addition, to automate tasks.\nIn fact, Bika.ai is an 'AI automation tool', not an 'AI assistant'. It uses automation features without relying on large AI models, meaning it does not consume AI inference costs. With Bika.ai's core components like tasks, summaries, and resources, many tasks can be efficiently completed, saving you time and allowing you to enjoy life more.\nVisit the [Template Center](https://bika.ai/en/template) to find solutions that suit you.",
    },
    {
      q: 'Is Bika.ai free to use?',
      a: 'Yes, Bika.ai is currently completely free to use, and the allowances are very generous. You can refer to the free specifications.\nIf you exceed the free tier, or if you want to enjoy customized features, private deployment, and other services, you can [contact sales](?m=eyJuYW1lIjoiQ09OVEFDVF9TRVJWSUNFIn0=).',
    },
    {
      q: 'What is the difference between Bika.ai and AI assistants like ChatGPT, Gemini?',
      a: 'ChatGPT, Gemini, Claude, and other AI assistants are based on large-model AI chatbots, requiring a substantial AI inference cost.\nWhereas Bika.ai is an AI automation and database tool that automates tasks.\nBoth are completely different products; you might want to browse the AI automation template center to see if there are templates that meet your daily needs [Templates](https://bika.ai/en/template).',
    },
    {
      q: 'What is the difference between Bika.ai and spreadsheet database?',
      a: "There are many excellent spreadsheet database tools on the market, such as [AITable.ai](https://aitable.ai), [Airtable](https://airtable.com), etc.\nIndeed, the core of Bika.ai is similar to spreadsheets database: tabular databases, automation, etc.\nspreadsheet database excel in collaboration, but Bika.ai is focused on 'tasks', 'reporting', and 'AI automation', not primarily on spreadsheet database. Bika.ai aims to be an AI automation tool that fully utilizes data.",
    },
    {
      q: 'Does Bika.ai get poor performance when the single database records reaches tens of thousands or hundreds of thousands of rows and the associations become more complex?',
      a: "No, it does not. Bika.ai's data tables are architected and designed to handle massive data volumes. The underlying infrastructure combines technologies from OLTP business databases, OLAP analytics databases, NoSQL databases, time series databases, vector databases and index databases to ensure that performance remains fast and responsive even at the millions or billions of rows scale.",
    },
    {
      q: "What is the 'Space' in Bika.ai?",
      a: "'Space' is a collaboration platform for all members of your organization or team. By clicking on the space icon in the top left corner of the Bika.ai space, you can access your personalized management and invited spaces.",
    },
    {
      q: 'How many paid spaces do I own after making a payment?',
      a: 'Bika.ai operates on a space subscription model. When you purchase a subscription, you gain access to a space. The number of spaces you own is equivalent to the number of subscriptions you have purchased. Each space includes specific usage rights as outlined in your purchase terms.',
    },
    {
      q: "What does 'Resources' mean?",
      a: "'Resources' refers to the file nodes in the directory tree on the left side of your space resources section, such as automations, databases, forms, dashboards, etc.\nDeleted resources are not counted. Creating a new database and a new form will add two file nodes to your count. Deleting one of these items will reduce the count by one.",
    },
    {
      q: "How does the Bika.ai team 'eat your own dog food' (use their own product)?",
      a: "That's a great question, we are very passionate about using Bika.ai internally.\nInternally, we use Bika.ai for: daily standup notifications/collection/summarization, weekly scrum iteration notifications/collection/summarization, scheduling Twitter public account article approvals and posts, daily sales data analysis report generation, automated feature request and bug management, periodic marketing email dispatches, and more - trying to automate as many repetitive, routine tasks as possible with AI.\nThe Bika.ai team runs 3 SaaS platform products, over 30 deployments, millions of users, and tens of thousands of team customers, and we desperately need a more AI-automated tool to reduce our own repetitive work, which is why we developed Bika.ai in the first place.\nWe hope our customers can use Bika.ai in a 'use and go' fashion - just get in, set it up, and then let the AI automate most of the work going forward.",
    },
    {
      q: 'How does Bika.ai help improve work efficiency?',
      a: 'Bika.ai helps users automate routine tasks through its powerful automation features. Users can set up automation tasks according to their needs, such as data update notifications, task scheduling, and automatic report generation, thereby reducing manual operations and increasing work efficiency. You can directly visit the [Template Center](https://bika.ai/en/template) to find solutions that suit you.',
    },
    {
      q: "What are the features of Bika.ai's AI automation?",
      a: "Bika.ai's AI automation features allow users to create condition-based tasks that can be automatically triggered when certain conditions are met. For example, when a data table receives new data, Bika.ai can automatically process and analyze the data, and even send notifications to relevant personnel. Visit the [Template Center](https://bika.ai/en/template) to experience it.",
    },
    {
      q: 'What are the automation templates in Bika.ai?',
      a: 'Automation templates are pre-set task workflows that users can directly apply to automate specific work processes within 3 minutes. These templates cover a wide range of common scenarios from data entry to complex data processing, allowing users to quickly deploy and leverage AI technology. It is recommended to visit the [Template Center](https://bika.ai/en/template) to use them.',
    },
    {
      q: 'Does Bika.ai support team collaboration and permissions features?',
      a: "Yes, Bika.ai provides the 'Space' feature, a team collaboration platform that not only allows internal team members to share resources, manage data, and collaborate, but also supports collaboration with external teams or individuals well. Spaces support personalized settings and permission management to ensure efficient team collaboration.",
    },
  ],
  'zh-CN': [
    {
      q: 'Bika.ai是免费使用的吗？',
      a: `是的，目前Bika.ai是完全免费使用，而且用量非常慷慨，你可以参考免费规格。\n如果您超出了免费规格，或者，你想享受定制功能、私有化部署等更多服务，可以[联系销售](?m=eyJuYW1lIjoiQ09OVEFDVF9TRVJWSUNFIn0=)`,
    },
    {
      q: '是什么让 Bika.ai 如此独特？',
      a: getServerDictionary('zh-CN').slogan.usp,
    },
    {
      q: '一句话快速介绍：什么是Bika.ai?',
      a: 'Bika.ai 是一个AI自动化工具，通过结合表格型数据库和AI技术，自动化地优化数据管理和任务处理，帮助您更高效地执行工作流程。',
    },
    {
      q: '"BIKA" 这个缩写单词代表什么意思？',
      a: 'B-I-K-A 是「智能体王国的老板 (Boss In Kingdom of Agents)」或「业务智能与知识自动化 (Business Intelligence and Knowledge Automation)」的缩写，一人公司+业智能+知识自动化+AI。',
    },
    {
      q: 'Bika.ai是怎么做到AI自动化做事的？',
      a: 'Bika.ai的起点不是“聊天框”，而是“自动化”，通过设置自动化和一些触发条件，如定时、新数据新增等，来触发AI自动化做事。\n实际上，Bika.ai是一个“AI自动化工具”，而不是一个“AI助手”，它使用自动化功能而不依赖于大型AI模型，这意味着不需要消耗AI推理成本。通过Bika.ai的任务、汇总、资源等核心组件，可以有效地完成许多工作，节省您的时间，让您有更多时间享受生活。\n直接前往[模板中心](https://bika.ai/template)来获取适合你的解决方案。',
    },

    {
      q: 'Bika.ai与Kimi、ChatGPT等AI助手有什么区别？',
      a: 'Kimi、ChatGPT、文心一言等AI助手，是基于大模型的AI聊天助手，需要消耗大量的AI推理成本，\n而Bika.ai是一个AI自动化、数据库工具，自动化地完成任务。\n两者是完全不一样的产品，你可以浏览一下AI自动化模板中心，看看有没有满足你日常需求的[模板](https://bika.ai/template)？',
    },
    {
      q: 'Bika.ai与多维表格有什么区别？',
      a: '市面上有很多优秀的多维表格工具，如多维表格首创者[vika维格云](https://vika.cn)、[飞书多维表格](https://www.feishu.cn/product/base) 、腾讯企业微信智能表格等。\n确实Bika.ai与多维表格的核心是类似的：表格型数据库、自动化等。\n多维表格更擅长协作，但Bika.ai是以“任务”、“汇报”、“AI自动化”为主，并不以表格为主，Bika.ai旨在利用AI充分把数据用起来的AI自动化工具。',
    },
    {
      q: 'Bika.ai在单表数据量、关联引用变多后，如几万行、几十万行，会卡吗？',
      a: '不会，Bika.ai的数据表在架构设计时以海量数据的方式设计和考虑，底层综合混用了业务数据库OLTP、分析数据库OLAP、NoSQL数据库、时序数据库、索引数据库、向量数据库的技术，来确保千万级、亿级数据量都能保持流程和高性能的表现。',
    },
    {
      q: 'Bika.ai中的"空间站"是什么?',
      a: '"空间站"是您的组织或团队所有成员的协作平台。通过单击Bika.ai工作台左上角的空间图标,可以访问您的个性化管理和受邀空间。',
    },
    {
      q: '付款后我拥有多少个付费空间？',
      a: 'Bika.ai 采用空间订阅模式。当您购买订阅时，您将获得一个空间。您拥有的空间数量等于您购买的订阅数量。每个空间都包含您购买条款中规定的特定使用权。',
    },
    {
      q: '什么是"资源"?',
      a: '"资源"一词指的是您空间资源部分左侧目录树文件中的文件节点，如自动化、数据库、表单、仪表板等。\n被删除的资源不会被计算在内。创建一个新的数据库和一个新的表单会增加两个文件节点到您的计数中。删除其中一个项目会使计数减少一个。',
    },
    {
      q: 'Bika.ai的团队是怎样”吃自己的狗粮“(应用自己的产品)的？',
      a: `好问题，我们自己内部非常热爱使用Bika.ai。
在内部，我们应用Bika.ai于：每日晨会通知/收集/汇总、每周scrum工作迭代通知/收集/汇总、定时给运营经理审批并发送twitter公众号文章、每日销售数据分析报告生成、功能需求和BUG AI自动化管理、定期营销邮件发送等等等，尽量将重复性、日常化的工作交给AI自动化。
Bika.ai团队运行着3个SaaS平台产品、30+个部署、百万的用户、数万个团队客户，我们也急需一个更AI自动化的工具，来减少我们的重复工作，这是我们开发Bika.ai的初心。
我们希望我们的用户，使用Bika.ai是「用完就走」的 —— 只要进去软件，初始化和设置后，之后大部分的工作，都交给AI自动化。`,
    },
    {
      q: 'Bika.ai如何帮助提高工作效率？',
      a: 'Bika.ai 通过其强大的自动化功能帮助用户自动执行常规任务。用户可以根据自己的需求设置自动化任务，比如数据更新通知、任务调度和自动报告生成等，从而减少人工操作，提高工作效率。你可以直接前往[模板中心](https://bika.ai/template)来获取适合你的解决方案。',
    },
    {
      q: 'Bika.ai 的AI自动化功能有哪些特点？',
      a: 'Bika.ai 的AI自动化功能允许用户创建基于条件的任务，这些任务可以在满足特定条件时自动触发。例如，当一个数据表接收到新数据时，Bika.ai 可以自动进行数据处理和分析，甚至发送通知给相关人员。直接前往[模板中心](https://bika.ai/template)来感受吧。',
    },
    {
      q: 'Bika.ai 中的自动化模板是什么？',
      a: '自动化模板是预先设定的任务流程，用户可以直接应用这些模板，3分钟来自动化特定的工作流程。这些模板涵盖了从数据录入到复杂数据处理的各种常见场景，使用户能够快速部署和利用AI技术。建议你可以直接前往[模板中心](https://bika.ai/template)进行使用。',
    },
    {
      q: 'Bika.ai 是否支持团队协作及权限功能？',
      a: '是的，Bika.ai 提供了“空间站”功能，这是一个团队协作平台，，不仅允许团队内部成员共享资源、管理数据和协同工作，还能良好地支持与外部团队或个人的协作。空间站支持个性化设置和权限管理，确保团队成员可以高效地协作。',
    },
    {
      q: 'Bika.ai是否只适合个人使用？企业团队会不适合？',
      a: `不会。Bika.ai在设计第一天就考虑到个人、团队、企业级功能的需求。\n团队在Bika.ai中，工作单元是「空间站」，一个空间站可以有多个成员，也可以创建多个部门、多个角色。\n在权限和安全方面，Bika.ai也提供了非常丰富的权限与安全设置，如空间站的公开性、成员的权限、资源的权限、公开分享、列权限、水印、邮箱限制等等等。\n如果你有进一步的功能需要，如私有化部署、功能定时等，请联系我们的高级管理人员：[联系我们](?m=eyJuYW1lIjoiQ09OVEFDVF9TRVJWSUNFIn0=)`,
    },
  ],
  'zh-TW': [
    {
      q: '一句話快速介紹：什麼是Bika.ai?',
      a: 'Bika.ai 是一個AI自動化工具，透過結合表格型數據庫和AI技術，自動化地優化數據管理和任務處理，幫助您更高效地執行工作流程。',
    },
    {
      q: '是什麽让 Bika.ai 如此独特？',
      a: getServerDictionary('zh-TW').slogan.usp,
    },
    {
      q: '"BIKA" 這個縮寫單詞代表什麼意思？',
      a: 'B-I-K-A 是「智能體王國的老闆 (Boss In Kingdom of Agents)」或「業務智能與知識自動化 (Business Intelligence and Knowledge Automation)」的縮寫，一人公司+業智能+知識自動化+AI。',
    },
    {
      q: 'Bika.ai是怎麼做到AI自動化做事的？',
      a: 'Bika.ai的起點不是“聊天框”，而是“自動化”，透過設置自動化和一些觸發條件，如定時、新數據新增等，來觸發AI自動化做事。\n實際上，Bika.ai是一個“AI自動化工具”，而不是一個“AI助手”，它使用自動化功能而不依賴於大型AI模型，這意味著不需要消耗AI推理成本。透過Bika.ai的任務、匯總、資源等核心組件，可以有效地完成許多工作，節省您的時間，讓您有更多時間享受生活。\n直接前往[模板中心](https://bika.ai/zh-TW/template)來獲取適合你的解決方案。',
    },
    {
      q: 'Bika.ai是免費使用的嗎？',
      a: '是的，目前Bika.ai是完全免費使用，而且用量非常慷慨，你可以參考免費規格。\n如果您超出了免費規格，或者，你想享受定制功能、私有化部署等更多服務，可以[聯繫銷售](?m=eyJuYW1lIjoiQ09OVEFDVF9TRVJWSUNFIn0=)',
    },
    {
      q: 'Bika.ai與ChatGPT、Gemini等AI助手有什麼區別？',
      a: 'ChatGPT、Gemini、Claude等AI助手，是基於大模型的AI聊天助手，需要消耗大量的AI推理成本，\n而Bika.ai是一個AI自動化、數據庫工具，自動化地完成任務。\n兩者是完全不一樣的產品，你可以瀏覽一下AI自動化模板中心，看看有沒有滿足你日常需求的[模板](https://bika.ai/zh-TW/template)？',
    },
    {
      q: 'Bika.ai與多維表格有什麼區別？',
      a: '市面上有很多優秀的多維表格工具，如[AITable.ai](https://aitable.ai)、[Airtable](https://airtable.com) 等。\n確實Bika.ai與多維表格的核心是類似的：表格型數據庫、自動化等。\n多維表格更擅長協作，但Bika.ai是以“任務”、“彙報”、“AI自動化”為主，並不以表格為主，Bika.ai旨在利用AI充分把數據用起來的AI自動化工具。',
    },
    {
      q: 'Bika.ai 在單表數據量、關聯引用變多後，如幾萬行、幾十萬行，會卡住嗎？',
      a: '不會。Bika.ai 的資料表在架構設計時就已考慮到海量數據的需求，底層綜合運用了 OLTP 業務資料庫、OLAP 分析資料庫、NoSQL 資料庫、時間序列資料庫、索引資料庫以及向量資料庫等技術，確保即使達到千萬級、億級的數據量,系統仍能保持流暢高效的效能表現。',
    },
    {
      q: 'Bika.ai中的"空間站"是什麼?',
      a: '"空間站"是您的組織或團隊所有成員的協作平台。通過單擊Bika.ai工作台左上角的空間圖標,可以訪問您的個性化管理和受邀空間。',
    },
    {
      q: '付款後我擁有多少個付費空間？',
      a: 'Bika.ai 採用空間訂閱模式。當您購買訂閱時，您將獲得一個空間。您擁有的空間數量等於您購買的訂閱數量。每個空間都包含您購買條款中規定的特定使用權。',
    },
    {
      q: '什麼是"資源"?',
      a: '"資源"一詞指的是您空間資源部分左側目錄樹文件中的文件節點，如自動化、數據庫、表單、儀表板等。\n被刪除的資源不會被計算在內。創建一個新的數據庫和一個新的表單會增加兩個文件節點到您的計數中。刪除其中一個項目會使計數減少一個。',
    },

    {
      q: 'Bika.ai 的團隊是如何「吃自己的狗糧」的？',
      a: '好問題,我們自己內部非常熱愛使用 Bika.ai。\n在內部,我們應用 Bika.ai 於:每日晨會通知/收集/彙整、每週 scrum 工作迭代通知/收集/彙整、定時給運營經理審批並發送 twitter 公眾號文章、每日銷售數據分析報告生成、功能需求和 BUG AI 自動化管理、定期行銷電郵發送等等等,盡量將重複性、日常化的工作交給 AI 自動化。\nBika.ai 團隊運營著 3 個 SaaS 平台產品、30+ 個部署、百萬的用戶、數萬個團隊客戶,我們也急需一個更 AI 自動化的工具,來減少我們的重複工作,這是我們開發 Bika.ai 的初衷。\n我們希望我們的用戶,使用 Bika.ai 是「用完就走」的 —— 只要進去軟體,初始化和設置後,之後大部分的工作,都交給 AI 自動化。',
    },
    {
      q: 'Bika.ai如何幫助提高工作效率？',
      a: 'Bika.ai 通過其強大的自動化功能幫助用戶自動執行常規任務。用戶可以根據自己的需求設置自動化任務，比如數據更新通知、任務調度和自動報告生成等，從而減少人工操作，提高工作效率。你可以直接前往[模板中心](https://bika.ai/zh-TW/template)來獲取適合你的解決方案。',
    },
    {
      q: 'Bika.ai 的AI自動化功能有哪些特點？',
      a: 'Bika.ai 的AI自動化功能允許用戶創建基於條件的任務，這些任務可以在滿足特定條件時自動觸發。例如，當一個數據表接收到新數據時，Bika.ai 可以自動進行數據處理和分析，甚至發送通知給相關人員。直接前往[模板中心](https://bika.ai/zh-TW/template)來感受吧。',
    },
    {
      q: 'Bika.ai 中的自動化模板是什麼？',
      a: '自動化模板是預先設定的任務流程，用戶可以直接應用這些模板，3分鐘來自動化特定的工作流程。這些模板涵蓋了從數據錄入到複雑數據處理的各種常見場景，使用戶能夠快速部署和利用AI技術。建議你可以直接前往[模板中心](https://bika.ai/zh-TW/template)進行使用。',
    },
    {
      q: 'Bika.ai 是否支持團隊協作及權限功能？',
      a: '是的，Bika.ai 提供了“空間站”功能，這是一個團隊協作平台，不僅允許團隊內部成員共享資源、管理數據和協同工作，還能良好地支持與外部團隊或個人的協作。空間站支持個性化設置和權限管理，確保團隊成員可以高效地協作。',
    },
  ],
  ja: [
    {
      q: '一言で説明：Bika.aiとは何ですか？',
      a: 'Bika.aiはAI自動化ツールであり、表形式のデータベースとAI技術を組み合わせて、データ管理とタスク処理を自動化し、作業プロセスの効率を向上させます。',
    },
    {
      q: 'Bika.aiは何がそんなにユニークなのですか？',
      a: getServerDictionary('ja').slogan.usp,
    },
    {
      q: '"BIKA" という略語は何を意味しますか？',
      a: 'B-I-K-Aは「エージェントの王国のボス（Boss In Kingdom of Agents）」または「ビジネスインテリジェンスと知識自動化（Business Intelligence and Knowledge Automation）」の略です。一人会社＋ビジネスインテリジェンス＋知識自動化＋AI。',
    },
    {
      q: 'Bika.aiはどのようにしてAI自動化を実現していますか？',
      a: 'Bika.aiの出発点は「チャットボックス」ではなく「自動化」で、定時や新規データ追加などのトリガー条件を設定してAI自動化を実行します。\n実際に、Bika.aiは「AI自動化ツール」であり、大規模AIモデルに依存せず、自動化機能を使用しているため、AI推論コストは発生しません。Bika.aiのタスク、集約、リソースなどのコアコンポーネントを通じて、多くの作業を効果的に完了させ、時間を節約し、より多くの時間を楽しむことができます。\n直接[テンプレートセンター](https://bika.ai/ja/template)にアクセスして、適切なソリューションを見つけてください。',
    },
    {
      q: 'Bika.aiは無料で使用できますか？',
      a: 'はい、現在Bika.aiは完全に無料で利用できます。非常に寛大な使用量が許可されています。無料スペックを参照してください。\n無料枠を超えた場合や、カスタマイズ機能やプライベートデプロイメントなど、より多くのサービスを希望する場合は、[営業担当者に連絡](?m=eyJuYW1lIjoiQ09OVEFDVF9TRVJWSUNFIn0=)してください。',
    },
    {
      q: 'Bika.aiとChatGPT、GeminiなどのAIアシスタントとの違いは何ですか？',
      a: 'ChatGPT、Gemini、ClaudeなどのAIアシスタントは、大規模なモデルに基づくAIチャットアシスタントで、大量のAI推論コストがかかります。\n一方、Bika.aiはAI自動化、データベースツールで、タスクを自動的に完了します。\nこれらは完全に異なる製品であり、[AI自動化テンプレートセンター](https://bika.ai/ja/template)をチェックして、日常のニーズに合ったテンプレートがあるかどうかを確認してください。',
    },
    {
      q: 'Bika.aiと多次元スプレッドシートの違いは何ですか？',
      a: '市場には多次元スプレッドシートの優れたツールが多数あります、例えば[vika維格雲](https://vika.cn)や[Airtable](https://airtable.com)などです。\n確かにBika.aiと多次元スプレッドシートのコアは似ています：表形式のデータベース、自動化などです。\n多次元スプレッドシートは協力に優れていますが、Bika.aiは「タスク」、「報告」、「AI自動化」を重視し、表ではなく、AIを利用してデータを活用するAI自動化ツールです。',
    },
    {
      q: 'Bika.aiでは、単一のテーブルのデータ量が数万行、数十万行に達し、関連参照が増えた場合、動作が遅くなりますか?',
      a: 'いいえ、遅くなりません。Bika.aiのデータテーブルは、巨大なデータ量を想定して設計されています。基盤ではOLTPビジネスデータベース、OLAPアナリティクスデータベース、NoSQL データベース、時系列データベース、インデックスデータベース、ベクトルデータベースなどの技術を組み合わせて使用しており、1000万件、1億件規模のデータ量でも、プロセスと高パフォーマンスを維持できるよう設計されています。',
    },
    {
      q: 'Bika.aiの「スペースステーション」とは何ですか？',
      a: '「スペースステーション」は、組織またはチームの全メンバーが協力するプラットフォームです。Bika.aiのワークスペース左上にあるスペースアイコンをクリックすることで、パーソナライズされた管理と招待されたスペースにアクセスできます。',
    },
    {
      q: '支払い後、いくつの有料スペースを持っていますか？',
      a: 'Bika.ai はスペースサブスクリプションモデルを採用しています。サブスクリプションを購入すると、1つのスペースが得られます。所有しているスペースの数は、購入したサブスクリプションの数に等しいです。各スペースには、購入条件で規定された特定の使用権が含まれています。',
    },
    {
      q: '「リソース」とは何ですか？',
      a: '「リソース」とは、スペースのリソース部分の左側のディレクトリツリー内のファイルノードを指します。これには、自動化、データベース、フォーム、ダッシュボードなどが含まれます。\n削除されたリソースはカウントに含まれません。新しいデータベースと新しいフォームを作成すると、カウントに2つのファイルノードが追加されます。そのうちの1つを削除すると、カウントが1減少します。',
    },
    {
      q: 'Bika.aiのチームはどのように「自社の製品を自ら使っている」のですか?',
      a: '素晴らしい質問ですね。私たち自身もBika.aiを熱心に使用しています。\n内部では、Bika.aiを以下のように活用しています:毎日の朝会の通知/収集/まとめ、週次Scrumの作業サイクルの通知/収集/まとめ、Twitterの公式アカウントの記事の承認と投稿のスケジュール管理、日次の販売データ分析レポートの生成、機能要求とバグのAI自動化管理、定期的なマーケティングメールの配信など、可能な限り定型的で日常的な作業をAI自動化しています。\nBika.aiチームは3つのSaaSプラットフォーム製品、30以上のデプロイ、百万人のユーザー、数万のチームカスタマーを運営しており、自社の反復作業を削減するためにもっとAI自動化ツールが必要だったので、Bika.aiを開発したのです。\n私たちはお客様にもBika.aiを「使って去る」形で使っていただきたいと考えています。つまり、ソフトウェアに入って初期設定をした後は、大半の作業をAIに任せられるようにしたいのです。',
    },
    {
      q: 'Bika.aiはどのようにして作業効率を向上させますか？',
      a: 'Bika.aiは強力な自動化機能を通じて、ユーザーが日常のタスクを自動的に実行するのを支援します。ユーザーは自分のニーズに応じて自動化タスクを設定できます。データ更新の通知、タスクスケジューリング、自動レポート生成などがこれに含まれます。これにより、手動操作を減らし効率を向上させることができます。適切なソリューションを見つけるために、[テンプレートセンター](https://bika.ai/ja/template)を直接訪問してください。',
    },
    {
      q: 'Bika.aiのAI自動化機能にはどのような特徴がありますか？',
      a: 'Bika.aiのAI自動化機能は、特定の条件を満たした時に自動的にトリガーされる条件ベースのタスクを作成することができます。たとえば、データテーブルが新しいデータを受信すると、Bika.aiはデータの処理と分析を自動的に行い、関連する人々に通知を送ることができます。[テンプレートセンター](https://bika.ai/ja/template)を直接訪問して体験してみてください。',
    },
    {
      q: 'Bika.aiの自動化テンプレートとは何ですか？',
      a: '自動化テンプレートは、事前に設定されたタスクフローです。ユーザーはこれらのテンプレートを直接適用して特定のワークフローを自動化することができます。これらのテンプレートは、データ入力から複雑なデータ処理までの様々な一般的なシナリオをカバーしており、ユーザーが迅速にAI技術を展開して活用するのに役立ちます。[テンプレートセンター](https://bika.ai/ja/template)を直接訪問して使用してみてください。',
    },
    {
      q: 'Bika.aiはチーム協力と権限機能をサポートしていますか？',
      a: 'はい、Bika.aiは「スペースステーション」という機能を提供しています。これは、チーム内のメンバーがリソースを共有し、データを管理し、協力して作業するためのプラットフォームです。また、外部のチームや個人との協力も良好にサポートします。スペースステーションはパーソナライズ設定と権限管理をサポートし、チームメンバーが効率的に協力できるようにします。',
    },
  ],
};
