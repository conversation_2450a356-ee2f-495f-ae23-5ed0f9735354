// 模板首页，模板推荐
import type { TemplateCenterSectionConfig } from '@bika/types/template/vo';

export const TemplateSmartHomeConfig: TemplateCenterSectionConfig[] = [
  {
    layout: 'card',
    name: {
      en: '🔥 Featured',
      'zh-CN': '🔥 强烈推荐',
      'zh-TW': '🔥 強烈推薦',
      ja: '🔥 おすすめ',
    },
    description: {
      en: 'We recommend you to try these templates to save your time',
      'zh-CN': '推荐您试试这些模板，让您节省重复劳动的工作时间，有更多时间享受生活',
      'zh-TW': '推薦您試試這些範本，讓您節省重複勞動的工作時間，有更多時間享受生活',
      ja: '繰り返しの作業にかかる時間を節約し、人生を楽しむ時間を増やすために、これらのテンプレートを試してみることをお勧めします',
    },
    templateIds: [
      // 'marketing-email-series-for-new-users-over-a-period-of-7-days',
      // 'send-emails-in-bulk-tag-triggered',
      'agent-ai-programmer',
      'agent-twitter-manager',
      'agent-github-issue-creator',
      'agent-ai-writer',
      'agent-brand-designer',
      'agent-email-marketer',
      'agent-stock-news-reporter',
      'agent-requirements-document-writer',
      'agent-office-docs-helper',
      'agent-customer-support-scribe',
      'agent-discourse-community-manager',
      'agent-google-analyst',
      'agent-ticket-manager',
      'agent-community-reporter',
    ],
    localeTemplateIdsOverride: {
      en: [
        // 'new-user-welcome-automation',
        // 'x-ai-automated-tweets',
        // 'slack-ai-automated-remind',
        // 'crm-b2b-sales',
        // 'ai-automated-ticket-system',
        // '@vika/scrum-standup',
        // 'imap-email-to-support-ticket',
      ],
      'zh-CN': [
        // 'dingtalk-ai-automated-remind',
        // 'wecom-ai-automated-remind',
        // 'rotating-duty-reminder-wecom',
        // 'ai-automated-ticket-system',
        // '@vika/scrum-standup',
        // 'imap-email-to-support-ticket',
        // 'ai-automated-task-management',
      ],
      'zh-TW': [
        // 'new-user-welcome-automation',
        // 'x-ai-automated-tweets',
        // 'slack-ai-automated-remind',
        // 'dingtalk-ai-automated-remind',
        // 'ai-automated-ticket-system',
        // '@vika/scrum-standup',
        // 'imap-email-to-support-ticket',
      ],
      ja: [
        // 'new-user-welcome-automation',
        // 'x-ai-automated-tweets',
        // 'slack-ai-automated-remind',
        // 'ai-automated-ticket-system',
        // '@vika/scrum-standup',
        // 'imap-email-to-support-ticket',
        // 'ai-automated-task-management',
      ],
    },
  },

  // {
  //   layout: 'card',
  //   name: {
  //     en: 'Send AI Messages',
  //     'zh-CN': '聊天软件通知',
  //     'zh-TW': '聊天軟體通知',
  //     ja: 'チャットソフトの通知',
  //   },
  //   description: {
  //     en: 'AI Automated Send Messages to IM softwares',
  //     'zh-CN': '定时自动发消息到聊天软件',
  //     'zh-TW': '定時自動發消息到聊天軟體',
  //     ja: 'チャットソフトへの定期自動メッセージ送信',
  //   },
  //   templateIds: [
  //     'weekly-meeting-ai-automated-remind-wecom',
  //     'weekly-meeting-ai-automated-remind-slack',
  //     'dingtalk-ai-automated-remind',
  //     'telegram-ai-automated-remind',
  //     'wecom-ai-automated-remind',
  //     'slack-ai-automated-remind',
  //     'invoice-org-ai-automated-remind',
  //     'feishu-ai-automated-remind',
  //     'x-ai-automated-tweets',
  //   ],
  // },
  // {
  //   layout: 'card',
  //   name: {
  //     en: 'Team Management',
  //     'zh-CN': '团队管理',
  //     'zh-TW': '團隊管理',
  //     ja: 'チーム管理',
  //   },
  //   description: {
  //     en: 'Automate team management tasks to improve team efficiency',
  //     'zh-CN': '自动化团队管理任务，提高团队效率',
  //     'zh-TW': '自動化團隊管理任務，提高團隊效率',
  //     ja: 'チームの効率を向上させるために、チーム管理タスクを自動化します',
  //   },
  //   templateIds: [
  //     'diary',
  //     '@vika/scrum-standup',
  //     'weekly-meeting-ai-automated-remind-wecom',
  //     'weekly-meeting-ai-automated-remind-slack',
  //   ],
  // },

  // {
  //   layout: 'card',
  //   name: {
  //     en: 'Scheduler',
  //     'zh-CN': '定时通知系列',
  //     'zh-TW': '定時通知系列',
  //     ja: '定期通知シリーズ',
  //   },
  //   description: {
  //     en: 'Automate scheduled notifications to ensure important information is delivered on time',
  //     'zh-CN': '自动化设置定时通知，确保重要信息按时送达',
  //     'zh-TW': '自動化設置定時通知，確保重要信息按時送達',
  //     ja: '重要な情報が時間通りに届けられるように、定期通知を自動化します',
  //   },
  //   templateIds: [
  //     'weekly-meeting-ai-automated-remind-wecom',
  //     'weekly-meeting-ai-automated-remind-slack',
  //     'dingtalk-ai-automated-remind',
  //     'telegram-ai-automated-remind',
  //     'wecom-ai-automated-remind',
  //     'slack-ai-automated-remind',
  //     'invoice-org-ai-automated-remind',
  //     'feishu-ai-automated-remind',
  //     'email-reminder',
  //     'rotating-duty-reminder-slack',
  //     'automated-get-currency-info-py',
  //     'automated-get-currency-info-js',
  //     'automated-get-stock-info-py',
  //     'automated-get-stock-info-js',
  //   ],
  // },

  // {
  //   layout: 'card',
  //   name: {
  //     en: 'Python & JavaScript',
  //     'zh-CN': 'Python & JavaScript',
  //     'zh-TW': 'Python & JavaScript',
  //     ja: 'Python & JavaScript',
  //   },
  //   description: {
  //     en: 'Python & JavaScript Automtion Templates',
  //     'zh-CN': '学完Python和JavaScript，不知道怎么用？这些AI自动化模板轻松帮您学以致用',
  //     'zh-TW': '學完 Python 和 JavaScript，不知道怎麼用？這些 AI 自動化模板輕鬆幫您學以致用',
  //     ja: 'PythonとJavaScriptを学んだけれど、どう使えばいいかわからない？これらのAI自動化テンプレートがあなたの学習を実践に活かすお手伝いをします。',
  //   },
  //   templateIds: [
  //     'automated-get-currency-info-py',
  //     'automated-get-currency-info-js',
  //     'automated-get-stock-info-py',
  //     'automated-get-stock-info-js',
  //   ],
  // },
];

// 目前，全部模板都按分类放上去

/**
 * 只能内部使用的模板
 */
export const TemplatesInternalOnly: TemplateCenterSectionConfig = {
  // 内部模板系列
  layout: 'card',
  name: {
    en: 'Internal',
    'zh-CN': '内部模板',
    'zh-TW': '内部模板',
    ja: '内部テンプレート',
  },
  description: {
    'zh-CN': '内部未对外公开的模板，本栏目只在开发环境出现',
    'zh-TW': '内部未對外公開的模板，本栏目只在開發環境出現',
    ja: '内部未公開のテンプレート、本栏目は開発環境でのみ表示されます',
    en: 'Internal templates not open to the public, only show in the development environment',
  },
  templateIds: [
    '@vika/crm',
    '@vika/mock',
    '@vika/okr',
    'ai-automated-ticket-system',
    'crm-b2b-sales',
  ],
};
