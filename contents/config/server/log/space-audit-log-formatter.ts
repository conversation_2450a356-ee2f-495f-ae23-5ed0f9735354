import type { SpaceAuditLogData, SpaceAuditLogType } from '@bika/types/log/bo';
import { iStringParse, type Locale } from 'basenext/i18n';
import { getServerLocaleContext } from '../../../i18n/server';

export function DefaultSpaceAuditLogFormatter(data: SpaceAuditLogData, locale: Locale): string {
  const { t } = getServerLocaleContext(locale);
  const template = t(
    `settings.audit.template.${data.type.replace(/\./g, '_')}`,
    {
      ...(data ?? {}),
    },
    locale,
  );
  if (Array.isArray(template)) {
    return template.join('');
  }
  return template;
}
export function DefaultSpaceAuditLogNameFormatter(data: SpaceAuditLogData, locale: Locale): string {
  const { t } = getServerLocaleContext(locale);
  return t(`settings.audit.action_name.${data.type.replace(/\./g, '_')}`);
}

export const SpaceAuditLogFormatters: Record<
  SpaceAuditLogType,
  {
    name: (data: SpaceAuditLogData, locale: Locale) => string;
    description: (data: SpaceAuditLogData, locale: Locale) => string;
  }
> = {
  'invitation.email.accept': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'space.create': {
    // 尽量使用这种方式配置，不要写到diciontary里面，服务端做格式化vo
    name: (_, locale: Locale) =>
      iStringParse(
        {
          en: 'Create Space',
          'zh-CN': '创建空间',
          'zh-TW': '創建空間',
          ja: 'スペースを作成',
        },
        locale,
      ),
    description: (_, locale: Locale) =>
      iStringParse(
        {
          en: 'Create the space',
          'zh-CN': '创建了空间站',
          'zh-TW': '創建了空間站',
          ja: 'がスペースステーションを作成しました',
        },
        locale,
      ),
  },
  'space.update': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'template.install': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.get': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.create': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.update': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.move': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.delete': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.publish': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.detach': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.import': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.export': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'node.upgrade': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.grant': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.revoke': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.upsert': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.restore': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.scope.update': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.password.create': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.password.update': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'share.password.delete': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'invitation.link.create': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'invitation.link.delete': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'invitation.email.send': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'invitation.email.resend': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'invitation.email.delete': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'invitation.link.accept': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
  'space.outgoing-webhook': {
    name: DefaultSpaceAuditLogNameFormatter,
    description: DefaultSpaceAuditLogFormatter,
  },
};
