// Features: Node Resource + Automation + Database + Dashboard + Integration，整合

import assert from 'node:assert';
import { AILanguageModelClientConfig } from '@bika/contents/config/client/ai/ai-model-client-config';
import type { ILocaleContext } from '@bika/contents/i18n';
import {
  type AIIntentType,
  AIIntentTypes,
  type PresetLanguageAIModelDef,
  PresetLanguageAIModelDefs,
} from '@bika/types/ai/bo';
import { type AIIntentUIType, AIIntentUITypes } from '@bika/types/ai/vo';
import {
  type ActionType,
  ActionTypes,
  type TriggerType,
  TriggerTypes,
} from '@bika/types/automation/bo';
import { type WidgetType, WidgetTypes } from '@bika/types/dashboard/bo';
import {
  type DatabaseFieldType,
  DatabaseFieldTypes,
  DatabaseViewTypes,
  type FormulaType,
  FormulaTypes,
  type ViewType,
} from '@bika/types/database/bo';
import { type IntegrationType, IntegrationTypes } from '@bika/types/integration/bo';
import { type MissionType, MissionTypes } from '@bika/types/mission/bo';
import { type NodeResourceType, NodeResourceTypes } from '@bika/types/node/bo';
import {
  type FeatureType,
  type IFeatureAbilityConfigDetail,
  type SpaceFeatureType,
  SpaceFeatureTypes,
} from '@bika/types/website/bo';
import { type iString, iStringParse } from 'basenext/i18n';
import { getAIIntentUITypesConfig } from '../ai/intent-ui';
import { getAIIntentTypesConfig } from '../ai/wizard';
import { getActionTypesConfig } from '../automation/actions';
import { getTriggerTypesConfig } from '../automation/triggers';
import { getWidgetTypesConfig } from '../dashboard/widgets';
import { getFieldTypesConfig } from '../database/fields';
import { getFormulaTypesConfig } from '../database/formulas';
import { getDatabaseViewTypesConfig } from '../database/views';
import { getIntegrationTypesConfig } from '../integration/integrations';
import { getMissionsTypesConfig } from '../mission/missions';
import { getResourcesTypesConfig } from '../node/node-resources';
import { getSpaceFeatureTypesConfig } from '../space/space-features';

/**
 * 获取一个分类下所有的sub feature名称和description
 */
export function getFeatureInfo(
  featureCategory: FeatureType,
  localeContext: ILocaleContext,
): {
  abilities: readonly string[];
  label: string; // 列表名字
  description: string;
  featureType: string;
} {
  const { t } = localeContext;
  switch (featureCategory) {
    case 'ai-model': {
      return {
        abilities: PresetLanguageAIModelDefs,
        label: t.ai.ai_models_features_list,
        featureType: t.ai.ai_models_feature,
        description: t.ai.ai_models_feature_description,
      };
    }
    case 'node-resource': {
      return {
        abilities: NodeResourceTypes,
        description: t.resource.description,
        featureType: t.resource.resource,
        label: t.resource.features_list,
      };
    }
    case 'automation-trigger': {
      return {
        abilities: TriggerTypes,
        description: t.resource.type.automation_trigger_description,
        featureType: `${t.automation.automation} - ${t.automation.trigger.triggers}`,
        label: t.resource.type.automation_trigger_features_list,
      };
    }
    case 'automation-action': {
      return {
        abilities: ActionTypes,
        description: t.resource.type.automation_action_description,
        featureType: `${t.automation.automation} - ${t.automation.action.actions}`,
        label: t.resource.type.automation_action_features_list,
      };
    }
    case 'database-field': {
      return {
        abilities: DatabaseFieldTypes,
        description: t.resource.type.database_field_description,
        featureType: `${t.resource.type.database} - ${t.resource.fields}`,
        label: t.resource.type.database_field_features_list,
      };
    }
    case 'database-view': {
      return {
        abilities: DatabaseViewTypes,
        description: t.resource.type.database_view_description,
        featureType: `${t.resource.type.database} - ${t.resource.type.view}`,
        label: t.resource.type.database_view_features_list,
      };
    }
    case 'dashboard-widget': {
      return {
        abilities: WidgetTypes,
        description: t.resource.widgets.description,
        featureType: `${t.resource.type.dashboard} - ${t.resource.widgets.name}`,
        label: t.resource.widgets.features_list,
      };
    }
    case 'integration': {
      return {
        abilities: IntegrationTypes,
        description: t.resource.type.integration_description,
        featureType: t.integration.banner_title,
        label: t.integration.features_list,
      };
    }
    case 'ai-intent-ui': {
      return {
        abilities: AIIntentUITypes,
        description: t.resource.type.ai_wizard_intent_ui_description,
        featureType: 'AI Intent UI',
        label: t.resource.type.ai_wizard_intent_ui_features_list,
      };
    }
    case 'ai-wizard': {
      return {
        abilities: AIIntentTypes,
        description: t.resource.type.ai_wizard_description,
        featureType: 'AI Wizard',
        label: t.resource.type.ai_wizard_features_list,
      };
    }
    case 'mission': {
      return {
        abilities: MissionTypes,
        featureType: t.mission.mission,
        description: t.mission.mission_description,
        label: t.mission.features_list,
      };
    }
    case 'formula': {
      return {
        abilities: FormulaTypes,
        description: t.database_fields.formula.description,
        featureType: t.database_fields.formula.name,
        label: t.formula.features_list,
      };
    }
    case 'space': {
      return {
        abilities: SpaceFeatureTypes,
        description: t.settings.space.description,
        featureType: t.space.space,
        label: t.space.features_list,
      };
    }

    default:
      throw new Error(`Not found: ${featureCategory}`);
  }
}
/**
 *
 * 根据feature配置，单一某个feature的基础info，name，description，icon
 *
 * @param localeContext
 * @param featureCategory
 * @param abilityKey
 * @returns
 */
export function getFeatureAbilityConfig(
  localeContext: ILocaleContext,
  featureCategory: FeatureType,
  abilityKey: string,
): IFeatureAbilityConfigDetail {
  let iconPath: string;
  let label: string;
  let description: string;
  let display: 'SHOW' | 'HIDDEN' | 'COMING_SOON';
  const links: { url: string; text?: iString }[] = [];
  switch (featureCategory) {
    case 'ai-model': {
      const config = AILanguageModelClientConfig[abilityKey as PresetLanguageAIModelDef];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.logo?.type === 'URL' ? config.logo.url : '';
      label = iStringParse(config.name, localeContext.lang);
      description = iStringParse(config.description, localeContext.lang);
      display = config.display || 'SHOW';
      break;
    }
    case 'node-resource': {
      const configs = getResourcesTypesConfig(localeContext);
      const config = configs[abilityKey as NodeResourceType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'automation-trigger': {
      const configs = getTriggerTypesConfig(localeContext);
      const config = configs[abilityKey as TriggerType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'automation-action': {
      const configs = getActionTypesConfig(localeContext);
      const config = configs[abilityKey as ActionType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'database-view': {
      const configs = getDatabaseViewTypesConfig(localeContext);
      const config = configs[abilityKey as ViewType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'database-field': {
      const configs = getFieldTypesConfig(localeContext);
      const config = configs[abilityKey as DatabaseFieldType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'dashboard-widget': {
      const configs = getWidgetTypesConfig(localeContext);
      const config = configs[abilityKey as WidgetType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'integration': {
      const configs = getIntegrationTypesConfig(localeContext);
      const config = configs[abilityKey as IntegrationType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.displayConfig.icon;
      label = config.displayConfig.label;
      description = config.displayConfig.description;
      display = config.displayConfig.display;
      if (config.displayConfig.websiteUrl) {
        links.push({
          url: config.displayConfig.websiteUrl,
          text: iStringParse(
            {
              en: `${label} Official Website`,
              'zh-CN': `${label}的官方网站`,
              'zh-TW': `${label}的官方網站`,
              ja: `${label}の公式ウェブサイト`,
            },
            localeContext.lang,
          ),
        });
      }
      if (config.displayConfig.links) {
        links.push(...config.displayConfig.links);
      }
      break;
    }
    case 'mission': {
      const configs = getMissionsTypesConfig(localeContext);
      const config = configs[abilityKey as MissionType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'formula': {
      const configs = getFormulaTypesConfig(localeContext);
      const config = configs[abilityKey as FormulaType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.summary;
      display = config.display;
      break;
    }
    case 'ai-intent-ui': {
      const configs = getAIIntentUITypesConfig(localeContext);
      const intentUIType = abilityKey as AIIntentUIType;
      const config = configs[intentUIType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'ai-wizard': {
      const configs = getAIIntentTypesConfig(localeContext);
      const config = configs[abilityKey as AIIntentType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    case 'space': {
      const configs = getSpaceFeatureTypesConfig(localeContext);
      const config = configs[abilityKey as SpaceFeatureType];
      assert(config, `Not found: ${abilityKey}`);
      iconPath = config.iconPath;
      label = config.label;
      description = config.description;
      display = config.display;
      break;
    }
    default:
      throw new Error(`Not found: ${featureCategory}`);
  }

  return {
    featureType: featureCategory,
    key: abilityKey,
    iconPath,
    label,
    display,
    description,
    links,
  };
}

/**
 *  所有的 features abilities 列表
 *
 * @param featureType
 * @param localeContext
 * @returns
 */
export function getFeatureAbilitiesList(
  featureType: FeatureType,
  localeContext: ILocaleContext,
): IFeatureAbilityConfigDetail[] {
  const locale = localeContext.lang;
  switch (featureType) {
    case 'ai-model': {
      const aiModelsCfgs: IFeatureAbilityConfigDetail[] = Object.entries(
        AILanguageModelClientConfig,
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          key,
          featureType: 'ai-model' as FeatureType,
          display: value.display || 'SHOW',
          iconPath: value.logo?.type === 'URL' ? value.logo.url : '',
          label: iStringParse(value.name, locale),
          description: iStringParse(value.description, locale),
        }));
      return aiModelsCfgs;
    }
    case 'node-resource': {
      const nodeResources: IFeatureAbilityConfigDetail[] = Object.entries(
        // getFeatureConfig(localeContext, )
        getResourcesTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'node-resource' as FeatureType,
          key,
          display: value.display,
          iconPath: value.iconPath,
          label: value.label,
          locale,
          description: value.description,
        }));
      return nodeResources;
    }
    case 'automation-trigger': {
      const triggers: IFeatureAbilityConfigDetail[] = Object.entries(
        getTriggerTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          key,
          featureType: 'automation-trigger' as FeatureType,
          display: value.display,
          iconPath: value.iconPath,
          label: value.label,
          description: value.description,
        }));
      return triggers;
    }
    case 'automation-action': {
      const actions: IFeatureAbilityConfigDetail[] = Object.entries(
        getActionTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          key,
          featureType: 'automation-action' as FeatureType,
          display: value.display,
          iconPath: value.iconPath,
          label: value.label,
          description: value.description,
        }));
      return actions;
    }
    case 'database-field': {
      const fields: IFeatureAbilityConfigDetail[] = Object.entries(
        getFieldTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          key,
          featureType: 'database-field' as FeatureType,
          display: value.display,
          iconPath: value.iconPath,
          label: value.label,
          description: value.description,
        }));
      return fields;
    }
    case 'database-view': {
      const fields: IFeatureAbilityConfigDetail[] = Object.entries(
        getDatabaseViewTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'database-view' as FeatureType,
          key,
          display: value.display,
          iconPath: value.iconPath,
          label: value.label,
          description: value.description,
        }));
      return fields;
    }
    case 'dashboard-widget': {
      const widgets: IFeatureAbilityConfigDetail[] = Object.entries(
        getWidgetTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'dashboard-widget' as FeatureType,
          key,
          iconPath: value.iconPath,
          display: value.display,
          label: value.label,
          description: value.description,
        }));
      return widgets;
    }
    case 'integration': {
      const integrations: IFeatureAbilityConfigDetail[] = Object.entries(
        getIntegrationTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.displayConfig.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'integration' as FeatureType,
          key,
          iconPath: value.displayConfig.icon,
          label: value.displayConfig.label,
          display: value.displayConfig.display,
          description: value.displayConfig.description,
        }));
      return integrations;
    }
    case 'mission': {
      const missions: IFeatureAbilityConfigDetail[] = Object.entries(
        getMissionsTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'mission' as FeatureType,
          key,
          iconPath: value.iconPath,
          label: value.label,
          display: value.display,
          description: value.description,
        }));
      return missions;
    }
    case 'formula': {
      const missions: IFeatureAbilityConfigDetail[] = Object.entries(
        getFormulaTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'formula' as FeatureType,
          key,
          iconPath: value.iconPath,
          label: value.label,
          display: value.display,
          description: value.summary,
        }));
      return missions;
    }
    case 'ai-wizard': {
      const missions: IFeatureAbilityConfigDetail[] = Object.entries(
        getAIIntentTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'ai-wizard' as FeatureType,
          key,
          iconPath: value.iconPath,
          label: value.label,
          display: value.display,
          description: value.description,
        }));
      return missions;
    }
    case 'space': {
      const missions: IFeatureAbilityConfigDetail[] = Object.entries(
        getSpaceFeatureTypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'space' as FeatureType,
          key,
          iconPath: value.iconPath,
          label: value.label,
          display: value.display,
          description: value.description,
        }));
      return missions;
    }
    case 'ai-intent-ui': {
      const intents: IFeatureAbilityConfigDetail[] = Object.entries(
        getAIIntentUITypesConfig(localeContext),
      )
        .filter(([_, value]) => value.display !== 'HIDDEN')
        .map(([key, value]) => ({
          featureType: 'ai-intent-ui' as FeatureType,
          key,
          iconPath: value.iconPath,
          label: value.label,
          display: value.display,
          description: value.description,
        }));
      return intents;
    }
    default: {
      console.error(`FeatureType ${featureType} not supported`);
      return [];
    }
  }
}
