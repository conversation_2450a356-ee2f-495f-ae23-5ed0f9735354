import type { ILocaleContext } from '@bika/contents/i18n/context';
import {
  ButtonClickTriggerSchema,
  DatetimeFieldReachedTriggerSchema,
  DummyTriggerSchema,
  FormSubmittedTriggerSchema,
  HttpIfChangeTriggerSchema,
  InboundEmailTriggerSchema,
  ManuallyTriggerSchema,
  MemberJoinedTriggerSchema,
  RecordCreatedTriggerSchema,
  RecordMatchTriggerSchema,
  SchedulerTriggerSchema,
  type Trigger,
  type TriggerType,
} from '@bika/types/automation/bo';
import type { IntegrationType } from '@bika/types/integration/bo';
import type { IFeatureAbilityConfig } from '@bika/types/website/bo';

export function getTriggerTypesConfig(locale: ILocaleContext): Record<
  TriggerType,
  IFeatureAbilityConfig & {
    integrations?: IntegrationType[];
    defaultValue: Trigger;
  }
> {
  const { t } = locale;
  return {
    HTTP_IF_CHANGE: {
      display: 'SHOW',
      label: t.automation.trigger.http_if_change.name,
      iconPath: '/assets/icons/automation/http-if-change.png',
      description: t.automation.trigger.http_if_change.description,
      defaultValue: HttpIfChangeTriggerSchema.parse({
        triggerType: 'HTTP_IF_CHANGE',
        input: {
          type: 'HTTP_IF_CHANGE',
          scheduler: {
            repeat: {
              every: {
                type: 'DAY',
                interval: 1,
              },
            },
            datetime: new Date().toISOString(),
          },
          url: '',
          method: 'GET',
          headers: [],
          policy: {
            type: 'FIRST_FAILED',
          },
        },
      }),
    },
    SCHEDULER: {
      display: 'SHOW',
      label: t.automation.trigger.scheduler.name,
      iconPath: '/assets/icons/automation/scheduled.png',
      description: t.automation.trigger.scheduler.description,
      defaultValue: SchedulerTriggerSchema.parse({
        triggerType: 'SCHEDULER',
        input: {
          type: 'SCHEDULER',
          scheduler: {
            repeat: {
              every: {
                type: 'DAY',
                interval: 1,
              },
            },
            datetime: new Date().toISOString(),
          },
        },
      }),
    },
    WEBHOOK_RECEIVED: {
      display: 'SHOW',
      label: t.automation.trigger.webhook_received.name,
      iconPath: '/assets/icons/automation/webhook.png',
      description: t.automation.trigger.webhook_received.description,
      defaultValue: {
        triggerType: 'WEBHOOK_RECEIVED',
        input: {
          type: 'WEBHOOK_RECEIVED',
        },
      },
    },
    RECORD_CREATED: {
      display: 'SHOW',
      label: t.automation.trigger.record_created.name,
      iconPath: '/assets/icons/automation/new_record.png',
      description: t.automation.trigger.record_created.description,
      defaultValue: RecordCreatedTriggerSchema.parse({
        triggerType: 'RECORD_CREATED',
        input: {
          type: 'DATABASE',
          databaseId: '',
        },
      }),
    },
    RECORD_MATCH: {
      display: 'SHOW',
      label: t.automation.trigger.record_match.name,
      iconPath: '/assets/icons/automation/record_match.png',
      description: t.automation.trigger.record_match.description,
      defaultValue: RecordMatchTriggerSchema.parse({
        triggerType: 'RECORD_MATCH',
        input: {
          type: 'DATABASE_WITH_FILTER',
          databaseId: '',
          filters: {
            conjunction: 'And',
            conds: [],
            conditions: [],
          },
        },
      }),
    },
    MANUALLY: {
      display: 'SHOW',
      label: t.automation.trigger.manually.name,
      iconPath: '/assets/icons/automation/manually.png',
      description: t.automation.trigger.manually.description,
      defaultValue: ManuallyTriggerSchema.parse({
        triggerType: 'MANUALLY',
      }),
    },
    DATETIME_FIELD_REACHED: {
      display: 'SHOW',
      label: t.automation.trigger.datetime_field_reached.name,
      iconPath: '/assets/icons/automation/datetime-field-reminder.png',
      description: t.automation.trigger.datetime_field_reached.description,
      defaultValue: DatetimeFieldReachedTriggerSchema.parse({
        triggerType: 'DATETIME_FIELD_REACHED',
        input: {
          type: 'DATETIME_FIELD_REACHED',
          databaseId: '',
          fieldId: '',
          datetime: {
            type: 'TODAY',
            hour: 9,
            minute: 0,
          },
        },
      }),
    },
    FORM_SUBMITTED: {
      display: 'SHOW',
      label: t.automation.trigger.form_submitted.name,
      iconPath: '/assets/icons/automation/form-submit.png',
      description: t.automation.trigger.form_submitted.description,
      defaultValue: FormSubmittedTriggerSchema.parse({
        triggerType: 'FORM_SUBMITTED',
        input: {
          type: 'FORM',
          formId: '',
        },
      }),
    },
    INBOUND_EMAIL: {
      display: 'SHOW',
      label: t.automation.trigger.inbound_email.name,
      iconPath: '/assets/icons/automation/email_received.png',
      description: t.automation.trigger.inbound_email.description,
      defaultValue: InboundEmailTriggerSchema.parse({
        triggerType: 'INBOUND_EMAIL',
        input: {
          type: 'INBOUND_EMAIL',
          integrationId: '',
        },
      }),
    },
    MEMBER_JOINED: {
      display: 'COMING_SOON',
      label: t.automation.trigger.member_joined.name,
      iconPath: '/assets/icons/automation/member-joined.png',
      description: t.automation.trigger.member_joined.description,
      defaultValue: MemberJoinedTriggerSchema.parse({
        triggerType: 'MEMBER_JOINED',
      }),
    },
    DUMMY_TRIGGER: {
      display: 'HIDDEN',
      label: t.automation.trigger.dummy_trigger.name,
      iconPath: '/assets/icons/automation/bika.png',
      description: t.automation.trigger.dummy_trigger.description,
      defaultValue: DummyTriggerSchema.parse({
        triggerType: 'DUMMY_TRIGGER',
      }),
    },
    BUTTON_CLICK: {
      display: 'COMING_SOON',
      label: t.automation.trigger.button_clicked.name,
      iconPath: '/assets/icons/automation/button_click.png',
      description: t.automation.trigger.button_clicked.description,
      defaultValue: ButtonClickTriggerSchema.parse({
        triggerType: 'BUTTON_CLICK',
      }),
    },
  };
}
