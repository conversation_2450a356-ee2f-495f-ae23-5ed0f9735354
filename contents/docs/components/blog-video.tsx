// server component

import type { HelpVideoType } from '@bika/types/website/bo';
import type { Locale } from 'basenext/i18n';
import { getServerLocaleContext } from '../../i18n/server';
import { HelpVideo } from './help-video';

/**
 * 文档专用，读取了服务端locale
 * @param param0
 * @returns
 */
export function BlogVideo({
  videoType,
  locale,
}: {
  // 营销视频、新手快速操作视频、产品深入视频、一分钟视频（表单、自动化、文档、仪表盘、数据库、AI Agent）、demo视频
  videoType: HelpVideoType;
  locale: Locale;
}) {
  const localeContext = getServerLocaleContext(locale);
  return <HelpVideo videoType={videoType} locale={localeContext} />;
}
