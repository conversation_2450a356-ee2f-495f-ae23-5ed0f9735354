import type { HelpVideoType } from '@bika/types/website/bo';
import type { ILocaleContext } from '../../i18n';
import { ArcadeOnboardingVideo } from './arcade-video';
import { IFrameVideo } from './iframe-video';

interface Props {
  videoType: HelpVideoType;
  locale: ILocaleContext;
  onLoad?: () => void;
}

export function HelpVideo(props: Props) {
  const { videoType, locale, onLoad } = props;
  const { t } = locale;
  let embedVideoUrl: string;

  switch (videoType) {
    case 'INTRODUCTION':
      embedVideoUrl = t.slogan.video_introduction;
      break;
    case 'ONBOARDING':
      // @deprecated,  Arcade iframe replaced
      embedVideoUrl = t.slogan.video_onboarding;
      break;
    case 'PARTNERS':
      embedVideoUrl = t.slogan.video_partners;
      break;
    case 'FORMS': // 新增：表单视频
      embedVideoUrl = t.slogan.video_forms;
      break;
    case 'AUTOMATION': // 新增：自动化视频
      embedVideoUrl = t.slogan.video_automation;
      break;
    case 'DOCUMENTS': // 新增：文档视频
      embedVideoUrl = t.slogan.video_documents;
      break;
    case 'DASHBOARD': // 新增：仪表盘视频
      embedVideoUrl = t.slogan.video_dashboard;
      break;
    case 'DATABASE': // 新增：数据库视频
      embedVideoUrl = t.slogan.video_database;
      break;
    case 'AI_AGENT': // 新增：AI Agent 视频
      embedVideoUrl = t.slogan.video_ai_agent;
      break;
    case 'PRESENTATION':
      embedVideoUrl = t.slogan.video_product;
      break;
    default:
      embedVideoUrl = t.slogan.video_introduction;
      break;
  }

  return (
    <>
      {/* <!--ARCADE EMBED START--> */}
      {/* // <!--ARCADE EMBED END--> */}
      {videoType === 'ONBOARDING' ? (
        <ArcadeOnboardingVideo />
      ) : (
        <IFrameVideo onLoad={onLoad} url={embedVideoUrl} title={videoType} />
      )}
      <br />
    </>
  );
}
