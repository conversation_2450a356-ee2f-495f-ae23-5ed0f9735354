// server component

import React from 'react';

/**
 * 任意视频
 *
 * @param param0
 * @returns
 */
export function IFrameVideo({
  url,
  title,
  onLoad,
}: {
  url: string;
  title?: string;
  onLoad?: () => void;
}) {
  return (
    <>
      <div
        style={{
          position: 'relative',
          paddingBottom: '56.25%',
          height: 0,
          overflow: 'hidden',
        }}
      >
        <iframe
          onLoad={onLoad}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
          src={url}
          title={`Bika.ai video: ${title}`}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        ></iframe>
      </div>
      <br />
    </>
  );
}
