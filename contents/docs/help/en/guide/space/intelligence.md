---
title: AI Intelligence
slug: help/en/guide/space/intelligence
sidebar_position: 70
sidebar_label: AI Intelligence
---


# AI Intelligence

# "Intelligence" Sidebar

 The "Intelligence" feature module in Bika.ai is one of the core highlights of the platform, aiming to provide a more efficient and intelligent working experience through AI and automation. Below is a detailed introduction to the "Intelligence" features:

## Intelligent Search

#### Function Introduction

The Intelligent Search function helps you quickly find the information you need, including folders, database, automations, forms, missions, reports, and more. It also matches and recommends content through multi-dimensional matching, allowing you to quickly locate the desired information.

#### Usage Steps

1. In the Bika.ai interface, click the search icon at the top left to open the Intelligent Search function.

<img src="https://book.bika.ai/bika-content/assets/ESm4buJOvoKKvsxZmPOl68edgfd.png" src-width="3144" src-height="1790" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/PEOWbaBIDoLSGUxpFK5lZSDigHg.png" src-width="3144" src-height="1796" align="center"/>

1. After entering keywords, Bika.ai will automatically match relevant content and display it in the search results, ensuring you can quickly find the information you need.

<img src="https://book.bika.ai/bika-content/assets/X5aybZdqgoGaNAx3WqLlnP5Ngad.png" src-width="2600" src-height="1726" align="center"/>

1. Click on the list item in the search results to directly access the corresponding content, making it convenient and fast.

<img src="https://book.bika.ai/bika-content/assets/Urb7bnXYxosMH2xcXeql75zkgEg.png" src-width="2686" src-height="1886" align="center"/>

## Intelligent Reports

#### Function Introduction

The Intelligent Report function can generate report materials based on set rules or data, helping you quickly understand data trends and insights.

#### Creation Methods

##### Through Automation Workflow

##### Through the "Add" Button in the Top Right Corner

## Intelligent Missions

#### Function Introduction

The Intelligent Mission function helps you automatically manage and track missions, improve team collaboration efficiency, and ensure ,issions are completed on time.

#### Creation Methods

##### Through Automation Workflow

##### Through the "Add" Button in the Top Right Corner

# AI Capabilities

In addition to the familiar Intelligent Search, Intelligent Reports, and Intelligent Missions in the Intelligence sidebar, Bika.ai also has many powerful AI capabilities. In this section, we will list some of the AI-related capabilities in Bika.ai. Through these functions, you can further explore and utilize these capabilities to unlock more business scenarios and create greater value for your enterprise.

## AI Generated Agent Template

<img src="https://book.bika.ai/bika-content/assets/N6qAb0f2Co2u2Sx1a8alXENqgmd.png" src-width="2386" src-height="826" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/NV19bHcMio8zuixxKhhl7JVrgrd.png" src-width="2352" src-height="1540" align="center"/>

You only need to click the center of the interface template and select "Use AI to Generate Agent" to enter a new interface.

Here, you only need to input your requirements, and the system will think and generate an agent template. At the same time, it will also display the thinking process, reference other templates, and output a complete architecture diagram.

After you click confirm, you can deploy and use this enterprise AI agent with one click.

We also plan to launch a "DeepThink" deep thinking function, enter the deep reasoning mode, and gradually deduce the complete agent architecture and template.

## AI Column Expansion

> This feature is under development.

After entering the database, click the option in the top-right corner.

If you have management rights for the database, you will see the "AI Column Expansion" option.

After clicking, an AI chat window will pop up. You just need to input your requirements and let the AI help you add new columns to the table to meet your business needs.

## AI Write

The AI Writer function allows you to quickly pass various types of information to the AI for processing, such as extracting key information or generating structured records. To achieve better results, please provide detailed and well-organized content. Currently, this function only supports text and numeric fields. The AI can not only analyze and gain insights from this information but also generate Mock Record based on your structure.

<img src="https://book.bika.ai/bika-content/assets/DqqpbXJyTo6G7bxXpmtlJLREg9g.png" src-width="2682" src-height="1798" align="center"/>

### AI-Assisted Configuration for Database Fields

> Coming Soon, this feature is not publicly available yet.

You can configure an assistant prompt for single or multi-text fields. When users are setting up text cells, they can summon the AI Writer, which will rewrite and polish the text based on your prompts.

<img src="https://book.bika.ai/bika-content/assets/HfPTbJJJRoO5DExR8V9lTGZDgAc.png" src-width="3354" src-height="1906" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/AnNDbynenoKHf7xd9z1lI3lKgOd.png" src-width="3164" src-height="1802" align="center"/>

### Performing AI Writer in Record Details

1. Enter a database, open the record details pop-up window, and initiate the AI Writer operation.
    <img src="https://book.bika.ai/bika-content/assets/Z09NbloCyonDqVxWvlAllXfHg1g.png" src-width="2682" src-height="1800" align="center"/>

2. Click "Edit Record," and find the "AI Writer" button in the top right corner of the pop-up window.
    <img src="https://book.bika.ai/bika-content/assets/BvJkbsUBDor7aMxEOBslKcJagVg.png" src-width="2682" src-height="1802" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/Y0xmbbHLMot77oxlWgXlDbE5gIh.png" src-width="2682" src-height="1802" align="center"/>

3. Follow the prompts to accurately input the text, data, or other content that needs processing. Additionally, you can switch the option in the top left corner to "Generate Mock Record" to let the AI generate Mock Record for you.
    <img src="https://book.bika.ai/bika-content/assets/J2TCbB3CSobbKTxsltAlzhBogeh.png" src-width="2682" src-height="1804" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/QxB9bT3A4oHxvDxfycolrpmQgsm.png" src-width="2682" src-height="1806" align="center"/>

4. Click the "AI Generate" button and wait a moment to obtain the results generated by the AI based on the database field titles. If you are not satisfied with the results, you can click "Generate Again."
    <img src="https://book.bika.ai/bika-content/assets/JaDGbmKfEoPU9Nx7WAKllMYPghb.png" src-width="2682" src-height="1800" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/HDiib42Avo8bafxf18qltqXygzC.png" src-width="2682" src-height="1800" align="center"/>

5. Once the generated results meet your requirements, click "Insert Content" to automatically write the content into the record details. Finally, click the "Save" button at the bottom to complete the operation.
    <img src="https://book.bika.ai/bika-content/assets/X9WQbYauToKQD2xgrl9lvuARgJe.png" src-width="2682" src-height="1806" align="center"/>

### Performing AI Writer in Node Descriptions

1. Click the "More" button, then click the "Edit" button to enter the node editing interface.
    <img src="https://book.bika.ai/bika-content/assets/NFwNbWrUwoxYJAx32mWlWR9lgVd.png" src-width="2682" src-height="1804" align="center"/>

2. Find and click the "AI Writer" button below the description.
    <img src="https://book.bika.ai/bika-content/assets/SpMLbXLC7okMGvxL4O4lJpougFg.png" src-width="2682" src-height="1802" align="center"/>

3. Enter supplementary information about the resource description as prompted. At this time, the AI will generate a resource description based on the context (if it's a folder, it will read the files under the folder; if it's an automation, it will read the trigger and action information).
    <img src="https://book.bika.ai/bika-content/assets/SA4Fbsos5oenNHxLurklIV7lgcg.png" src-width="2682" src-height="1802" align="center"/>

4. Click the "AI Generate" button and wait a moment to get the AI-generated result. If the result does not meet your expectations, you can click "Regenerate."
    <img src="https://book.bika.ai/bika-content/assets/BFY8buueOoyhATxFcukldOiig5g.png" src-width="2682" src-height="1806" align="center"/>

5. Once the generated result meets your requirements, click "Insert Content" to automatically write the content into the record details. Finally, click the "Save" button at the bottom to complete the operation.
    <img src="https://book.bika.ai/bika-content/assets/CoYrbAVS5ocjXdxAluwlDqC5ghf.png" src-width="2682" src-height="1802" align="center"/>

### More AI Write

- Rephrase
    > Coming Soon, this feature is not publicly available yet.
    <img src="https://book.bika.ai/bika-content/assets/YmLrbU8U5ocAt5xFdQPlKc26ghh.png" src-width="2846" src-height="1634" align="center"/>

- Multi-language strings
    > Coming Soon, this feature is not publicly available yet.
    <img src="https://book.bika.ai/bika-content/assets/VWOCb4oOLou8bjxt8rzlAZ0igNe.png" src-width="2560" src-height="1446" align="center"/>

- Translation
    > Coming Soon, this feature is not publicly available yet.
    <img src="https://book.bika.ai/bika-content/assets/QXPpbCzBRozBezxTkjalMiPCg0e.png" src-width="2778" src-height="1568" align="center"/>

- Document AI Assistant
    > Coming Soon, this feature is not publicly available yet.
    <img src="https://book.bika.ai/bika-content/assets/U3cvbcpyYoh2CAxAT1HlxR5Bguc.png" src-width="2910" src-height="1630" align="center"/>

- Document AI Graphics Assistant
    > Coming Soon, this feature is not publicly available yet.
    <img src="https://book.bika.ai/bika-content/assets/CB3CbAaTzoTUzKxsVi9lRyP6gBb.png" src-width="2910" src-height="1630" align="center"/>

## AI Field Column

> Coming Soon, this feature is not publicly available yet.

In database, there is a special column type - AI Column. Users can choose to create this column to call AI and generate diverse tables. They can also flexibly select different AI models, such as the well-known OpenAI GPT model, Anthropic Claude model, Google Gemini, and DeepSeek, to meet various usage requirements.

### AI Text

> Coming Soon, this feature is not publicly available yet.

Take the work scene of a tester as an example. After the company's product is successfully launched, a large number of external users feedback problems encountered during use, causing testers to receive a mountain of complaint emails every day. Facing a large number of emails, if the traditional method of manually replying one by one is used, it will not only consume a lot of time and energy but also be extremely inefficient. By leveraging Bika's powerful AI text feature, testers can simply enter relevant prompts accurately to quickly generate a series of friendly and professional reply scripts, greatly improving work efficiency.

At the same time, if your reply content involves specific data, be sure to accurately fill in the relevant column data you wish to reference in the corresponding area. These data will serve as the key information support for the AI-generated content. After completing the above operations, the Bika platform will automatically generate the required text content for you.

In addition, it has a strong real-time update function. Just click the "Auto Update" button, and the platform can synchronize the latest data in real-time, achieving dynamic content updates, which greatly improves work efficiency and data timeliness, allowing you to always obtain the latest and most accurate information.

<b>How to Use</b>

1. In the relevant operation interface, create a field of AI text type.
    <img src="https://book.bika.ai/bika-content/assets/ZcI6bbxBBoePSaxhdWAljByxgLg.png" src-width="2324" src-height="1318" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/LB6Jb9h4dokpFzxfth8l6Tr0gXc.png" src-width="2362" src-height="1348" align="center"/>

2. From the provided model list, such as OpenAI GPT, Anthropic Claude, Google Gemini, or DeepSeek, select an AI model that suits your needs.
    <img src="https://book.bika.ai/bika-content/assets/H8Vsbsm1XoY6oVxnKvKlu7Jygvf.png" src-width="2354" src-height="1332" align="center"/>

3. In the prompt input area, enter clear prompts according to specific scenarios. You can enter "Based on the 'complaint issue' field, write a reply script of about 50 words in a friendly tone, using the 'submitter' field to address the other party".
    <img src="https://book.bika.ai/bika-content/assets/RsC9bpiQFoVvRTxoyMtlMxoWg1g.png" src-width="2350" src-height="1350" align="center"/>

4. Click the "Preview Result" button to view the content preview generated by AI based on the first record's data, so as to understand the generation effect in advance.
    <img src="https://book.bika.ai/bika-content/assets/W2HFbSEqToFlEexFTP6l2SBegOb.png" src-width="2548" src-height="1458" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/Khq2bazCbo66B2xULlPlCeAHgIb.png" src-width="2426" src-height="1386" align="center"/>

5. After enabling the "Auto Update" function in the field settings, the AI-generated text field will synchronize the latest data in real-time, achieving dynamic content updates. This process significantly improves work efficiency and ensures data timeliness. Once this function is turned on, the relevant cells in the database will automatically update according to the changes in the inserted content.
    <img src="https://book.bika.ai/bika-content/assets/I0Z7bhsk8oWmAUxc8HHlmB8ygBB.png" src-width="2218" src-height="1260" align="center"/>

6. After confirming that the prompt is correct, save the AI text field.
7. Back to the table, hover over the corresponding cell where you want to generate content, and click the "AI Generate" button. Wait a moment, and AI will start running and generating the corresponding content.
    <img src="https://book.bika.ai/bika-content/assets/KNzPbplk9oSi9Pxl0pQlD0NLgzR.png" src-width="2280" src-height="1292" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/Zpx1bxiNXoebiXxReRpl5kuZgEd.png" src-width="2608" src-height="1480" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/TFDIbxbREoAPzHxZHG9lcWQngzg.png" src-width="2636" src-height="1486" align="center"/>

8. Secondary editing and fine-tuning: If the generated content does not fully meet your expectations, you can double-click the cell to fine-tune the generated content until it best meets your needs.
    <img src="https://book.bika.ai/bika-content/assets/JSKfbkKzmoYcjNxq7tPlzoZpgFe.png" src-width="2628" src-height="1490" align="center"/>

### AI Image Generation

> Coming Soon, this feature is not publicly available yet.

After selecting the AI image generation function in the AI data column, you also need to input descriptive prompts, which will guide AI to understand the image content you expect to generate. For example, you can enter specific descriptions like "sunset on a summer beach" or "abstract style horse". You can also set relevant image parameters such as size, color mode, and image style (realistic, cartoon, oil painting, etc.). If there are relevant column data that can be used as references or integrated elements, please fill them in accurately. These data will provide important information support for AI to generate images. Once the settings are completed, AI can generate images that meet the requirements based on these instructions.

<img src="https://book.bika.ai/bika-content/assets/T2M2bWfoao7rimxszeWlcIjsgac.png" src-width="2534" src-height="1444" align="center"/>

 

## AI Validator

> Coming Soon, this feature is not publicly available yet.

The AI Validator is a powerful field data validation tool designed to ensure that user input meets specific rules and requirements. It offers two primary validation methods: AI Prompt Validator and Script Validator, allowing users to choose based on their actual needs. The AI Validator can accurately validate data in various scenarios and promptly provide validation results to users, helping them improve data quality and reduce input errors.

### AI Prompt Validator

Use Case: In a company's performance management system, employees and teams need to set OKRs (Objectives and Key Results) at the end of each quarter. To ensure the effectiveness and measurability of OKRs, it's necessary to check whether the formulated Q3 OKRs contain quantifiable metrics. Using the AI Prompt Validator can quickly and efficiently screen a large number of OKR contents, avoiding vague and unmeasurable goals and key results.

<b>How to Use</b>

1. Create a validation rule:
    - Enter the AI Validator configuration interface and click the "Add Validation Rule" button.
        <img src="https://book.bika.ai/bika-content/assets/SwlvbVQuVoSMDbxrf2IlzNZigeb.png" src-width="2350" src-height="1334" align="center"/>
    - In the "Select Validation Rule Type" drop-down menu, choose the "AI Prompt Validator" option.
        <img src="https://book.bika.ai/bika-content/assets/XkfdbxPLEoPKqnx1RQilsyvBgNf.png" src-width="2348" src-height="1334" align="center"/>

2. Write AI prompt information:
    - In the "AI Prompt Information" input box, enter detailed and accurate validation prompt content: "Check the Q3 OKR content. Goals should clearly point to specific achievable outcomes, and key results must contain quantifiable metrics, such as specific numbers, percentages, quantities, etc. If quantifiable metrics are not included in the goals or key results, they are deemed invalid. The error message should be: The Q3 OKR you submitted contains unquantified goals or key results. Please add quantifiable metrics to ensure the measurability of OKRs for accurate assessment of goal completion." You can refer to the sample text in the input box and adjust and improve it according to actual needs. Ensure that the prompt information clearly conveys the validation criteria and requirements so that users can clearly understand the issues with their OKRs.
        <img src="https://book.bika.ai/bika-content/assets/EI6sbeOvtoy5W4x6vWeloX4Wg2e.png" src-width="2118" src-height="1196" align="center"/>
    - After completing the settings, click the "Save" button, usually located at the bottom of the page or in the operation bar. The system will then apply the validation rule you set to the corresponding Q3 OKR field.
        <img src="https://book.bika.ai/bika-content/assets/DXSKbRFZyotLI3xhLI3l7ZPhgCh.png" src-width="2118" src-height="1204" align="center"/>

3. Fill in content and validate:
    - When employees or teams fill in the Q3 OKR content in Bika and click to submit the form, the system will comprehensively validate the input OKR based on the AI prompt information. If the system finds that the OKR lacks quantifiable metrics in the goals or key results, it will immediately display the error message you set, requiring users to return and make changes until the OKR meets the quantification requirements before it can be successfully submitted.
        <img src="https://book.bika.ai/bika-content/assets/FC0ybPIGQo2CH4xnAcilmyeFgof.png" src-width="2376" src-height="1350" align="center"/>
        <img src="https://book.bika.ai/bika-content/assets/D2zfbQOTwoh26lxFvgKlsfhLgsb.png" src-width="2938" src-height="1656" align="center"/>
    - If the OKR content meets the quantification standards, it will be submitted smoothly and enter the subsequent approval or execution process.
        <img src="https://book.bika.ai/bika-content/assets/PtRFbJbdhoh59jxgH4flFBfJgYe.png" src-width="2184" src-height="1230" align="center"/>

By following the above steps, the AI Prompt Validator can effectively validate whether the Q3 OKR is quantified, helping enterprises ensure the quality and executability of OKRs, and providing strong support for performance management.

### Script Validator

Use Case: In the course registration system of an online education platform, users need to fill in the estimated weekly study hours they plan to dedicate to the course. The platform requires the input to be between 2 to 10 hours to ensure normal course progress. If the input is out of this range, the system will not allow submission and will prompt the user to modify.

<b>Implementation Steps:</b>

1. Create a validation rule:
    - Enter the AI Validator configuration interface and click the "Add Validation Rule" button.
        <img src="https://book.bika.ai/bika-content/assets/UbrjbJPoNoQ6apxOsPclpWAdg3c.png" src-width="1964" src-height="1114" align="center"/>
    - In the "Select Validation Rule Type" drop-down menu, choose the "Script Validator" option.
        <img src="https://book.bika.ai/bika-content/assets/GaWQbQ6ANo68zyxF4l2l4wOpgHb.png" src-width="2560" src-height="1456" align="center"/>

2. Write script content:
In the "Script Content" input box, write the following TypeScript script:
        ```ts
// Assuming the input value is stored in the variable 'value'
const studyHours = parseFloat(value);

if (studyHours < 2 || studyHours > 10) {
    return {
        valid: false,
        message: 'The weekly study hours you entered do not meet the requirements. They must be between 2 and 10 hours.'
    };
}

return {
    valid: true,
    message: null
};
```
        This script first checks if the input is a valid number and then determines if the study hours are within the 2 to 10 hour range. If not, it returns a validation failure result and prompts the user to modify; otherwise, it returns a validation success result.
    After completing the script writing, click the "Save" button to apply this validation rule to the "Estimated Weekly Study Hours" field.
    <img src="https://book.bika.ai/bika-content/assets/L3QhbufxHolrobxsB0ulf1lMgPe.png" src-width="3014" src-height="1718" align="center"/>

3. Fill in content and validate:
    - When users fill in the "Estimated Weekly Study Hours" field in the course registration system and attempt to submit, the system automatically executes the above script for validation. If the input hours do not meet the requirements, the system pops up the corresponding error message, preventing submission and informing the user of the issue.
        <img src="https://book.bika.ai/bika-content/assets/LqUfbvUskowQ8uxUpiflhbw7gdc.png" src-width="2290" src-height="1300" align="center"/>
    - Users must modify their input according to the prompt to bring it within the 2 to 10 hour range to successfully submit course registration information.
        <img src="https://book.bika.ai/bika-content/assets/BL0DbGAYGoMfyVx7Bm1laP7Ngig.png" src-width="2098" src-height="1180" align="center"/>

# Automation Workflow AI

Automation Workflow AI is an extremely distinctive feature of the Bika.ai platform. It supports batch execution of text-to-text, text-to-image, and text-to-video tasks, significantly reducing human effort, shortening the time required for work, and effectively improving overall work efficiency.

### Creation and Configuration Steps

1. Enter the Bika.ai Automation Workflow page (usually accessible by clicking on an "Automation Workflow" in the left sidebar), and click the "Add Executor" button. Give the new workflow a precise and meaningful name to facilitate quick identification and management later.
    <img src="https://book.bika.ai/bika-content/assets/H5RZbzLI1ohb0HxiW2ZltI4kgyg.png" src-width="2794" src-height="1686" align="center"/>

2. Based on the specific function you wish to achieve (such as text-to-text, text-to-image, or text-to-video), select the corresponding AI processing module. For example, for text-to-text:
    - Add a new executor, and in the many types provided by the platform, find the "OpenAI - Text Generation" option.
        <img src="https://book.bika.ai/bika-content/assets/SbT5bkaneoJDaQxextmlps5DgBd.png" src-width="2794" src-height="1688" align="center"/>
    - Accurately input the API key provided by OpenAI. This key is essential for ensuring smooth communication with OpenAI services.
        <img src="https://book.bika.ai/bika-content/assets/OfutbfY3EoOBpNx27wllergDgmf.png" src-width="2794" src-height="1686" align="center"/>
    - Choose a model ID from the list provided by OpenAI that fits your task requirements. Different models have varying capabilities and areas of expertise, so choose carefully.
    - Enter the prompt content clearly and in detail in the prompt box, such as "Generate a sales report." Accurate prompts help produce text content that meets your expectations. Click "Save" after completion.
        <img src="https://book.bika.ai/bika-content/assets/XDp6b0ygHobKdpx0jn2l9fgOgNg.png" src-width="2794" src-height="1682" align="center"/>

3. After completing all settings, click the "Run Test" button to conduct a small-scale test. Carefully review the generated text content from multiple dimensions, such as relevance, language fluency, and logical coherence, to determine if it meets your expectations. If any deviations or issues are found in the generated results, immediately return to the workflow editing interface and make targeted adjustments to parameters (such as model selection, prompt details, etc.) and settings until the content fully satisfies your requirements.
    <img src="https://book.bika.ai/bika-content/assets/VsNAbkhZYopkx8xbfoolxlovgUf.png" src-width="2794" src-height="1684" align="center"/>

4. Once the test is passed, save the configuration properly according to your actual business needs. Subsequently, you can flexibly output the results of the text-to-text generation to a database for statistical analysis, write them into a report as content support, or send them to a designated email for information sharing, fully leveraging the value of Automation Workflow AI.
    <img src="https://book.bika.ai/bika-content/assets/PyHnbHFo6oSSGxx0feQl8lFugmb.png" src-width="2794" src-height="1684" align="center"/>

5. Below is an example of a sales optimization report generated through the text-to-text function, which intuitively demonstrates the powerful content generation capabilities of the Bika.ai platform.
    <img src="https://book.bika.ai/bika-content/assets/RrovbXXSnowWwAxojo7lG5ptgqu.png" src-width="3674" src-height="2290" align="center"/>

### More AI Integrations

Bika.ai is equipped with powerful capabilities, integrating multiple advanced AI models to provide users with an intelligent experience. Below are some common integrated AI models and their functional introductions:

1. Open AI's GPT Models

Using Open AI's GPT models, users can generate natural language text within Bika.ai. For example, in scenarios such as writing reports, descriptions, summaries, and more, high-quality text content can be obtained quickly. It can also be used for intelligent Q&A and content polishing.

 

1. Alibaba Cloud Tongyi Qianwen 

Tongyi Qianwen is a large-scale language model developed by Alibaba Cloud. After integration with Bika.ai, it can provide various language-related intelligent services, such as generating copy, performing language translation, and understanding and processing natural language instructions.

 

1. Claude.ai

The Claude model family is a series of large language models. After integration with Bika.ai, it can assist users in text creation, data interpretation, and intelligent analysis. For example, when dealing with complex business data, Claude.ai can quickly extract key information and generate analysis reports.

 

1. Google AI (e.g., Gemini)

Google AI's large language models (such as Gemini) integrated into Bika.ai possess powerful language understanding and generation capabilities. Users can utilize them for intelligent conversations, content generation, and complex language logic processing.

 

1. ByteDance Doubao

Doubao, integrated into Bika.ai, provides intelligent conversation and content generation services. In office scenarios, whether it's quickly generating a framework for a proposal, answering business questions, or creating creative content, it offers strong support. 

Doubao, integrated into Bika.ai, provides intelligent conversation and content generation services. In office scenarios, whether it's quickly generating a framework for a proposal, answering business questions, or creating creative content, it offers strong support.

For more AI integrations, please refer to the integration list in the documentation: https://bika.ai/help/reference/integration/features-list

# AI Chatbot

> Coming Soon, this feature is not publicly available yet.

The AI Chatbot is an intelligent conversational service robot that can be pre-configured with AI and fine-tuned with internal data. It can connect to various internal enterprise systems to provide convenient interaction services.

The steps for operation are as follows:

First, click "Add," then select AI Chatbot, and proceed with the configuration. For example, you can choose to use search documents or big database as its knowledge base and information source, or select PDF, doc, and other files as information sources. Once configured, when you chat with the AI Chatbot, it will use these data sources for information input.

<img src="https://book.bika.ai/bika-content/assets/ZIv9bPh1JonKTnxHfAzlRhm0gVg.png" src-width="1440" src-height="900" align="center"/>

# AI Page

Used to create visual and interactive pages that integrate data, documents, forms, and other resources, enabling quick construction of showcase or functional interfaces such as project portals and data dashboards.

<img src="https://book.bika.ai/bika-content/assets/JmSFbj6ePo9yjsxyt3GlrBdngQU.png" src-width="1180" src-height="815" align="center"/>

