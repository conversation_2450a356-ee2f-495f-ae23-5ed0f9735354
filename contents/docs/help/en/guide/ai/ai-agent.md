---
title: AI Agent
slug: help/en/guide/ai/ai-agent
sidebar_position: 9
sidebar_label: AI Agent
---


# AI Agent

The AI Agent in Bika.ai goes far beyond simple "text-in, text-out".  

It comes with powerful capabilities to handle complex tasks, thanks to a wide range of <b>Skillsets</b> designed for real-world use. With these built-in Skillsets, AI agents can work with spreadsheets, create documents, generate images, call third-party APIs, and even connect to open-source MCP Servers to extend their abilities.  

Whether it’s organizing sales data, generating client reports, writing marketing copy, or automatically posting on social media, the AI Agent can handle it. This frees up your time and energy for creativity and decision-making.  

For <b>one-person companies</b> and <b>super individuals</b>, the AI Agent acts like an all-in-one assistant—capable of operations, analysis, and content creation, supporting your entire business pipeline. And with MCP Server integration, you can keep adding new skills anytime, allowing your Agent to continuously evolve into a reliable and flexible digital partner.  

<img src="https://book.bika.ai/bika-content/assets/Iwcpb4dm5oTJgLxOWMslLVJEgLz.jpeg" src-width="1920" src-height="1111"/>

## Getting Started

### Creating and Configuring an Agent

AI Agent is a type of resource node. To create one, click the "+" button at the top of the resource area and select "<b>new AI agent</b>".  

<img src="https://book.bika.ai/bika-content/assets/EL19b1LqLozP43xJDV9ll0hhgdb.jpeg" src-width="1771" src-height="1130" align="center"/>

A newly created agent is ready to chat right away. For more advanced tasks, you'll need to configure it and equip it with the right skills.  

Click the "..." in the top-right corner, then select "<b>Edit</b>" to open the Agent configuration panel.  

<img src="https://book.bika.ai/bika-content/assets/O4akbq0hdo5GDSxlRuQlGhxWg9c.png" src-width="1280" src-height="741" align="center"/>

Configuration options include:  

- <b>Resource name</b>: Name of the Agent  
- <b>Resource description</b>: A short description of the Agent. (Agents are "digital teammates" in your space—they'll show up in "Chat" and "Team" tabs. A clear description helps differentiate roles.)  
- <b>AI Model</b>: The model the Agent uses for conversations and tasks. The default is <b>Auto</b>, which lets the system choose the best model. As LLMs improve, "Auto" will automatically point to the latest option. Each model has different strengths, so pick what fits your use case. Conversations with Agents consume <b>Space Credits</b>. Costs vary by model (check <b>Space Settings &gt; Billing</b> to see usage).  
- <b>Prompt</b>: The preset instructions for the Agent. This defines its role, goals, and workflow. You can customize this to fit your needs. (Tips and templates are available in the community.)  

<img src="https://book.bika.ai/bika-content/assets/P5n1bQGJ6otR3Fx1ypTl1SuygBb.png" src-width="1920" src-height="1112" align="center"/>

- <b>Data sources</b>: Specify which data sources the Agent can access. For security reasons, it can only use data you configure here. Supported sources include databases, documents, automations, and folders.  
    <img src="https://book.bika.ai/bika-content/assets/H0J9bjeKgobphXxTjoZlfvvsgAd.png" src-width="1920" src-height="1153" align="center"/>

- <b>Skillsets</b>: A collection of skills the Agent can use. Bika.ai provides many ready-to-use Skillsets for different scenarios.For example, you can use a database skillset to read/write data from a database. AI Agents also support integration with 2000+ MCP Servers, giving them access to the internet and even real-world devices.  
    <img src="https://book.bika.ai/bika-content/assets/KRMsbY3HGocZLFxkgwXl9TdcgMd.png" src-width="1920" src-height="1112" align="center"/>

### Chatting with an Agent

Once you’ve set up prompts, data sources, and Skillsets, your Agent is ready to work.  

Here’s an example of an Agent specialized in writing tweets:  

I shared a screenshot of an article, and the Agent searched online to draft a tweet for me. After I approved, it published the tweet directly.  

<img src="https://book.bika.ai/bika-content/assets/DnYEb5Zl1osf0uxMSvslkKlugOh.png" src-width="1920" src-height="1112" align="center"/>

### Switching and Sharing Conversations

In the top-right corner of the Agent interface, you’ll find the <b>Conversation History</b> button.  

- Click an item to switch conversations  
- Click the “...” on a conversation card to <b>share</b> or <b>delete</b> it  

<img src="https://book.bika.ai/bika-content/assets/J4IQbbd3YoWxiNxd2GmlcBEUgJf.png" src-width="1920" src-height="1112" align="center"/>

Conversations can be shared via link. For example, if an agent generates a great research report, you can share it with your team or community.  

There are two visibility options:  

- <b>Only space members or visitors can access</b>
- <b>Internet users with the link can access</b>
    
If you choose the second, you’ll also see a <b>Publish to Community</b> toggle. Enabling it makes the conversation visible to all users in the Bika community.  

<img src="https://book.bika.ai/bika-content/assets/ISacbJQaDoMHGGxBS4ild6xQgXs.png" src-width="1920" src-height="1112" align="center"/>

### Recent Conversations

Since AI agents can be stored in different folders and levels within the resources tab, it can be tricky to find them.  

The "<b>Chat</b>" tab in the left sidebar shows all recently used Agents. Just click an Agent’s avatar to continue your conversation.  

<img src="https://book.bika.ai/bika-content/assets/DOjFbTojwo9wLcx1nlilyvj1gmh.png" src-width="1920" src-height="1112" align="center"/>

### Agents in Your Team

In Bika, AI agents are treated as <b>digital employees</b>.  

That means they also appear in the <b>Team</b> tab, where you can assign them to different groups or departments based on their role—making it easier to manage your AI workforce.  

<img src="https://book.bika.ai/bika-content/assets/Dch6b0VJzoSQvkx9qeRlGzZXgxc.png" src-width="1920" src-height="1111" align="center"/>

### AI Models and Billing

Different models have different computational costs, which means credits are consumed at different rates.  

To make this easier to understand, we use a <b>Multiplier</b> system:  

- <b>Multiplier = 1</b> → baseline cost  
- <b>Multiplier &gt; 1</b> → more powerful, but uses more Credits  
- <b>Multiplier &lt; 1</b> → lighter and cheaper to run  
    
Examples:  

- Model A (Multiplier = 1), Model B (Multiplier = 2) → B costs about 2x as much as A  
- Model C (Multiplier = 0.5) → C costs about half of A  
    
⚠️ Notes:  

- Multipliers are for <b>relative comparison only</b>; they are not exact credit formulas  
- Actual usage may vary depending on input length and output size  
    
<table>
<colgroup>
<col width="200"/>
<col width="200"/>
</colgroup>
<tbody>
<tr><td><p>Model</p></td><td><p>Multiplier</p></td></tr>
<tr><td><p>GPT-4.1</p></td><td><p>1x</p></td></tr>
<tr><td><p>GPT-4.1 Mini</p></td><td><p>0.28x</p></td></tr>
<tr><td><p>Qwen3 Coder Plus</p></td><td><p>0.12x</p></td></tr>
<tr><td><p>Qwen3 Plus</p></td><td><p>0.03x</p></td></tr>
<tr><td><p>Claude 3.7 Sonnet</p></td><td><p>1.25x</p></td></tr>
<tr><td><p>Claude 4 Sonnet</p></td><td><p>1.25x</p></td></tr>
</tbody>
</table>

## Q&A

<b>Q: Can I run multiple AI agents at the same time?</b>  

Yes. Some tasks may take a while to finish, but you don't need to wait. You can switch to other agents anytime. Tasks run in the cloud and won’t be interrupted if you leave the page.  

<b>Q: Can other space members see my Agent's conversation history?</b>  

If a member has access to the Agent's node, they can view all of its past conversations—including those with other people. They can also continue chatting from an existing conversation.  

If you'd like a private agent, you can create one under the <b>Personal</b> area in the Resources tab or adjust the agent's node permissions so only you (or selected members) can access it.  

<b>Q: Can I connect my own MCP Server to an Agent?</b>  

Yes, with some requirements. Your MCP Server must provide a publicly accessible URL and use <b>Streamable HTTP</b> as the transport protocol.  

In the Agent configuration panel, go to <b>Skillsets</b>, select "<b>Custom MCP Server</b>", and enter your server parameters.  

For detailed instructions, see our community tutorial: xxx  

