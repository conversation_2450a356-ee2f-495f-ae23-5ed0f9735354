---
title: How to build a good AI Agent in Bika.ai?
slug: help/en/cookbook/_build-a-good-ai-agent
sidebar_position: 19
---


# How to build a good AI Agent in Bika.ai?

An AI Agent is an intelligent assistant that you can create independently on the Bika platform to help you complete various targeted work tasks. This guide will take building a "Brand Social Media Writing Assistant" as an example, leading you through configuring a customized AI Agent from scratch to meet the needs of copy generation and personalized posting on platforms such as X (formerly Twitter), Instagram, and Facebook.

# Creating and Configuring the Agent

## Create a Basic Agent and Set Information

First, we need to create a new Agent and clarify its core positioning to help team members quickly understand its purpose:

- Go to the "Resource" page of the workbench, click 「+ New Resource」 → select 「New AI Agent」;

<img src="https://book.bika.ai/bika-content/assets/Sm8UbeR8So7qfVxpw5klMxihgVC.png" src-width="2880" src-height="1800" align="center"/>

- Set up basic Agent information:
    - Name: Brand Social Media Writing Assistant
    - Description: Used to generate brand copy for platforms such as X, Instagram, and Facebook.
    <img src="https://book.bika.ai/bika-content/assets/Hp05bRV2koeeg7xbafklwqRdgWh.png" src-width="2880" src-height="1800" align="center"/>

- Click 「Save」 to proceed to the next configuration step.

## Select a Model - Choose a "Copywriting Brain" for the AI

Click the "Edit" button at the top right corner of the Agent interface to add corresponding configurations to the Agent. The model is like the AI's brain, determining its level of intelligence.

<img src="https://book.bika.ai/bika-content/assets/QkD7bS7WjoWR47xaiLDlReXSgGh.png" src-width="2880" src-height="1800" align="center"/>

When you enter the 「Model Selection」 interface, you can see the specific models currently supported. The model determines the creativity and logic of the copy generated by the AI. Here, we can select GPT-4.1.

<img src="https://book.bika.ai/bika-content/assets/VaIGb60KOortgFxlCcMlLrz4g2b.png" src-width="2880" src-height="1800" align="center"/>

## Write a Prompt - Define the "Copy Style" for the AI

A Prompt is the AI's "style guide" and needs to clarify the "platform characteristics", "brand tone", and "output requirements". The following is an exclusive template for "social media writing":

```sql
You are the "Brand Social Media Writing Assistant," responsible for generating copy for X, Instagram, and Facebook. You must strictly follow the rules below:

1. Brand tone: Overall style should be lively and friendly, avoiding stiff advertising tones (no vulgar or slang language).
2. Platform format:
   - X: Start with an emoji to attract attention (e.g., ✨), the main content should be divided into 3–4 modules (with subtitles), and end with 2–3 brand-related hashtags (e.g., #XXbrandSummerNew).
   - Instagram: Start with hashtags, keep the main content within 200 words, and end with "👉 Click the link to learn more."
   - Facebook: The main content can be longer but should remain concise, and end with "#BrandNewProduct."
3. Content requirements:
   - Must incorporate current trending topics on the platform (can be accessed via the "Content Research Skill").
   - When mentioning product information, the core selling points must be extracted from the "Brand Product Document" data source; no fabricated information.
   - Avoid using extreme words like "most," "first," or "top-tier."
```

<img src="https://book.bika.ai/bika-content/assets/AbLrbT9Hso3FFHxipajl2RxNgbg.png" src-width="2880" src-height="1800" align="center"/>

## Add Datasources — Feed the AI with Brand Knowledge

Datasources are the core basis for generating brand-compliant content. Currently, three types of data sources are supported:

- 📁 <b>Node Resources</b>: Select existing node resources from your <b>space</b>.
- 🌐 <b>URL</b>: Teach the AI content from a specific website.
- 🗺️ <b>Sitemap</b>: Import content from an entire website in bulk.

Here, we add two node resources, "Brand Guidelines" and "Historical Materials", as contextual references, and turn on the "Display the datasource in the input box" switch. This way, every member using this Agent can see which data sources are available on the chat interface. It is worth noting that ordinary members cannot delete or modify the data sources already configured by the administrator during use, but they can add their own data sources in the chat interface according to their needs, and this operation only takes effect for the current user.

<img src="https://book.bika.ai/bika-content/assets/B42GbquFWoBXvaxppyKlSuumgxf.png" src-width="2880" src-height="1800" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/RmpjbFG1aohXQDxArzNlCaYtgTd.png" src-width="2880" src-height="1800" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/JFJxb63Y8o9A3ZxVh39lFcVXgte.png" src-width="2880" src-height="1800" align="center"/>

## Set Up Skills - Determine What Operations the AI Can Perform

Skills determine the core capabilities of the AI and what operations it can execute. It's like assigning job functions to employees. We first configure the "Basic Copywriting Capabilities".

### Skill Types

- 🎯 Preset Skills (Recommended for Beginners): Standard skill packages provided by the platform, ready to use out of the box
- 🤖 Fully Automatic Skills: Allow the AI to judge which skills are needed by itself
- 🔗 Connect to Third-Party Applications (MCP ToolSDK): Connect to external tools and services, with the most powerful functions

### Add Skill Sets

Here, we can directly use the preset skill sets to meet our needs.Open the 「Skillsets」 interface and select the following "Preset Skills":

- 🎯 Bika Search: "bika_search_page" and "bika_search_images" — automatically search and capture relevant information
- 🎯 Bika Office: "generate_markdown_document" — automatically generate documents

<img src="https://book.bika.ai/bika-content/assets/U95sbXZUKoTIqbxq38wl6ghsgt4.png" src-width="2880" src-height="1800" align="center"/>

### Turn on the 「Append skillsets」 Switch - Allow Members to Append Skills

In 「Skill Permission Management」, check 「Allow members to append skillsets」. After checking, the AI will have 3 basic skills configured by the administrator by default. When team members use it, they can append "tool skills for the platform they are responsible for" in the chat input box (only valid for the current conversation and does not affect other members' use);

Examples:

- Members in charge of X: Can append the 「X API Tool」 — directly publish the generated copy to the brand's X account automatically after generation;
- Members in charge of Instagram: Can append the 「Instagram Hashtag Monitoring Tool」 — automatically add real-time trending hashtags (such as "#InstagramSummerNewProducts") when generating copy;
- Members in charge of Facebook: Can append the 「Facebook Copy Segmentation Tool」 — split long copy into "title + 3 short sentences" to adapt to the Facebook comment section layout;

Note: Members can only "append" skills and cannot delete the basic skills configured by the administrator (to avoid the loss of core capabilities).

<img src="https://book.bika.ai/bika-content/assets/YOUGbDiMpo5Es6xpGR3l1sn2gMe.png" src-width="2880" src-height="1800" align="center"/>

# Configuration Effect Preview

After completing the above 5 steps, you will see the following function buttons on the chat interface:

- 🔧 「Skill Icon」: Displays the 2 skills configured by the administrator; click to append skills (such as the "X API Tool");
- ➕ 「Plus Button」: Members can temporarily add personal data sources (such as "URL of recent popular topics on Facebook"), which is only valid for the current conversation;

<img src="https://book.bika.ai/bika-content/assets/DGZWb9k6MoSTuHx0QS4lJOkNg5e.png" src-width="2880" src-height="1800" align="center"/>

# Frequently Asked Questions (FAQ)

<b>Q: Will using the "Brand Social Media Writing Assistant" consume Credits?</b>

A: Yes, the consumption amount is linked to "model multiplier × number of generated words" (e.g., generating a 500-word copy with GPT-4 consumes "2× Credit multiplier × the quota corresponding to 500 words").

<b>Q: What is the difference in Credit consumption between different models?</b>

A: Basic models (such as GPT-3.5) have a "one X" (standard consumption), and high-level models (such as GPT-4, GPT-4.1) have a "two X" (double consumption). For details, you can check the labels on the model selection interface of the AI Agent (Autonomous AI Assistant).

<b>Q: Can the configured Agent be published externally?</b>

A: Currently, "team-wide sharing" is supported; the external publishing function has not been launched yet. You can pay attention to the 「Help Center Update Announcements」.

<b>Q: Can members modify the skill sets I (the administrator) configured?</b>

A: There are two scenarios:

- If the 「Allow members to append skillsets」 switch is not checked: Members can only use the skills configured by the administrator and cannot modify them;
- If the switch is checked: Members can only "append" skills and cannot delete the basic skills configured by the administrator.

<b>Q: Why can the skill sets in the chat box be modified sometimes and not others?</b>

A: It depends on whether the administrator has turned on the 「Allow members to append skillsets」 switch — if turned on, skills can be appended; if not turned on, only fixed skills can be used. In addition, if members add "temporary skills", they will automatically disappear after exiting the conversation and need to be re-added when entering next time.

<b>Q: Why can't I find the corresponding skill set?</b>

A: There may be two reasons: ① The skill has not been launched yet (platform skills are continuously updated; you can contact the administrator to feedback your needs); ② The skill belongs to "third-party application connection" (you need to add the API in 「Tool SDK」 first, then select it in the skill list).

<b>Q: Why does the copy generated by the AI not conform to the brand tone?</b>

A: It is recommended to check two points: ① Whether the data sources include "Brand Visual Guidelines Document" and "Historical High-Quality Copy"; ② Whether the Prompt clearly defines the brand tone (such as "avoid using professional terms and be colloquial"), and you can add more specific examples (such as "refer to the style of the popular X copy in May 2024").

