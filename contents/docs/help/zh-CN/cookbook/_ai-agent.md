---
title: ❌ AI Agent配置指南 副本
slug: help/zh-CN/cookbook/_ai-agent
sidebar_position: 19
---


# ❌ AI Agent配置指南 副本

AI Agent是你在Bika平台上创建的智能助手（❌ 改 Autonomoous AI Assistant），可以帮你完成各种工作任务。本指南将教你如何从零开始配置一个专属的AI Agent。

我们发现很多用户在配置AI Agent时会遇到困惑，不知道如何开始，也不清楚各种设置的作用。这份指南会用最简单的方式，带你一步步完成配置。

## 快速开始

创建AI Agent只需要四个步骤：

1. <b>选择模型</b> - 给你的AI选一个"大脑"
2. <b>添加数据源</b> - 告诉AI它需要学习什么知识
3. <b>设置技能</b> - 决定AI能做什么操作
4. <b>写个性化提示</b> - 让AI知道用什么风格工作

下面我们详细讲解每个步骤。

## 1. 选择模型

模型就像AI的大脑，决定了它有多聪明。

<b>简单选择方法：（不同模型的消耗）</b>

- 列出目前的模型

## 2. 添加数据源

数据源就是给AI的"学习资料"，让它了解你的业务和需求。

### 数据源类型

<b>📁 Node节点资源</b> 从你的空间站里选择现有的各类节点资源：❌ 跳转到节点类型的文档链接 放 noderesource 大的帮助文档

（新增跳转到对应的节点 新页签）

<em>Database节点</em> - 数据库资源 

```text
包含内容：客户信息、产品数据、订单记录等结构化数据
使用场景：让AI了解你的业务数据
```

<em>Document节点</em> - 文档资源

```text
包含内容：Word文档、PDF文件、文本资料等
使用场景：让AI学习公司制度、产品说明等
```

<em>⭐浏览器节点</em> - 特殊高级功能 （根据实际界面是否显示）

```text
使用场景：
- Gmail邮箱管理
- 社交媒体发布
- 后台数据查看

配置方法：
1. 先创建一个浏览器节点
2. 在节点中登录目标网站
3. 将这个节点添加为数据源
```

<b>🌐 URL网址</b> 让AI学习某个网站的内容：

```text
使用场景：
- 公司官网信息
- 产品介绍页面  
- 行业资讯网站
- 技术文档网站

操作方法：
1. 复制网址链接
2. 粘贴到"URL"输入框
3. AI会自动抓取内容
```

<b>📄 Sitemap网站地图</b> 批量导入整个网站的内容：

```text
适用情况：企业官网、文档站点
注意：会消耗较多Token，建议谨慎使用
```

### 管理员 vs 用户权限  

打钩了   append  你的成员们 就可以根据他们

<b>管理员模式</b>（配置者）：

- 可以设置所有数据源
- 决定哪些数据源对用户可见
- 锁定重要的数据源

<b>用户模式</b>（使用者）：

- 可以临时添加自己的数据源
- 添加的数据源只在当前对话中有效
- 不能删除管理员设置的数据源

## 3. 设置技能

技能决定了AI能执行什么操作。就像给员工分配工作职能一样。

### 技能类型

<b>🎯 预设技能（推荐新手）</b> 平台提供的标准技能包，开箱即用：

- <b>搜索技能</b> - 在网上查找信息
- <b>研究技能</b> - 深度调研和分析
- <b>办公技能</b> - 制作PPT、写文档
- <b>浏览器技能</b> - 操作网页（需要配合浏览器节点）

```text
选择建议：
- 客服助手 → 搜索 + 办公技能
- 内容创作 → 研究 + 办公技能  
- 数据分析 → 搜索 + 办公技能
- 社媒管理 → 浏览器 + 搜索技能
```

<b>🤖 全自动技能</b> 让AI自己判断需要什么技能：

- 优点：智能、灵活
- 缺点：消耗更多Token（更贵）
- 适合：复杂多变的任务

<b>🔗 连接第三方应用 (MCP ToolSDK)</b> 连接外部工具和服务，功能最强大：

```text
热门连接：
- Twitter API - 自动发推文
- Google Analytics - 网站数据分析
- Gmail API - 邮件自动处理
- Slack - 团队协作
- 数据库连接 - 查询和更新数据
```

### 浏览器技能特别说明（英文版屏蔽）

浏览器技能有两种用法：

<b>基础用法</b>：

- 每次使用都启动新的浏览器
- 没有登录状态
- 适合浏览公开网页

<b>高级用法</b>：

- 使用已经登录的浏览器节点
- 保持你的登录状态
- 可以操作需要登录的网站

```text
高级配置步骤：
1. 创建浏览器节点
2. 在节点中登录目标网站（如Gmail）
3. 添加浏览器技能时选择这个节点
4. AI就能以你的身份操作网站了
```

### 技能权限管理   （改）

<b>管理员设置</b>：

- 锁定基础技能，用户不能删除
- 决定用户是否可以添加新技能

<b>用户使用</b>：

- 只能在管理员允许的基础上添加技能
- 不能删除管理员配置的技能
- 遵循"只能加，不能减"的原则

补充  （上面有一个配置叫做允许成员可以添加技能集好，这个按钮是什么意思？在当你配置了这一个技能级之后，那么这个 AI A9呢？它就拥有了这一系列固定的那个 skill set 技能级了，比如说你设置了一个浏览器跟一个那一个写作的那一个技能级，那么这个 A9就会调用这两个技能级，而用户他在输入框的时候就只能看到这个技能级，并且不可修改。但是如果说。你希望配的这两个技能级之外，用户他在输入框的时候又可以做更多的一些技能级，那你就可以勾选出这一个允许用户的添加更多的技能级的这个选项，让用户添加更多的选项。）

## 4. 系统提示词

这是给AI的"工作指导"，告诉它用什么风格和态度工作。

### 简单模板

<b>客服助手：</b>

```text
你是一位专业、耐心的客服代表。回答问题时要：
- 语气友好、礼貌
- 提供准确的信息
- 如果不确定，主动查找资料
- 遇到无法解决的问题，指导用户联系人工客服
```

<b>内容创作助手：</b>

```text
你是一位有创意的内容创作专家。工作时要：
- 保持品牌调性一致
- 内容要有趣、有价值
- 根据目标受众调整语言风格
- 确保内容原创，避免抄袭
```

<b>数据分析师：</b>

```text
你是一位严谨的数据分析师。分析时要：
- 基于事实和数据说话
- 提供清晰的结论和建议
- 用图表和数据支撑观点
- 客观中立，不带个人情感
```

## 实际配置案例

### 案例1：客服聊天机器人

<b>场景</b>：为电商网站创建自动客服

<b>配置清单</b>：

```text
🧠 模型：标准对话模型
📚 数据源：
  - 产品目录文档
  - 常见问题FAQ
  - 售后政策文档
🛠️ 技能：搜索技能 + 办公技能
💬 提示词：专业友好的客服风格
```

<b>效果</b>：能回答产品问题、处理订单咨询、提供售后指导

### 案例2：社交媒体助手

<b>场景</b>：自动管理公司X

<b>配置清单</b>：

```text
🧠 模型：创意写作模型
📚 数据源：
  - 品牌指导文档
  - 浏览器节点（已登录X）
🛠️ 技能：
  - 浏览器技能（选择X浏览器节点）
  - 搜索技能（监控热点）
💬 提示词：活泼专业的品牌声音
```

<b>特殊配置</b>：

1. 创建浏览器节点，登录X账号
2. 在技能设置中选择这个节点
3. AI就能以公司身份发布内容了

<b>效果</b>：自动发布X、回复评论、追踪热点话题

### 案例3：邮件处理助手

<b>场景</b>：自动处理Gmail客服邮件

<b>配置清单</b>：

```text
🧠 模型：文本处理模型
📚 数据源：
  - 邮件回复模板
  - 客户信息database
  - 浏览器节点（已登录Gmail）
🛠️ 技能：
  - 浏览器技能（Gmail操作）
  - 办公技能（邮件编写）
💬 提示词：专业的客服邮件风格
```

<b>效果</b>：自动分类邮件、起草回复、标记重要邮件

### 案例4：数据报告生成器

<b>场景</b>：每天自动生成网站流量报告

<b>配置清单</b>：

```text
🧠 模型：数据分析模型
📚 数据源：
  - 历史数据database
  - Google Analytics API
🛠️ 技能：
  - 第三方连接（Google Analytics）
  - 办公技能（制作报告）
💬 提示词：客观严谨的分析师风格
```

<b>效果</b>：每天自动获取数据、生成分析报告、发送给相关人员

## 配置界面说明 （配置效果的展示）

配置完成后，你会在聊天界面看到：

<b>按钮说明</b>：

- <b>🔧 技能图标</b> - 显示当前AI的技能配置
- <b>➕ 加号</b> - 临时添加学习资料（只在这次对话有效）
- <b>🛠️ 工具</b> - 修改AI的技能设置

## 常见问题

<b>Q: 为什么我的AI回答不准确？</b> A: 可能是数据源不够全面，尝试添加更多相关资料。

<b>Q: AI消耗Token太快怎么办？</b> A: 避免使用"全自动技能"和"Sitemap导入"，选择具体的预设技能。

<b>Q: 怎么让AI保持登录状态操作网站？</b> A: 使用浏览器节点功能，先登录再配置技能。

<b>Q: 用户可以修改我设置的技能吗？</b> A: 取决于你的权限设置，用户只能添加技能，不能删除你锁定的技能。

<b>Q: 数据源会显示给用户看吗？</b> A: 取决于你的显示设置，可以选择隐藏或显示。

Q： 使用 ai agent 会消耗 Credit 

不同模型的 credit 有什么区别吗

我的 agent 可以对外发布吗

我配置好的技能集 用户还能改吗

- 什么时候能改 什么时候不能改
- 聊天框里的技能集有时候可以改 有时候不可以改

我有一个需求 但是找不到我想要的技能集 为什么

## 
---

# 
