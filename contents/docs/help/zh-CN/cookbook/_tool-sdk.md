---
title: Bika.ai 如何通过与 ToolSDK.ai 实现无限的 MCP 集成
slug: help/zh-CN/cookbook/_tool-sdk
sidebar_position: 9
sidebar_label: Bika.AI × ToolSDK.ai MCP集成指南
---


# Bika.ai 如何通过与 ToolSDK.ai 实现无限的 MCP 集成

在之前的《AI Agent Cookbook：搭建品牌社交媒体写作助手》中，我们介绍了 Bika AI 的几种技能集类型：<b>预置技能集、全自动技能集、 连接第三方应用 (MCP ToolSDK) </b>。本指南将重点介绍 <b>Tool SDK 技能集</b>，解释为什么 Bika.AI 能够实现无限扩展的第三方应用集成

## 一、ToolSDK 简介

ToolSDK 是一个免费的 TypeScript SDK，专为构建 AI Agent 和 Automation 而设计。它让开发者可以轻松创建自动化应用，通过一行代码即可连接超过5,000个不同的 MCP Server 和 AI工具

<img src="https://book.bika.ai/bika-content/assets/S32gbKyrvo1XiexJeYtlz0Hggff.png" src-width="2560" src-height="1440" align="center"/>

### 核心特性：

- 🔗 <b>丰富集成</b>：支持超过5,000个MCP Server和AI工具
- 🛠️ <b>简单易用</b>：一行代码即可完成集成
- 🔄 <b>无限扩展</b>：支持自定义MCP Server开发
- 🌐 <b>广泛兼容</b>：兼容Zapier协议等主流集成标准

## 二、为什么 Bika.AI 选择 Tool SDK ？

### 开发痛点分析

在产品开发过程中，我们团队遇到了大量第三方应用集成需求：

- 对接 Slack 进行团队协作
- 发送 Email 和 WhatsApp 消息
- 连接各类数据源和 API 服务

传统开发模式存在以下问题：

1. 研发成本高昂

根据 IT咨询报告显示，企业每年 IT 预算的 20%投入在集成（Integrations）和配置（Configuration）上，造成大量时间和资源浪费

<img src="https://book.bika.ai/bika-content/assets/UuUJbWPqootSPMxhAbGlvQ3ygvh.png" src-width="1602" src-height="1042" align="center"/>

1. 重复造轮子

不同项目间缺乏代码复用，相似的集成功能需要反复开发

1. 维护成本递增

随着集成数量增长，维护和更新成本呈指数级上升

### Bika.AI 的解决方案

正是这些痛点催生了 Zapier、Make.com 、n8n 等集成平台的兴起。它们将分散的第三方应用集成能力聚合在统一平台上，让普通用户无需开发即可连接不同应用。

Bika.AI 在开发过程中同样面临这个问题。我们的两个核心功能——<b>自动化流程（Automation）和AI Agent</b>——都需要大量第三方应用支持：

- <b>Automation </b>需要各种 Action 和外部行为（如发邮件、发 Slack）
    <img src="https://book.bika.ai/bika-content/assets/ZmTZbbebtoUXtLxOHFhlBkfAg6O.png" src-width="2560" src-height="1440" align="center"/>

- <b>AI Agent </b>需要丰富的技能集来处理不同场景
    <img src="https://book.bika.ai/bika-content/assets/C3KpbJt6EoJt4Yxz97dluZwng5y.png" src-width="2560" src-height="1440" align="center"/>

### MCP 协议的机遇

随着 MCP（Model Context Protocol）协议的推出，我们看到了新的可能性。虽然 MCP 设计初衷是为 AI 服务，但本质上它就是连接不同应用

因此，我们决定：

1. <b>利用 MCP Server 能力构建自动化应用连接</b> - 将 MCP Server 作为统一的第三方应用连接层
2. <b>封装成标准化技能集</b> - 将各种 MCP Server 封装成可复用的第三方应用技能集
3. <b>兼容主流协议</b> - 同时支持 Zapier 等主流集成协议，确保广泛兼容性
4. <b>统一共享架构</b> - 让 Automation 和 AI Agent 共享所有 Tool SDK 集成能力

通过这种架构设计，我们省去了成千上万的研发工时，实现了真正的无限扩展能力。

## 三、在 AI Agent 中使用 Tool SDK：社交媒体助手案例

AI Agent 是你在 Bika 平台上可自主创建的智能助手，可以帮你完成各类定向工作任务。将以搭建"Twitter 运营助手"为例，带你从零开始配置专属 AI Agent，解决 Twitter 内容发布、粉丝互动等日常运营需求。

### 用户故事：快速搭建 Twitter 发布助手

<b>背景</b>：小张负责公司的社交媒体运营，每天需要发布 Twitter 内容并与粉丝互动。她希望创建一个 AI 助手，能够帮助她自动发布推文、回复粉丝评论，将操作时间从 15 分钟缩短到 2 分钟。

### 创建和配置 Agent

#### 步骤1: 创建基础 Agent 并设置信息

首先，我们需要创建一个空白 Agent 并明确其核心定位，方便团队成员快速理解用途：

- 进入工作台"Resource"页面，点击「+ 新建资源」→ 选择「New AI Agent」
    <img src="https://book.bika.ai/bika-content/assets/UiatbH7fxoPFdexOBjMl29fVgqh.png" src-width="2560" src-height="1440" align="center"/>

- 设置 Agent 基础信息： 
    - 名称：Twitter 运营助手
    - 描述：帮助发布 Twitter 内容和管理粉丝互动
    <img src="https://book.bika.ai/bika-content/assets/UJYvbRbeuoUkc3xkyDjlwz9zgle.png" src-width="2560" src-height="1440" align="center"/>

#### 步骤2：写 Prompt——给 AI 定"运营风格"

Prompt 是 AI 的"运营指南"，需明确"平台特性""内容调性""互动要求"，以下为"Twitter 运营"专属模板

1. 点击 Agent 界面右上方的"编辑"按钮，给 Agent 添加对应的配置。模型就像 AI 的大脑，决定了它在社交媒体运营上的表现能力。
    <img src="https://book.bika.ai/bika-content/assets/W3Job5TC8oXqDexbfERlt7YogHd.png" src-width="2556" src-height="1438" align="center"/>

2. 在Prompt设置中输入：

```text
你是Twitter运营助手，主要功能：
1. 根据用户要求发布推文
2. 自动添加合适的话题标签
3. 回复粉丝的评论和私信
保持友好、专业的语调
```

<img src="https://book.bika.ai/bika-content/assets/WqUubVcxxoCKACxN69xlbFr9gdh.png" src-width="2560" src-height="1440" align="center"/>

#### 步骤3：设置技能——决定 AI 能做什么操作

技能决定了 AI 的核心能力，就像给员工分配工作职能一样。我们需要配置"Twitter 运营核心能力"：

1. 添加技能集
    <img src="https://book.bika.ai/bika-content/assets/CnMibK1v6oIGsfxvfxalMPx3gAa.png" src-width="2560" src-height="1440" align="center"/>

2. 在搜索框中输入"Twitter" 选择" X(Twitter) MCP "技能集并添加
3. 按提示完成 Twitte r账号授权
4. [ 获取 X(Twitter) MCP 的 Key 和 Token](https://developer.twitter.com/) 并填入
    <img src="https://book.bika.ai/bika-content/assets/GABpbzrM4obUlwxkg8ClXcXngue.png" src-width="2560" src-height="1440" align="center"/>

5. 开启 X(Twitter) - Reply Tweet 和  X(Twitter) - Create Tweet 技能并保存
    <img src="https://book.bika.ai/bika-content/assets/TktEboNyco7G2gxjwUEl1q8UgTc.png" src-width="2560" src-height="1440" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/Q1BxbfLy5ofcXdxVybclAjOBgR4.png" src-width="2560" src-height="1440" align="center"/>

如果不确定要搜索什么应用，你可以访问 [ToolSDK.ai](https://toolsdk.ai) 查看完整应用列表。

#### 使用效果

现在小张只需要说："刚刚尝试了Bika.ai，我真的很印象深刻 🚀。它可以帮助我快速创建页面，管理数据，甚至与我的团队在一个地方进行协作。超级流畅，节省了很多时间!这值得一查。# AI # 生产力 # BikaAI"

<img src="https://book.bika.ai/bika-content/assets/G4oZbtGM5oGPQNxFdT8lMZYuguh.png" src-width="2560" src-height="1440" align="center"/>

AI助手就会：

- 自动生成推文内容
    <img src="https://book.bika.ai/bika-content/assets/Zlczbd43po0BnDx8FJRl8JkLgVh.png" src-width="2560" src-height="1440" align="center"/>

- 添加相关话题标并发布到 Twitter 账号
    <img src="https://book.bika.ai/bika-content/assets/LldbbhI5go5JebxdUSGlSMq9gze.png" src-width="2560" src-height="1440" align="center"/>

<b>效果提升：操作时间从15分钟缩短到2分钟！</b>

### 常见问题（FAQ）

<b>Q：如何找到我需要的 Tool SDK？</b>
 A：可以访问 [ToolSDK.ai](https://toolsdk.ai) 查看完整的 Tool SDK 应用列表，或在自动化配置中执行器 MCP Server (by ToolSDK.ai) 搜索应用名称（如"X(Twitter)"、"Slack"等）

<b>Q：使用"Twitter 运营助手"会消耗 Credit 吗？</b>
 A：会，消耗额度与“模型倍率×生成字数”挂钩（例：用GPT-4生成500字文案，消耗“2倍Credit×500字对应额度”）

<b>Q：不同模型的Credit消耗有什么区别？</b>

<b> </b>A：基础模型（如GPT-3.5）为“1倍X”（标准消耗），高级模型（如GPT-4、GPT-4.1）为“2倍X”（双倍消耗）。具体可查看AI Agent（自主人工智能助手）的模型选择界面上的标注

<b>Q：Twitter 账号授权安全吗？</b>
 A：平台支持 OAuth 1.0a 和 OAuth 2.0 两种标准授权协议，不会存储你的第三方应用密码。OAuth 1.0a 使用数字签名验证每次请求，OAuth 2.0 依赖 HTTPS 加密和访问令牌机制，两种协议都能确保授权安全，且你可以随时在对应应用的设置中撤销授权

<b>Q：为什么 AI 发布的推文不符合预期？</b>
 A：建议检查两个方面：① 数据源是否添加了"品牌规范""历史优质推文"；② Prompt 是否明确了内容风格和要求。

<b>Q：如何处理 Twitter API 限制？</b>
 A：平台会自动处理 API 频率限制，如遇到限制会提示用户，建议合理安排发布频次。

## 四、在 Automation 中使用 Tool SDK：邮件营销自动化案例

Automation 是你在 Bika 平台上可自主创建的智能工作流，可以帮你实现各类业务流程的自动化。将以搭建"新用户欢迎通知流程"为例，带你从零开始配置专属自动化流程，解决用户注册后的邮件发送、团队通知等营销自动化需求。

### 用户故事：设置新用户欢迎通知

<b>背景</b>：小王运营一个在线课程网站，每天都有新用户注册。他希望当有新用户注册时，系统能够自动发送个性化欢迎邮件并通知团队，将团队响应新用户的时间从几小时缩短到几分钟，提升用户体验和团队协作效率。

### 创建和配置 Automation

#### 步骤1：创建自动化流程——给系统“定任务”

首先，我们需要创建一个空白的自动化流程并明确其触发条件，让系统知道什么时候开始工作：

1. 进入 Automation页面，点击"+ 新建流程"
    <img src="https://book.bika.ai/bika-content/assets/LIwsbd0RUoaxzGxR0SrloKXHgie.png" src-width="2560" src-height="1440" align="center"/>

2. 命名为"新用户欢迎流程"
    <img src="https://book.bika.ai/bika-content/assets/SHvxbNyCWov0uoxJ88GlstjxgXg.png" src-width="2560" src-height="1440" align="center"/>

3. 设置触发器：选择 Record Created
    <img src="https://book.bika.ai/bika-content/assets/NrTDbFdVwor5PPxSvw1lJ5BYg6d.png" src-width="2560" src-height="1440" align="center"/>
    - 选择你的注册用户数据表"Registered Users"（你可以与你的平台集成，自动将新用户添加到列表）
        <img src="https://book.bika.ai/bika-content/assets/CmqPbkpGYo4GdKx7XeslsdIfgth.png" src-width="2560" src-height="1440" align="center"/>
        <img src="https://book.bika.ai/bika-content/assets/NwP5bo7jIoOZecxUa7rlkXZZgTg.png" src-width="2560" src-height="1440" align="center"/>

这样系统就知道「只要这个 Webhook 被调用，就要启动流程」。

#### 步骤2：添加邮件 Action——让系统“打招呼”

1. 我们可以使用 Send Email 发送欢迎邮件
2. 添加执行器，在类型中输入 Send Email 
    <img src="https://book.bika.ai/bika-content/assets/HP9abPi2woRD5DxTIPtlvXwngkg.png" src-width="2560" src-height="1440" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/E2qkbdUc6ofebHxJ0MVlzoSggRe.png" src-width="2556" src-height="1438" align="center"/>

3. 选择 Bika Email Service 发送邮件技能
    <img src="https://book.bika.ai/bika-content/assets/Fy9JbvjUKoVmy4xnbTEl1fzag1e.png" src-width="2560" src-height="1440" align="center"/>

4. 配置邮件内容： 
    - 收件人：{{用户邮箱}}
    - 主题："欢迎来到bika！"
    - 内容：包含欢迎语和产品介绍
        <img src="https://book.bika.ai/bika-content/assets/M19cbhqSXoL4DgxnNj4ldRxIgOf.png" src-width="2560" src-height="1440" align="center"/>

这样一来，每个新注册的用户都会自动收到欢迎邮件。

<img src="https://book.bika.ai/bika-content/assets/T5p9bFqwQoYFtpxB8zblALFlgyU.png" src-width="697" src-height="714" align="center"/>

#### 步骤3：添加团队通知 Action——让团队“马上知道”

1. 继续添加 Action → 选择"MCP Server (by Tool'SDK.ai)"
    <img src="https://book.bika.ai/bika-content/assets/YvGSbYRJCoxDsixlVqYlRdzCgqf.png" src-width="2560" src-height="1440" align="center"/>

2. 选择"Slack MCP Server"
3. 连接 MCP Server [（如何获取 Slack Token）](https://api.slack.com/apps)
    <img src="https://book.bika.ai/bika-content/assets/RqHKbAJ2Lo5tdDxFQJxlhmGGgJc.png" src-width="2560" src-height="1440" align="center"/>

4. 配置通知内容： 
    - 频道：Bika-team
    - 消息："新用户 {{用户姓名}} 已注册"
    <img src="https://book.bika.ai/bika-content/assets/XinkbxUlNoy58DxJEPxlM4qygRL.png" src-width="2560" src-height="1440" align="center"/>

这样，Slack 频道里的小伙伴就能实时收到新用户的提醒。

#### 步骤4：测试和发布——“彩排+开演”

1. 点击「测试运行」来验证邮件和 Slack 通知是否正常
    <img src="https://book.bika.ai/bika-content/assets/TwmsbmDYroCropx4WWplOIRZgsc.png" src-width="2560" src-height="1440" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/K7Albejjyo9KZvx58Uvl3J5Lgjh.png" src-width="1370" src-height="1394"/>

2. 确认无误后，点击「开启」，让流程自动上线

<img src="https://book.bika.ai/bika-content/assets/OhIqbNQ2towyKBxufGxlKSGug8f.png" src-width="2560" src-height="1440" align="center"/>

#### 使用效果

现在每当有新用户注册时，系统会自动：

- ✅ <b>即时触发</b>：检测到用户注册事件后立即启动
- ✅ <b>个性化邮件</b>：发送包含用户姓名的欢迎邮件
- ✅ <b>团队通知</b>：在 Slack 频道推送新用户信息
- ✅ <b>执行记录</b>：记录完整的执行日志和结果

<b>团队响应新用户的时间从几小时缩短到几分钟！</b>

### 常见问题（FAQ）

<b>Q：如何找到我需要的 Tool SDK？</b>
 A：可以访问 [ToolSDK.ai](https://toolsdk.ai) 查看完整的 Tool SDK 应用列表，或在自动化配置中执行器 MCP Server (by ToolSDK.ai) 搜索应用名称（如"X(Twitter)"、"Slack"等）

<b>Q：使用"Tool SDK Actions"会消耗 Credit 吗？</b>
 A：会，消耗额度与“模型倍率×生成字数”挂钩（例：用GPT-4生成500字文案，消耗“2倍Credit×500字对应额度”）

<b>Q：不同模型的Credit消耗有什么区别？</b>

<b> </b>A：基础模型（如GPT-3.5）为“1倍X”（标准消耗），高级模型（如GPT-4、GPT-4.1）为“2倍X”（双倍消耗）

<b>Q：Tool SDK 授权安全吗？</b>
 A：平台支持 OAuth 1.0a 和 OAuth 2.0 两种标准授权协议，不会存储你的第三方应用密码。OAuth 1.0a 使用数字签名验证每次请求，OAuth 2.0 依赖 HTTPS 加密和访问令牌机制，两种协议都能确保授权安全，且你可以随时在对应应用的设置中撤销授权

<b>Q：可以同时使用多个 Tool SDK 吗？</b>
 A：可以。一个自动化流程中可以添加多个不同的 Tool SDK Actions，它们会按照设置的顺序依次执行

<b>Q：Tool SDK 有调用频率限制吗？</b>
 A：会受到第三方应用自身的 API 限制。例如：Slack 每分钟最多发送 1 条消息到同一频道。平台会自动处理这些限制并提供相应提示

## 五、扩展 MCP 服务器：自定义与社区贡献

除了使用现有的 MCP 服务器，Bika.ai 还支持两种方式来扩展功能：

### Custom MCP Server（自定义 MCP 服务器）

#### 什么是 Custom MCP Server？

Custom MCP Server 允许你为特定的 AI Agent 添加完全自定义的功能，无需等待官方审核。

#### 使用场景

- 企业内部专有系统集成
- 测试阶段的 MCP 服务器
- 个人定制化需求

#### 如何使用

1. 在 AI Agent 的技能设置中，选择"Custom MCP Server"
    <img src="https://book.bika.ai/bika-content/assets/XB3pbecObo86Pox2pZSla30Sgog.png" src-width="2560" src-height="1440" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/MGZKbUjvIoZPXBxeal3lsBXVgOg.png" src-width="2560" src-height="1440" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/NSpGbZ3yuopdm8x9W26lMWAIgTg.png" src-width="2560" src-height="1440" align="center"/>

2. 填入你的 MCP 服务器配置信息
    <img src="https://book.bika.ai/bika-content/assets/Mbe3bNagjoy888xHhyylEhmsgNx.png" src-width="2560" src-height="1440" align="center"/>

3. 该自定义服务器<b>仅对当前 AI Agent 生效</b>

#### 重要说明

- ✅ <b>AI Agent 支持</b>：可以直接添加和使用
- ❌ <b>Automation 不支持</b>：自动化流程暂不支持 Custom MCP Server

---

### GitHub 社区贡献（推荐）

#### 为什么选择 GitHub 贡献？

通过 GitHub 提交你的 MCP 服务器，可以：

- 让所有 Bika.AI 用户受益
- 获得官方维护和支持
- 提高服务器的稳定性和可靠性

#### 提交流程

1. <b>分叉存储库</b> → 访问 [awesome-mcp-registry](https://github.com/toolsdk-ai/awesome-mcp-registry)
2. <b>添加配置</b> → 在相应目录添加你的 JSON 配置文件
3. <b>提交 PR</b> → 创建 Pull Request 并填写详细说明
4. <b>等待审核</b> → 我们会审核你的提交
5. <b>发布上线</b> → 审核通过后自动发布到 ToolSDK.ai

<b>💡 详细操作指南</b>：如需了解具体的提交步骤和配置格式，请参考 [如何将 MCP 服务器提交给 ToolSDK.ai](https://toolsdk.ai/help/tutorials/how-to-submit-a-mcp-server-to-toolsdk-ai) 完整教程。

#### 审核通过后的可用性

#### 常见问题

<b>Q：如何将 MCP 服务器提交给 ToolSDK.ai</b>
 A：可以查看详细的提交指南 https://toolsdk.ai/help/tutorials/how-to-submit-a-mcp-server-to-toolsdk-ai

<b>Q：为什么 Automation 需要额外步骤？</b>
 A：为了保证自动化流程的稳定性，我们对 Automation 中的 MCP 服务器进行更严格的筛选。

<b>Q：多久能在 Automation 中使用我提交的 MCP 服务器？</b>
 A：通常在发布到 ToolSDK.ai 后的 1-2 个工作日内完成收藏，届时会在 Automation 执行器列表中显示。

<b>Q：如何知道我的 MCP 服务器是否已在 Automation 中可用？</b>
 A：可以在创建自动化流程时，搜索你的 MCP 服务器名称来确认。

<img src="https://book.bika.ai/bika-content/assets/MYBxbnuXwoevolxpmryliFEOgce.png" src-width="2560" src-height="1440" align="center"/>

<b>💡 小提示</b>：如果你需要快速测试某个 MCP 服务器，建议先使用 Custom MCP Server 在 AI Agent 中验证功能，确认无误后再提交到 GitHub 社区。

## 六、无限集成的核心优势

通过以上介绍，您应该理解了Bika.AI与其他集成平台的根本区别：

### 🔄 真正的无限扩展

- 不局限于预设的集成数量
- 支持用户自主扩展和定制
- 市面上所有MCP Server都可以接入

### 🛠️ 开发者友好

- 无需从零开发集成功能
- 可以在现有MCP Server基础上进行定制
- 满足特定业务需求的个性化修改

### 🔗 统一架构设计

- Automation和AI Agent共享集成能力
- 一次配置，多处使用
- 降低学习和维护成本

### 🚀 无限业务场景

- 通过组合不同的Tool SDK技能
- 实现复杂的业务自动化流程
- 支持从简单任务到企业级解决方案的全场景覆盖

## 结语

通过 Tool SDK，Bika.ai 实现了真正意义上的无限第三方集成能力。与传统的有限集成平台不同，用户可以：

- 🔧 <b>自主扩展</b>：根据业务需求添加任何 MCP Server
- 🎯 <b>精准定制</b>：对现有集成进行个性化修改
- 🚀 <b>快速部署</b>：将配置好的 Tool SDK 技能应用到多个业务场景

这种架构设计不仅解决了开发成本问题，更为用户提供了无限的创新可能性。无论是简单的任务自动化，还是复杂的企业级解决方案，Bika.AI 都能通过 Tool SDK 提供强大的集成支持。

---

<b>相关资源</b>：

- [Tool SDK官方网站](https://toolsdk.ai/)
- [MCP协议文档](https://docs.anthropic.com/en/docs/build-with-claude/mcp)
- [Bika.AI帮助中心](https://bika.ai/help)

