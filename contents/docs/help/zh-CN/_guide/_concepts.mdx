# Bika.ai概念白皮书

在开始使用Bika.ai之后，您需要了解平台中使用的概念和术语。

本指南将帮助您理解Bika.ai中使用的这些概念和术语。

## UI整体布局

当您进入Bika.ai的首页（Web或移动端），

您可以电脑版的主界面顶部栏，其中包含以下按钮：

（照片）

- My Smart Area（智能个人区域）:
  智能个人区域，是一个智能汇总与你相关资源的地方，用户使用；
- Resources资源: 你的文件夹、文件、数据表、自动化、AI机器人等资源；
- Explorer管理: 通常为空间站的全局管理，管理员使用；

## 智能个人区域 (Smart Area)

这个区域包含可选以下标签页：

- Home首页: 一个仪表盘，迅速汇总Todos, Agenda, Reports等；
- Todos待办: 汇总你的任务、待办、提醒等；
- Agenda日程: 以日历的形式展示你的任务、待办、提醒、自动化触发等(AgendaEvent)；
- Reports报告: 汇总与你相关的AI生成报告、消息、邮件等；
- Pin收藏: 你的收藏夹，收藏你感兴趣的资源，包括Node Resources、Record、Report等；

### Todos（待办事项）

有两种数据，会展示在Todos界面上：

1. 任务（Mission）: 任务是Bika.ai中最为重要的概念，点击[这里](#mission)查看更多。
2. 某些特殊的Database或Records
   - 事项(Task)，默认带有Due Date和Assignee，当你被分配为Assignee时，会自动汇总到你的Todos界面
   - 带有Due Date，并且分配了你为Assignee的Records

待办界面，Todos界面，会自动汇总任务、提醒的汇总，它告诉了有什么是需要你完成的，你可以在这里查看、添加、编辑、删除关于你的任务，无需理会复杂的资源组织。

其中，Mission是Bika.ai中最关键的理念，点击[这里](#mission)查看更多。

## Agenda 日程

打开日程界面，是一个大日历。
是的，它所展示的数据，是你的任务等，
与Todos界面不同的是，它是以日历的方式展示的。
以日历的形式展示你的任务、待办、提醒、自动化触发等(AgendaEvent)。

### Report（报告）

报告，通常是指一些文章Article，通常是AI生成的，或者是系统自动生成的，它会汇总到你的报告界面上。

除了系统生成文章外，它通常是用来汇总数据表中的notes(docs)、集成的email等，在这里汇总显示。

### Pin收藏

Pin收藏，包括：

- Node Resources（文件夹和文件）
- Report
- Database Records

## 任务（Missions）

<span id="mission" />

Mission任务，是Bika.ai中最为重要的概念。

它通常是来自AI、自动化、模板、系统的分配给用户的、操作资源任务，比如：

- 修改数据记录
- 添加数据记录
- 阅读数据记录
- 让你创建一个数据库
- 提醒你做某个事
- ......

与你通常在其它待办Todo软件的任务不同，
通常，Todo软件中的任务是由用户，手动勾选复选框完成的。

Bika.ai的Mission任务，由系统或AI自动判断完成的，它是一种自动化的任务，更像是RPG游戏中的“任务(Quest)”的概念。

玩家从游戏中接受任务，例如，击败10个怪物。

一旦玩家击败了10个怪物，使命将自动完成。

Bika.ai中， 例如，一种名为“创建记录”的使命类型，它是创建数据行的的使命，要求用户创建一条记录。

当用户收到任务时，用户可以选择完成或拒绝任务。

如果用户在数据库中创建了一条记录，那么使命将自动完成。

在当前的AI时代非常有用，大部分现代工作的完成，取决于计算机，而不是人类。

用户可以按照计算机的指示完成使命。

Mission任务，应用场景举例：
在营销、销售、服务场景中，通常任务是自动化的，如收到工单，同事需要处理工单，处理工单后，会留下数据记录。
这些就是Mission任务，它的完成与否，依赖是否对系统数据进行操了一些操作。

Task任务应用场景举例：
在产品研发、日常生活中，有一些零碎的事项，不需要依赖系统判断，比如，去超市买东西，你买完之后，自己打勾标记已完成。

这些就是Task任务，它的完成与否，由用户自己打勾决定。

但是，一些task任务，也依然可以依赖Mission来完成，

比如，每周都要去超市买东西，可以自动化创建Mission，分配给家人，家人完成后，拍照提交小票的照片(CREATE_RECORD, Field Validator)，Mission被完成。

比如，Github的Issues被完成，Mission被完成；

## 节点资源 Node Resources（文件夹和文件）

### View

等于之前的Mirror，可以挂载Database里，也可以挂载Node Resources上。

View，除了可以处理Database里的数据，还可以映射Node Resources里的文件夹和文件。

### 数据库 Database

Database Type有两种类型：

- 数据（Datum），纯数据行
- 事项（Task），特殊的数据行，带有Due Date和Assignee、Complete Status完成状态的特定列的数据行
- .. 未来可以设置Database Type，特点是拥有某些固定列

#### 数据（Datum）

固定列：

- 首列

数据Datum，依然可以通过配置，设置为某些特定的列，如：Due Date、Assignee、Complete Status等，实现类似Task的功能，但不会被Todo汇总。

#### 任务（Task）

<span id="database-task" />

固定列：

- 首列：默认是字符列，可设置为关联列，如单向关联一个客户表
- Task Due Date: 过期日期
- Task Assignee: 委托人，被分配人
- Task Complete Status: 完成状态，（特殊的单选字段 Complete Status Field），可设置为复选框列，如：未完成、已完成(默认)

## Explorer

可以进行：

- 组织单元Unit管理
  - 成员 Members
  - 部门 Teams
  - 角色 Roles
- 全局Mission管理
- 全局Report管理

{/\*

## AI

### Launcher UI启动期

### AI向导 AI Wizard

逐步进行， \*/}
