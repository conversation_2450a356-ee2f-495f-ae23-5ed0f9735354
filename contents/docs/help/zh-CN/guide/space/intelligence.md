---
title: AI 智能
slug: help/zh-CN/guide/space/intelligence
sidebar_position: 70
sidebar_label: AI智能
---


# AI 智能

# “智能”边栏 

Bika.ai 的“智能”功能模块是平台的核心亮点之一，旨在通过 AI 与其他功能的结合，为你提供更高效、更智能的工作体验。

## 智能搜索

#### 功能简介

智能搜索功能可以帮助你快速找到所需的信息，包括文件夹、数据表、自动化、表单、智能任务、智能报告等，还可以通过多维度匹配和智能推荐，快速定位你需要的内容。

#### 使用步骤

1. 在 Bika.ai 的界面中，点击左侧顶部的搜索框图标，打开智能搜索功能。

<img src="https://book.bika.ai/bika-content/assets/YHN8bhMKTo4Dl8xAOd8lbHRagK8.png" src-width="3144" src-height="1790" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/C7XQbUQ5AoOgg0xoO12lDg4dgKb.png" src-width="3144" src-height="1796" align="center"/>

1. 输入关键词后，Bika.ai 会自动匹配相关内容，并在搜索结果中展示，确保你快速找到所需信息，

<img src="https://book.bika.ai/bika-content/assets/QN4pbKASOoJLuTxKeWRlIvkJgJd.png" src-width="2600" src-height="1726" align="center"/>

1. 当你点击搜索结果中的列表项，即可直接访问对应的内容，方便快捷。

<img src="https://book.bika.ai/bika-content/assets/XypDbHhUqoQspIxpWgxlWONsgCe.png" src-width="2686" src-height="1886" align="center"/>

## 智能报告（Report）

#### 功能简介

智能报告功能可以通过设定的规则或数据进行生成报告材料，帮助你快速了解数据的趋势和洞察。

#### 新建方式

##### 通过自动化流程新建

##### 通过右上角新建

## 智能任务（Mission）

#### 功能简介

智能任务功能可以帮助你自动管理和跟踪任务，提高团队协作效率，确保任务按时完成。

#### 新建方式

##### 通过自动化流程新建

##### 通过右上角新建

# AI 助手

除了智能边栏中大家已经熟悉的智能搜索、智能报告和智能任务等功能外，[Bika.ai](http://Bika.ai) 还具备许多强大的 AI 能力，这一节中我们会罗列一下 Bika.ai 中的一些 AI 相关的能力，通过以下功能，你可以进一步挖掘和利用这些能力，解锁更多业务场景，为企业创造更大价值。

## AI 生成智能体模板

<img src="https://book.bika.ai/bika-content/assets/YGFxb0WZ9oYyHlxzLhrlQ8HmgCh.png" src-width="2386" src-height="826" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/VB4CbWOh6oBJSwxVajtlVkjug1f.png" src-width="2352" src-height="1540" align="center"/>

你只需点击界面模板的中心，然后选择“使用 AI 生成智能体”，便会进入新的界面。

在这里，你只需输入你的需求，系统会进行思考并生成一个智能体的模板。同时，它还会展示思考过程、参考其他模板，并输出完整的架构图。

当你点击确认后，就可以一键部署并使用这个企业AI智能体。

我们还计划上线“DeepThink 深度思考功能”，进入深度推理模式，逐步推导出完整的智能体架构和模板。

### 
## AI 扩列

> 该功能正在开发中

您可以在进入数据表后，点击右上方的选项。

如果您拥有该数据表的管理权限，您将看到“AI 扩列”选项。

点击后，会弹出一个 AI 聊天窗口。您只需输入您的需求，让 AI 帮助您在表格中新建列，以满足您的业务需求。AI 文档滑词改写

## AI 写手

AI 录入功能能让你快速将各类信息传递给 AI 处理，比如提取关键信息、快速生成结构化记录。为获得更理想的结果，请尽量提供详细且条理清晰的内容。目前该功能仅支持文本和数字类型的字段。AI 不仅能基于这些信息进行分析洞察，还能根据你的结构生成示例数据。

<img src="https://book.bika.ai/bika-content/assets/NAFzbxhlQo6AQbxnsKSlBM58gKg.png" src-width="2682" src-height="1798" align="center"/>

### 数据表字段的 AI辅助配置 

>  Coming Soon，该功能未对外公开

你可以为单多文本字段配置一个辅助提示器，当用户在配置文本单元格，可以唤起 AI 写手，会根据你的提示器，进行一个“改写”和“润色”

<img src="https://book.bika.ai/bika-content/assets/RysObPjonodWOqxldDOlq2dwgde.png" src-width="3354" src-height="1906" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/CNCTbEd0poVBtAxgziQlPRdlg1e.png" src-width="3164" src-height="1802" align="center"/>

### 在记录详情里进行 AI 录入

1. 进入一张数据表，打开记录详情弹窗，开启 AI 录入操作。
    <img src="https://book.bika.ai/bika-content/assets/UozvbUHCpo33Uoxo084lWVPqg0d.png" src-width="2682" src-height="1800" align="center"/>

2. 点击 “编辑记录”，在弹窗右上角找到 “AI 录入” 按钮。
    <img src="https://book.bika.ai/bika-content/assets/FYNKbAHBfosOGZxcxJwloawGgWd.png" src-width="2682" src-height="1802" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/FOAbbC9A1oYuAtxWAXtlhuc8gHf.png" src-width="2682" src-height="1802" align="center"/>

3. 按照 “提取数据” 的提示要求，准确无误地输入需要处理的文本、数据等内容。此外，你还可以切换左上角选项为 “生成示例数据”，让 AI 直接帮你生成示例数据。
    <img src="https://book.bika.ai/bika-content/assets/Ru2ibP7wwoCvakxw0P9lqIhag4f.png" src-width="2682" src-height="1804" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/IMlVbKdzPotjYDxEThzlnvF3g9d.png" src-width="2682" src-height="1806" align="center"/>

4. 点击 “AI 生成” 按钮，稍作等待，即可获取 AI 根据该数据表字段标题生成的结果。若对结果不满意，可点击 “再次生成”。
    <img src="https://book.bika.ai/bika-content/assets/DoTybkfTuogGI9xUXiSljbVDg17.png" src-width="2682" src-height="1800" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/VBLdbsnsGoNR3kxRMgil6w83gUb.png" src-width="2682" src-height="1800" align="center"/>

5. 当生成结果符合要求时，点击 “插入新内容”，内容将自动写入记录详情，最后点击下方的 “保存” 按钮完成操作。
    <img src="https://book.bika.ai/bika-content/assets/ZmRqbE0nloIYDTxniImlv8ThgCf.png" src-width="2682" src-height="1806" align="center"/>

### 对资源描述或文件夹说明进行 AI 录入

1. 点击 “更多” 按钮，再点击 “编辑” 按钮，进入编辑节点界面。
    <img src="https://book.bika.ai/bika-content/assets/Nl1zbSzjIoZIsMxpYkxl3VfDgke.png" src-width="2682" src-height="1804" align="center"/>

2. 在描述下方，找到 “AI 录入” 按钮并点击。
    <img src="https://book.bika.ai/bika-content/assets/SosIbmDBFoMX36xLhf6lhoI9g6e.png" src-width="2682" src-height="1802" align="center"/>

3. 根据提示，在下方填写关于资源描述的补充信息。此时，AI 会依据上下文（若是文件夹，将读取文件夹下的文件信息；若是自动化，将读取触发器和动作信息）智能生成资源描述。
    <img src="https://book.bika.ai/bika-content/assets/Sc71bRZynoGVEIxOcgXlQwXcg3b.png" src-width="2682" src-height="1802" align="center"/>

4. 点击 “AI 生成” 按钮，稍等片刻就能得到 AI 生成的结果。若结果不符合预期，可点击 “再次生成”。
    <img src="https://book.bika.ai/bika-content/assets/EgKUb0DLkox07nxUeOTlWqN6gLb.png" src-width="2682" src-height="1806" align="center"/>

5. 当生成结果符合需求时，点击 “插入新内容”，内容将自动写入记录详情，最后点击下方的 “保存” 按钮，完成操作。
    <img src="https://book.bika.ai/bika-content/assets/AYuTbiBVWowAXPx8w45l9KjXgnh.png" src-width="2682" src-height="1802" align="center"/>

### 更多 AI 录入

- 改写（Rephrase）
    >  Coming Soon，该功能未对外公开
    <img src="https://book.bika.ai/bika-content/assets/K43qbi6kpoZWORxlDg7l2JgJgJc.png" src-width="2846" src-height="1634" align="center"/>

- 多语言字符串
    >  Coming Soon，该功能未对外公开
    <img src="https://book.bika.ai/bika-content/assets/JVYzbpfXroJKTdxs61Nlyu85gWc.png" src-width="2560" src-height="1446" align="center"/>

- 翻译
    >  Coming Soon，该功能未对外公开
    <img src="https://book.bika.ai/bika-content/assets/Y6kNbriyLodiAPxjq8Nl6W42gzc.png" src-width="2778" src-height="1568" align="center"/>

- <del>文件夹 README</del>
- <del>资源详情描述</del>
- 文档 AI 生文助手
    >  Coming Soon，该功能未对外公开
    <img src="https://book.bika.ai/bika-content/assets/PZuWbjRjjowSuExyGHnlqGe2gLh.png" src-width="2910" src-height="1630" align="center"/>

- 文档 AI 生图助手
    >  Coming Soon，该功能未对外公开
    <img src="https://book.bika.ai/bika-content/assets/PtpAbFlZ9oUOgDxQ82ilqQIdgUb.png" src-width="2910" src-height="1630" align="center"/>

## AI 字段列

>  Coming Soon，该功能未对外公开

在数据表中，存在一种特殊的列 ——AI 列。用户可选择创建该列，进而调用 AI 生成多样化的表格。同时，还能灵活选用不同的 AI 模型，如知名的 OpenAI GPT 模型、Anthropic Claude 模型、Google Gemini 以及 DeepSeek 等，满足不同的使用需求。

### AI 生文

>  Coming Soon，该功能未对外公开

以测试人员的工作场景为例，公司产品成功上线后，大量外部用户纷纷反馈使用过程中的问题，致使测试人员每天都会收到堆积如山的吐槽邮件。面对数量庞大的邮件，若采用手动逐封回复的传统方式，不仅耗费大量时间和精力，效率也极其低下。而借助 Bika 平台强大的 AI 生文功能，测试人员只需准确输入相关提示词，就能快速生成一系列既友好又专业的回复话术，大大提升了工作效率。

同时，若您的回复内容涉及到一些具体的数据，务必将您希望引用的相关列数据准确无误地填写到对应区域，这些数据将成为 AI 生成内容的关键信息支撑。完成上述操作后，Bika 平台会自动为您生成符合要求的文本内容。

此外，还具备强大的实时更新功能。只需点击 “自动更新” 按钮，平台就能实时同步最新数据，实现内容的动态更新，极大地提高了工作效率和数据的实时性，让您始终获取最新、最准确的信息。

<b>如何使用</b>

1. 在相关操作界面中，新建一个字段类型为 AI 文本的字段。
    <img src="https://book.bika.ai/bika-content/assets/Ur9EbYf4IobTvFxjtl3lQqwKgwb.png" src-width="2324" src-height="1318" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/BTALbAz19ogBsrxgb3VlPm69gJg.png" src-width="2362" src-height="1348" align="center"/>

2. 从提供的模型列表中，如 OpenAI GPT 模型、Anthropic Claude 模型、Google Gemini 或 DeepSeek 等，选择一个适合您需求的 AI 模型。
    <img src="https://book.bika.ai/bika-content/assets/EnusbVxw4oZGo7xkTtRlz2AOgkf.png" src-width="2354" src-height="1332" align="center"/>

3. 在提示词输入区域，根据具体场景输入清晰的提示。此时可输入 “根据‘吐槽问题’字段，以友好的语气编写一条 50 字左右的回复话术，使用‘提交人’字段来称呼对方”。
    <img src="https://book.bika.ai/bika-content/assets/XUCJbgFFQog2lSxRdsFlzfSOgdf.png" src-width="2350" src-height="1350" align="center"/>

4. 点击 “预览结果” 按钮，查看 AI 依据第一条记录的数据生成的内容预览，以便提前了解生成效果。
    <img src="https://book.bika.ai/bika-content/assets/IdAhb9oqEo2JQ3xvkmIlutf4g7f.png" src-width="2548" src-height="1458" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/PwaSbGekXoUYuoxHHuRl2U3NgMe.png" src-width="2426" src-height="1386" align="center"/>

5. 启用字段配置中的“自动更新”功能后，AI 生文字段将实时同步最新数据，实现内容的动态更新。这一过程显著提升了工作效率，并确保了数据的实时性。一旦开启此功能，数据表中的相关单元格将自动跟随插入内容的变化进行同步更新。
    <img src="https://book.bika.ai/bika-content/assets/WSwGb60FkocrdLxN7CWlIXuXgRh.png" src-width="2218" src-height="1260" align="center"/>

6. 确认提示词无误后，保存该 AI 文本字段。
7. 回到表格后，将鼠标移入到您想要生成内容的对应单元格上，点击 “AI 生成” 按钮。稍作等待，AI 便会开始运行并生成相应的内容。
    <img src="https://book.bika.ai/bika-content/assets/DkJSbtGIloZSCcxuMWblykSvgXf.png" src-width="2280" src-height="1292" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/Ivheb6HKcot4Lxxn3RnlkFBYgAg.png" src-width="2608" src-height="1480" align="center"/>
    <img src="https://book.bika.ai/bika-content/assets/NQxnbsequo4YlZxr9ewlQlqogaf.png" src-width="2636" src-height="1486" align="center"/>

8. 二次编辑微调：若生成的内容不完全符合您的期望，您还可以双击该单元格，对生成的内容进行二次编辑微调，直至内容最符合您的需求为止。
    <img src="https://book.bika.ai/bika-content/assets/RMGgb96uFoLxZGxmEc7lBZiigYd.png" src-width="2628" src-height="1490" align="center"/>

### AI 生图

>  Coming Soon，该功能未对外公开

在 AI 数据列中选择 AI 生图功能后，同样需要输入描述性提示词，这些提示词将引导 AI 理解你期望生成的图像内容。例如，你可以输入 “夏日海滩上的日落”“抽象风格的骏马” 等具体描述。同时，还能对图像的相关参数进行设置，如尺寸、色彩模式、图像风格（写实、卡通、油画等），若有相关的列数据可作为参考或融入元素，应准确无误地填入对应区域，这些数据将为 AI 生成图片提供重要的信息支撑，设定完成后，AI 便能依据这些指令生成符合需求的图像。

<img src="https://book.bika.ai/bika-content/assets/LHk4bKOB3ov4zJxuVs3lD2GHgaf.png" src-width="2534" src-height="1444" align="center"/>

<b>如何使用</b>

1. 在操作界面中找到并进入 AI 生图的相关板块。
2. 在提示词输入区域，根据具体场景输入详细描述。例如，您是一名广告设计师，需要为一家甜品店制作宣传海报。可输入 “生成一张以清新甜美风格为主的甜品店宣传海报，画面中有各种色彩缤纷的蛋糕、马卡龙，背景是淡粉色的梦幻色调，要有‘甜蜜时光甜品店’的店名文字，字体可爱俏皮”。同时，若有相关如特定的 logo 图片数据等，将其准确填入对应区域。
3. 根据需求，从平台提供的多种图片风格选项（如写实、卡通、水彩等）中进行选择，还可设置图片尺寸、分辨率等参数。
4. 点击 “预览结果” 按钮，查看 AI 生成的图片预览，提前了解大致效果。
5. 确认提示词、参数等无误后，点击 “AI 生成” 按钮。稍作等待，AI 会依据您的输入生成最终图片。
6. 若生成的图片不完全符合期望，可根据平台提供的编辑工具，对图片进行二次调整，如裁剪、调色、添加元素等，直至图片达到最满意的效果。 

## AI 校验器

>  Coming Soon，该功能未对外公开

AI 校验器是一款强大的字段数据校验工具，旨在确保用户输入的数据符合特定的规则和要求。它提供了两种主要的校验方式：AI 提示校验器和脚本校验器，用户可以根据实际需求灵活选择。AI 校验器能够在不同场景下对数据进行精准校验，并及时向用户反馈校验结果，帮助用户提高数据质量，减少错误输入。

### AI 提示校验器

使用场景：在企业的绩效管理系统中，每到季度末都需要员工和团队制定下一季度的 OKR（目标与关键结果）。现在正值 Q3 季度 OKR 制定阶段，为了确保 OKR 的有效性和可衡量性，需要对所制定的 Q3 季度 OKR 进行检查，判断其是否包含量化指标。使用 AI 提示校验器能够快速、高效地对大量的 OKR 内容进行筛查，避免出现模糊、无法衡量的目标和关键结果。

<b>如何使用</b>

1. 创建校验规则：
    - 进入 AI 校验器的配置界面，在界面上找到并点击 “新增校验规则” 按钮。
        <img src="https://book.bika.ai/bika-content/assets/Yvi6bOOjYoVoXGxB1sRlrp5mg6d.png" src-width="2350" src-height="1334" align="center"/>
    - 在 “选择校验规则类型” 下拉菜单中，通过鼠标点击选择 “AI 提示校验器” 选项。这个下拉菜单会展示所有可用的校验器类型，选择 “AI 提示校验器” 是因为它不需要编写复杂的代码，只需通过自然语言描述校验要求即可。
        <img src="https://book.bika.ai/bika-content/assets/QYvNbY9vUo6Hfqx3zPAlvRemgwh.png" src-width="2348" src-height="1334" align="center"/>

2. 编写 AI 提示信息：
    - 在 “AI 提示信息” 输入框中，输入详细且准确的校验提示内容：“检查输入的 Q3 季度 OKR 内容，目标应明确指向具体可达成的成果，关键结果需包含可量化的指标，如具体的数值、百分比、数量等。若目标或关键结果中未包含量化指标，则判定为不通过。给出的错误信息为：您提交的 Q3 季度 OKR 中存在目标或关键结果未量化的情况，请为相关内容添加可量化的指标，确保 OKR 可衡量，以便准确评估目标的完成情况”。您可以参考输入框中的示例文案，根据实际需求进行调整和完善。确保提示信息清晰地传达了校验的标准和要求，让用户能够明确知道自己的 OKR 存在的问题。
        <img src="https://book.bika.ai/bika-content/assets/HsdVbv2zDoeP4OxBipjlXTLUgpd.png" src-width="2118" src-height="1196" align="center"/>
    - 完成上述设置后，点击 “保存” 按钮，一般位于页面的底部或操作栏中。点击 “保存” 后，系统会将您设置的校验规则应用到 Q3 季度 OKR的相应字段上
        <img src="https://book.bika.ai/bika-content/assets/SbmRb1Ixxo7zElxjAWclUuZYgte.png" src-width="2118" src-height="1204" align="center"/>

3. 填写内容并检验：
    - 当员工或团队在bika中填写完 Q3 季度 OKR 内容并点击提交表单时，系统会根据 AI 提示信息对输入的 OKR 进行全面校验。如果发现 OKR 中存在目标或关键结果没有量化指标的情况，系统会立即显示您设置的错误提示信息，要求用户返回修改，直至 OKR 满足量化要求后才能成功提交。
        <img src="https://book.bika.ai/bika-content/assets/RKBNb3D91ox2m7xzZhqlBOo7gyh.png" src-width="2376" src-height="1350" align="center"/>
        <img src="https://book.bika.ai/bika-content/assets/LX8KbOW1VogwykxokzUleZQkgQh.png" src-width="2938" src-height="1656" align="center"/>
    - 如果 OKR 内容符合量化标准，将顺利提交，进入后续的审批或执行流程。
        <img src="https://book.bika.ai/bika-content/assets/PoqIbr36uoHO4lxDLmnlj0kPgeR.png" src-width="2184" src-height="1230" align="center"/>

通过以上步骤，利用 AI 提示校验器可以有效地对 Q3 季度 OKR 是否量化进行校验，帮助企业确保 OKR 的质量和可执行性，为绩效管理提供有力支持。

### 脚本校验器

使用场景：在一个在线教育平台的课程报名系统中，用户需要填写预计投入到该课程学习的每周学习时长。平台要求用户填写的学习时长必须在2至10小时之间，以确保能跟上课程的正常学习进度。若用户填写的时长超出这个范围，系统将不允许提交，并提示用户进行修改。

<b>以下是具体的实现步骤：</b>

1. 创建校验规则：
    - 进入 AI 校验器的配置界面，点击 “新增校验规则” 按钮。
        <img src="https://book.bika.ai/bika-content/assets/XufPbIjLHoWC1zxr38wlJXFzg1e.png" src-width="1964" src-height="1114" align="center"/>
    - 在 “选择校验规则类型” 下拉菜单中，选择 “脚本校验器” 选项。
        <img src="https://book.bika.ai/bika-content/assets/JZiQbj5YNoYujhx12XRlipo6gue.png" src-width="2560" src-height="1456" align="center"/>

2. 编写脚本内容：
在 “脚本内容” 输入框中，编写如下 TypeScript 脚本：
        ```ts
// 假设输入的值存储在变量 value 中
const studyHours = parseFloat(value);

if (studyHours < 2 || studyHours > 10) {
    return {
        valid: false,
        message: '您填写的每周学习时长不符合要求，必须在2至10小时之间。'
    };
}

return {
    valid: true,
    message: null
};
```
    这段脚本首先检查输入是否为有效的数字，然后判断输入的学习时长是否在2至10小时的范围内。如果不在范围内，返回校验失败的结果，并提示用户进行修改；如果在范围内，则返回校验通过的结果。
    完成脚本编写后，点击 “保存” 按钮，将该校验规则应用到 “预计每周学习时长” 字段上。
    <img src="https://book.bika.ai/bika-content/assets/Igmnbjl34owwGOxRRy8lGUFdg9g.png" src-width="3014" src-height="1718" align="center"/>

3. 填写内容并检验：
    - 当用户在课程报名系统中填写“预计每周学习时长”字段并尝试提交时，系统会自动执行上述脚本进行校验。如果用户填写的时长不符合要求，系统会弹出相应的错误提示信息，阻止提交并告知用户存在的问题。
        <img src="https://book.bika.ai/bika-content/assets/GPdcbC1C2ok9W8xbYqalnPIcgug.png" src-width="2290" src-height="1300" align="center"/>
    - 用户必须根据提示修改填写的时长，使其符合2至10小时的范围，才能成功提交课程报名信息。
        <img src="https://book.bika.ai/bika-content/assets/Hluzb4LR9o1TrkxlPCOlsMSSgKb.png" src-width="2098" src-height="1180" align="center"/>

# 自动化流程 AI

自动化流程 AI 是 [Bika.ai](http://Bika.ai) 平台极具特色的功能，它支持批量执行文生文、文生图、文生视频等任务，显著降低人力投入，大幅缩短工作耗时，有效提升整体工作效率。

### 创建与配置步骤

1. 进入 [Bika.ai](http://Bika.ai) 的自动化流程页面（通常在左侧资源点击一个 “自动化流程” 即可进入），点击 “添加执行器” 按钮，为即将创建的流程起一个精准且表意明确的名称，便于后续快速识别与管理。
    <img src="https://book.bika.ai/bika-content/assets/ZC8ubkv2Uo9rGbxVXzclylqygEf.png" src-width="2794" src-height="1686" align="center"/>

2. 依据你期望达成的具体功能（如文生文、文生图或文生视频），挑选对应的 AI 处理模块。以文生文为例：
    - 添加一个新的执行器，在平台提供的众多类型中，找到 “OpenAI - 生成文本” 选项。
        <img src="https://book.bika.ai/bika-content/assets/QUbpbAJmNowL4hxggJGlNyNggXc.png" src-width="2794" src-height="1688" align="center"/>
    - 准确输入由 OpenAI 提供的 API 密钥，这是确保与 OpenAI 服务正常通信的关键凭证。
        <img src="https://book.bika.ai/bika-content/assets/O4mEblhpIo8kqZxnZy1lYl5Wg3c.png" src-width="2794" src-height="1686" align="center"/>
    - 从 OpenAI 提供的模型列表里，选择契合你任务需求的模型 id，不同模型在生成能力、擅长领域等方面存在差异，需谨慎抉择。
    - 在提示词输入框中，详细且清晰地填入提示内容，比如 “帮我生成一段销售报告” ，精确的提示词有助于生成更符合预期的文本内容，填写完毕后点击保存。
        <img src="https://book.bika.ai/bika-content/assets/SWMBbpgQ5oGwAQxdzOVlwViDgbZ.png" src-width="2794" src-height="1682" align="center"/>

3. 完成上述所有设置后，点击 “测试” 按钮，开展小规模的测试工作。仔细检查生成的文本内容，从内容相关性、语言流畅度、逻辑合理性等多个维度判断其是否契合预期。一旦发现生成结果存在偏差或问题，立即返回流程编辑界面，对各项参数（如模型选择、提示词细节等）和设置进行针对性调整，直至生成的内容完全满足你的期望。
    <img src="https://book.bika.ai/bika-content/assets/H8FfbMlR4om9qmxvrXKlcVtSgab.png" src-width="2794" src-height="1684" align="center"/>

4. 测试通过后，依据实际业务需求，妥善保存该配置。后续可按照你的业务流程，灵活地将文生文生成的结果输出到数据表用于数据统计分析、写入报告作为内容支撑、发送至指定邮箱实现信息共享等，充分发挥自动化流程 AI 的价值 。
    <img src="https://book.bika.ai/bika-content/assets/Qdpzbj7zqoQYbKxrLkIlQDeOgee.png" src-width="2794" src-height="1684" align="center"/>

5. 在下方，你可以看到一个通过文生文功能生成的销售优化报告示例，它直观展示了 [Bika.ai](http://Bika.ai) 平台强大的内容生成能力。
    <img src="https://book.bika.ai/bika-content/assets/Rh2yb69KmoFvn1xvpxylndTzgAh.png" src-width="3674" src-height="2290" align="center"/>

### 更多 AI 集成 

Bika.ai 具备强大的功能，其中集成了多个先进的 AI 模型，为用户带来智能化的体验。以下是一些常见集成的 AI 模型及其功能介绍：

 

1. Open - AI 的 GPT 模型

使用 Open - AI 的 GPT 模型，用户可以在 Bika.ai 中自动生成自然语言文本。例如，在撰写报告、描述、摘要等场景下，能够快速获得高质量的文本内容，还可用于智能问答、内容润色等。

 

1. 阿里云通义千问（Tongyi Qianwen）

 

通义千问是阿里云开发的大规模语言模型，在 Bika.ai 中集成后，可为用户提供多种语言相关的智能服务，如生成文案、进行语言翻译、理解和处理自然语言指令等。

 

1. Claude.ai

 

Claude 模型家族是一系列大型语言模型，在 Bika.ai 中集成后，可辅助用户进行文本创作、数据解读、智能分析等工作。例如，在处理复杂的业务数据时，可通过 Claude.ai 快速提取关键信息、生成分析报告。

 

1. Google AI（如 Gemini 等）

 

Google AI 系列的大型语言模型（如 Gemini）集成到 Bika.ai 后，具备强大的语言理解和生成能力。用户可以利用其进行智能对话、内容生成以及复杂的语言逻辑处理等操作。

 

1. 字节跳动豆包（ByteDance Doubao）

豆包集成于 Bika.ai，能够为用户提供智能对话和内容生成服务。在办公场景中，无论是快速生成方案框架、解答业务问题，还是进行创意内容创作，都能提供有力支持。

 

通过集成这些 AI 模型，Bika.ai 为用户提供了更加智能、高效的使用方式，满足多样化的需求。

更多 AI 集成，请查看参考文档的集成列表：https://bika.ai/help/reference/integration/features-list

# AI 聊天机器人

>  Coming Soon，该功能未对外公开

Bika.ai Chatbot 是一款智能聊天服务机器人，通过预先创建 AI 并结合内部数据进行微调，可连接企业内部各类系统，为您提供便捷的交互服务。

操作步骤如下：

首先点击 “新建”，然后选择 AI 聊天机器人，之后可对其进行配置。例如，您可以选择搜索文档或大数据表格作为其知识库和信息源，也可以选择 PDF、doc 等文件作为信息源。当配置完成后，在您与 AI Chatbot 聊天时，它便会利用这些数据源进行信息录入。

<img src="https://book.bika.ai/bika-content/assets/A6AvbuW6koSTrax3hdQliu0ngjf.png" src-width="1440" src-height="900" align="center"/>

# AI 网页

用于创建可视化的交互式页面，可整合数据、文档、表单等资源，快速搭建展示型或功能型界面，如项目门户、数据看板页面等。

<img src="https://book.bika.ai/bika-content/assets/Qib9bmj7CoQJR9xDWTxlQtMQgWf.png" src-width="1180" src-height="815" align="center"/>

