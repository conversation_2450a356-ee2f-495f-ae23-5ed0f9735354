---
title: AI智能体
slug: help/zh-CN/guide/ai/ai-agent
sidebar_position: 9
sidebar_label: AI智能体
---


# AI智能体

Bika.ai 的 AI Agent 不只是“文字进，文字出”，它拥有强大的能力，可以完成各种复杂任务。我们为 AI Agent 设计了丰富的Skillsets（技能集），让它能帮你处理表格数据、生成文档、制作图片、调用第三方平台 API，甚至接入开源社区的 MCP Server，将能力延伸到更广的领域。  

无论是整理销售表格、生成客户报告，还是写营销文案、在社交媒体上自动发帖，都可以交给它来完成。这样你就能把更多精力放在真正重要的创意和决策上。  

对于 <b>一人公司</b> 和 <b>超级个体</b> 来说，AI Agent 就像全能助理，可以同时胜任运营、分析、创作等工作，帮你撑起整条业务线。再结合 MCP Server 的扩展，你还能随时为 Agent 添加新技能，让它持续进化，成为你可靠又灵活的数字拍档。  

<img src="https://book.bika.ai/bika-content/assets/JJbibWVdJoJ7zkxzVZClN4Y1g6d.jpeg" src-width="1920" src-height="1111"/>

## 快速开始（Getting Started）

### 智能体的创建与配置

智智能体是一种节点资源。你可以在资源区顶部点击“+”，选择 <b>new AI agent</b> 来创建。  

<img src="https://book.bika.ai/bika-content/assets/CQYubIEfxodHEBxLn17ld2FTgSc.jpeg" src-width="1771" src-height="1130" align="center"/>

新建的 AI 智能体可以立刻开始对话。如果要执行更复杂的任务，需要先进行一些配置并添加必要的技能。  

点击界面右上角的“...”，选择 “Edit”，即可进入配置界面。  

<img src="https://book.bika.ai/bika-content/assets/MSs0bxiUGoU9itxpAeOl1UW2gBb.png" src-width="1920" src-height="1112" align="center"/>

在配置界面中，各项设置的用途如下：  

- <b>Resource name</b>: 智能体的名称 
- <b>Resource description</b>: 智能体的简介（它是空间站中的“数字伙伴”，会出现在“聊天”和“团队”等版块中。清晰的简介能帮助你区分不同智能体的职能）
- <b>AI Model</b>: 指定智能体所使用的模型。默认值为 **Auto**，由系统自动选择合适的模型，随着 LLM 的迭代会自动更新。每个模型都有不同特长，请根据需求选择。使用 AI Agnt 会消耗 Space Credits，不同模型的费用也不同（可在 <b>空间站设置 &gt; Billing</b> 中查看已消耗量）。  
- <b>Prompt</b>: 预设给智能体的提示词。它将决定智能体的角色、目标和工作流程。你可以在这里进行灵活设置。  （提示词设计技巧可前往社区交流，或参考模板中心的智能体模板）  

<img src="https://book.bika.ai/bika-content/assets/H7zgb0rLIovfpaxZAoOlJ32Rgvg.png" src-width="1920" src-height="1112" align="center"/>

- <b>Data sources</b>: 允许智能体访问的数据来源。为了数据安全，智能体只能访问这里配置的数据。支持的数据源类型包括表格、文档、自动化流程、文件夹等。
    <img src="https://book.bika.ai/bika-content/assets/O9GHbSWjQokIA3x46A7ldsPSgTO.png" src-width="1920" src-height="1153" align="center"/>

- <b>Skillsets：</b>智能体可用的技能合集。Bika.ai 提供了丰富的技能集，覆盖不同应用场景。  例如，你可以选择数据表技能集，让智能体读写表格数据。  此外，Bika AI Agent 还能接入 2000+ MCP Server，通过这些扩展连接到互联网甚至现实设备。 
    <img src="https://book.bika.ai/bika-content/assets/TkhgbLxOhouCvyxWQYfl7tVJgId.png" src-width="1920" src-height="1112" align="center"/>

### 与智能体对话

当你设置好提示词、数据源和技能集后，智能体就能正式上岗了。  

下图展示了一个专职写推特的智能体。在对话中，我给它一篇文章的截图，它结合网络搜索结果，帮我生成推文，并在我确认后直接发布。

<img src="https://book.bika.ai/bika-content/assets/OYcnbe6buoEorsxjYJLlfiGngDd.png" src-width="1920" src-height="1112" align="center"/>

### 会话的切换与分享

在智能体界面右上角，你会看到 <b>对话历史</b> 按钮，点击后可查看历史会话列表。  

- 点击任意一项即可切换会话  

- 点击卡片右上角的“...”可选择 <b>分享对话</b> 或 <b>删除对话</b>  

<img src="https://book.bika.ai/bika-content/assets/Z2w0beBQcoWnRjxAhQ1lUdZCgWd.png" src-width="1920" src-height="1112" align="center"/>

分享功能支持两种权限：  

- <b>仅空间站成员可访问</b>  
- <b>获得链接的互联网用户可访问</b>  

如果选择第二种，还会出现 <b>发布至社区</b> 开关。开启后，所有 Bika 用户都能在社区中看到你的对话成果。 

<img src="https://book.bika.ai/bika-content/assets/TmsnbQ7AIoyIazxnxP7lRTYTgGe.png" src-width="1920" src-height="1112" align="center"/>

### 最近的聊天记录

在 Resource 面板中，智能体可能存放在不同文件夹和层级下，不太好找。  

你可以通过左侧导航栏进入 **聊天面板**，这里会显示所有最近对话过的智能体，点击头像即可继续沟通。  

<img src="https://book.bika.ai/bika-content/assets/CABlbwnxUoGoeJxWfxMlRvFSgmf.png" src-width="1920" src-height="1112" align="center"/>

### 在组织架构中的智能体

在 Bika 中，智能体就像“数字员工”。  

因此你可以在 <b>团队面板</b> 中看到它们，并将不同职能的智能体分配到对应的小组或部门，更灵活地管理 AI 团队。  

<img src="https://book.bika.ai/bika-content/assets/Q2aYbOZ3ZoookjxcHnTlOhDlgyD.png" src-width="1920" src-height="1111" align="center"/>

### AI 模型的选择与计费

不同模型在运行时的计算成本不一样，因此在使用 AI 功能时，所消耗的 <b>Credits</b> 也会有所不同。
 为了让你更直观地了解不同模型之间的相对成本，我们引入了 <b>“Multipliers（消耗倍数）”</b> 概念。

- <b>Multiplier = 1</b> ：表示该模型的消耗为基准水平。
- <b>Multiplier &gt; 1</b> ：表示该模型更强大，但计算成本更高，使用一次会消耗更多 Credits。
- <b>Multiplier &lt; 1</b> ：表示该模型更加轻量，使用一次会消耗更少 Credits。

举个例子：

- 如果模型 A 的 Multiplier 是 <b>1</b>，模型 B 的 Multiplier 是 <b>2</b>，那么在相同请求下，B 的消耗大约是 A 的 <b>2 倍</b>。
- 如果模型 C 的 Multiplier 是 <b>0.5</b>，那么在相同请求下，C 的消耗大约是 A 的 <b>一半</b>。

⚠️ 注意：

- Multipliers 仅用于帮助你 <b>对比不同模型的相对成本</b>，并不代表精确的 Credits 扣除公式。
- 实际消耗还会受到请求长度、生成内容多少等因素的影响。

<table>
<colgroup>
<col width="183"/>
<col width="171"/>
</colgroup>
<tbody>
<tr><td><p><b>Model</b></p></td><td><p><b>Multiplier</b></p></td></tr>
<tr><td><p>GPT-4.1</p></td><td><p>1x</p></td></tr>
<tr><td><p>GPT-4.1 Mini</p></td><td><p>0.28x</p></td></tr>
<tr><td><p>Qwen3 Coder Plus</p></td><td><p>0.12x</p></td></tr>
<tr><td><p>Qwen3 Plus</p></td><td><p>0.03x</p></td></tr>
<tr><td><p>Claude 3.7 Sonnet</p></td><td><p>1.25x</p></td></tr>
<tr><td><p>Claude 4 Sonnet</p></td><td><p>1.25x</p></td></tr>
</tbody>
</table>

### Q&A

<b>Q: 我能同时运行多个智能体吗？</b>

可以的。有些任务执行可能需要较长时间，你无需在界面等待结果，可以随时切换到其他智能体继续对话。智能体会在云端独立执行任务，切换页面不会中断它的运行。 

<b>Q：智能体的历史对话，空间站的其他成员是否可以查看？</b>

如果空间站成员拥有智能体的节点访问权限，他们就能查看该智能体的所有历史对话，包括智能体与他人的对话，并可在历史对话基础上继续交流。  

如果你想创建个人专属的智能体，可以在资源面板的“个人”标签页下创建，或者通过调整智能体的节点权限，限制访问人员，仅自己或少数人可使用。 

<b>Q：智能体可以连接我自己开发的MCP Server 吗？</b>

可以，但有一些限制。你的 MCP Server 需要提供一个公网可访问的 URL，并使用 <b>Streamable HTTP</b> 作为传输方式。  

在智能体配置界面的 <b>“技能集”</b> 设置中，选择 <b>“Custom MCP Server”</b> 技能集，并填写你的 MCP Server 访问参数。  

关于 “Custom MCP Server” 的详细使用方法，请参阅社区教程：xxx

