import { unsafeGetDictionary } from '../../i18n/dictionaries/index';
import type { ContentBlock, LandingPage } from '../type';

const dict = unsafeGetDictionary('en');

// shared cta
const cta: ContentBlock = {
  type: 'content',
  title: 'No need to tap the screen every day',
  description: dict.slogan.slogan_prd_xl,
  buttons: [
    {
      text: 'Get Started',
      cls: 'primary',
      link: '/space',
    },
    {
      text: 'Template Center',
      cls: 'secondary',
      link: '/en/template',
    },
  ],
  media: [],
};

const page: LandingPage = {
  // background: {
  //   background: 'url(/assets/pages/bika-ai-background.png)',
  //   backgroundSize: '100% auto', // 宽度100%，高度自动等比缩放
  //   backgroundRepeat: 'no-repeat',
  //   backgroundPosition: 'center top', // 从顶部开始显示
  //   position: 'absolute',
  //   width: '100%',
  //   // height: '100%',
  // },
  metadata: {
    title: dict.slogan.slogan_prd_m,
    description: dict.slogan.slogan_prd_xl,
    keywords: dict.slogan.keywords,
  },
  blocks: [
    // hero
    {
      type: 'hero',
      direction: 'column',
      textAlign: 'center',
      media: [
        {
          type: 'third-party-video',
          url: 'https://www.youtube.com/embed/UZeC0EIj03g?si=EBwkAGtLQAYiW5gn',
          title: 'YouTube video player',
          cover: '/assets/pages/video-cover.jpg',
          styles: {
            width: '1000px',
            height: '600px',
            borderRadius: '30px',
          },
          // background: {
          //   display: 'flex',
          //   justifyContent: 'center',
          //   alignItems: 'center',
          //   padding: '20px',
          //   // width: '100%',
          //   background: 'var(--bg-controls)',
          //   // background: 'linear-gradient(180deg, rgba(80, 89, 125, 0.48) 0%, rgba(37, 42, 80, 0.3) 100%)',
          //   boxShadow: '0px 2.73267px 12.9119px rgba(0, 0, 0, 0.25)',
          //   backdropFilter: 'blur(45.4649px)',
          //   /* Note: backdrop-filter has minimal browser support */
          //   borderRadius: '40px',
          //   margin: '0px 0',
          // },
        },
      ],

      mediaBackground: {
        // assets/pages/bikabg.png
        background: 'url(/assets/pages/bika-ai-background2.png)',
        // backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        position: 'absolute',
      },
      announcement: dict.slogan.slogan_mkt_s,
      name: dict.slogan.slogan_prd_m.replace(`World's `, `World's\n`),
      tagline: dict.slogan.slogan_prd_l,
      buttons: [
        {
          text: 'Start for free',
          cls: 'primary',
          link: '/space',
        },

        {
          text: 'See Templates',
          cls: 'secondary',
          link: '/template',
        },
      ],
    },

    // templates
    {
      type: 'category-templates',
      category: undefined,
      locale: 'en',
      title: 'Replays: Hire an Agentic Teammate ',
      description:
        'Learn how AI consulting team and AI copilots build agentic apps - apps that combines AI agents, automation, databases, form, dashboards and wikis through step-by-step replays from scratch using natural language.',
    },

    // logos again
    {
      type: 'logos',
      logoType: 'clients',
      locale: 'en',
      display: 'GRID',
      title: 'Trusted by teams at',
      disableLinks: true,
    },
    {
      type: 'voc',
      locale: 'en',
      title: "Don't just take our word \n hear from our customers",
    },

    {
      type: 'content',
      title: 'Bika.ai reshapes your firm’s digital \n capabilities at the core',
      media: [
        {
          type: 'image',
          url: '/assets/pages/zh-CN/bika-vs-human.png',
          title: 'bika-vs-human', // alt 描述
        },
      ],
    },
  ],
};

export default page;
