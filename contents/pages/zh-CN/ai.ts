import { unsafeGetDictionary } from '../../i18n/dictionaries/index';
import zh from '../../i18n/dictionaries/zh-CN';
import type { ContentBlock, LandingPage } from '../type';

const dict = unsafeGetDictionary('zh-CN');

// shared cta
const cta: ContentBlock = {
  type: 'content',
  title: '无需每天点击屏幕',
  description:
    '使用 Bika.ai，商业智能和知识自动化平台，来自动化您的工作。我们鼓励您告别软件屏幕，将繁琐和重复的任务交给 Bika.ai。',
  buttons: [
    {
      text: '立即开始',
      cls: 'primary',
      link: '/space',
    },
  ],
  media: [],
};

const page: LandingPage = {
  // background: {
  //   // assets/pages/bikabg.png
  //   background: 'url(/assets/pages/bika-ai-background.png)',
  //   backgroundSize: 'cover',
  //   // background-repeat: no-repeat;
  //   backgroundPosition: 'center',
  //   position: 'absolute',
  // },
  metadata: {
    title: dict.slogan.slogan_title,
    description: zh.slogan.slogan_prd_xl,
    keywords: zh.slogan.keywords,
  },
  blocks: [
    // hero
    {
      type: 'hero',
      direction: 'column',
      textAlign: 'center',
      media: [
        {
          type: 'third-party-video',
          url: 'https://www.youtube.com/embed/UZeC0EIj03g?si=EBwkAGtLQAYiW5gn',
          title: 'bilibili 视频播放器',
          cover: '/assets/pages/video-cover.jpg',

          styles: {
            width: '850px',
            height: '450px',
            borderRadius: '30px',
          },
        },
      ],
      announcement: zh.slogan.slogan_prd_m.replace(/\n/g, ' '), // 替换换行符为单个空格
      name: zh.slogan.slogan_mkt_s,
      tagline: zh.slogan.slogan_prd_l,
      buttons: [
        {
          text: '立即开始，免费使用',
          cls: 'primary',
          link: '/space',
        },
        {
          text: '查看模板',
          cls: 'secondary',
          link: '/template',
        },
      ],
    },
    // templates
    {
      type: 'category-templates',
      category: undefined,
      locale: 'zh-CN',
      title: '回放：从灵感到智能化应用的生成过程',
      description:
        '来看看 AI 咨询团队和 AI 助手过程回放，如何从 0 通过自然语言构建智能化应用 - 一种融合 AI 智能体、自动化流程、数据多维表格、表单、仪表盘和文档的一体化系统应用。',
    },
    // logos again
    {
      type: 'logos',
      logoType: 'clients',
      locale: 'zh-CN',
      display: 'GRID',
      title: '被以下团队信赖',
    },

    {
      type: 'voc',
      locale: 'zh-CN',
      title: '不要仅仅听我们说——听听客户怎么说',
    },

    {
      type: 'content',
      title: 'Bika.ai 在核心重新塑造贵公司的数字化能力',
      media: [
        {
          type: 'image',
          url: '/assets/pages/zh-CN/bika-vs-human.png',
          title: '超越顶尖人类专家的表现', // alt 描述
        },
      ],
    },
  ],
};

export default page;
