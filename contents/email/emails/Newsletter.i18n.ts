import type { Locale } from 'basenext/i18n';

const NewsLetterI18N: { [_l in Locale]: Record<string, string> } = {
  en: {
    button: 'Read More',
    team: 'Warm Regards,<br/> The Bika.ai Team',
  },
  'zh-TW': {
    button: '閱讀更多',
    team: '敬上,<br/> Bika.ai 團隊',
  },
  'zh-CN': {
    button: '阅读更多',
    team: '温馨问候，<br/> Bika.ai 团队',
  },
  ja: {
    button: 'もっと読む',
    team: 'Bika.aiチームより',
  },
};
export default NewsLetterI18N;
