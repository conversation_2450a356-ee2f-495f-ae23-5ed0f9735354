import type { Locale } from 'basenext/i18n';

const VerifyI18N: { [_l in Locale]: Record<string, string> } = {
  en: {
    emailTitle: ' | Bika.ai Email Verification Code: ',
    title: '「Bika.ai」 Email Verification',
    subTitle: 'Verification code',
    codeValidityPeriod: 'This code is valid for 10 minutes',
    content:
      'For the security of your account, please do not disclose the verification code to others to prevent being deceived.',
    warning: 'If this was not your request, please ignore this email.',
    greeting: 'Thanks for choosing Bika.ai.',
    team: 'The Bika.ai Team',
  },
  'zh-TW': {
    emailTitle: ' | Bika.ai 電子郵件驗證碼: ',
    title: '「Bika.ai」 郵箱驗證',
    subTitle: '驗證碼',
    codeValidityPeriod: '此驗證碼有效期為10分鐘',
    content: '為了您的帳號安全，請不要將驗證碼洩露給其他人，謹防被騙。',
    warning: '如果這不是您的請求，請忽略此郵件。',
    greeting: '感謝您選擇 Bika.ai !',
    team: 'Bika.ai 團隊',
  },
  'zh-CN': {
    emailTitle: ' | Bika.ai 邮件验证码: ',
    title: '「Bika 哔咔」邮箱验证',
    subTitle: '验证码',
    codeValidityPeriod: '此验证码有效期为10分钟',
    content: '为了您的账号安全，请不要把验证码泄露给其他人，谨防被骗。',
    warning: '如果这不是您的请求，请忽略此邮件。',
    greeting: '感谢您选择 Bika.ai !',
    team: 'Bika 哔咔团队',
  },
  ja: {
    emailTitle: ' | Bika.ai 電子メール認証コード: ',
    title: '「Bika.ai」 メール認証',
    subTitle: '検証コード',
    codeValidityPeriod: 'このコードは10分間有効です',
    content: 'アカウントの安全性を確保するため、他人に確認コードを漏らさないようご注意ください。',
    warning: 'もしこのメールがあなたの要求でない場合は、無視してください。',
    greeting: 'Bika.ai を選んでいただき、ありがとうございます !',
    team: 'Bika.aiチーム',
  },
};
export default VerifyI18N;
