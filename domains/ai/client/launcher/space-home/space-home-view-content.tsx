'use client';

import { useApi<PERSON>aller } from '@bika/api-caller';
import { spaceHomeTabs } from '@bika/contents/config/client/ai/launcher/tabs';
import { getAIIntentTypesConfig } from '@bika/contents/config/client/ai/wizard';
import { useLocale } from '@bika/contents/i18n/context';
import { useAIChatSession } from '@bika/domains/ai/client/chat/hooks/use-ai-chat-cache';
import type { IAIChatInputState, LauncherTabType } from '@bika/types/ai/bo';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import type { TalkDetailVO } from '@bika/types/space/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { Box } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { Skeleton } from '@bika/ui/skeleton';
import assert from 'assert';
import { iStringParse } from 'basenext/i18n';
import { clone } from 'lodash';
import React from 'react';
import {
  SPACE_SIDEBAR_TAB,
  type SpaceSidebarTabType,
  useSpaceLocalStorageState,
} from '../../../../shared/client/hooks/use-space-local-storage-state';
import { useTalkDisplayInfo } from '../../../../talk/client/use-talk-display-info';
import { getGreeting } from '../../ai-agent/utils';
import { AIWelcome } from '../../chat/ai-welcome';
import type { AIChatSelector } from '../../chat/hooks/use-ai-chat-cache';
import { useSendAutoChatState } from '../../wizard/use-send-auto-chat-state';
import { AgentsSelector } from './agents-selector/agents-selector';
import { useAgentsItems } from './agents-selector/use-agent-items';
import { SpaceAnnouncement } from './space-announcement';
import { SpaceLauncherDownArea } from './space-home-down-area';

export function SpaceHomeViewContent() {
  const spaceRouter = useSpaceRouter();
  const spaceContext = useSpaceContextForce();
  const spaceId = spaceContext?.data?.id;
  const spaceName = spaceContext?.data?.name;
  const locale = useLocale();
  const { trpcQuery } = useApiCaller();
  const context = useGlobalContext();
  const adavancedAI = context.systemConfiguration.advancedAI;

  const { rootNode } = spaceContext.useRootNode();

  const { talks, isLoading } = useAgentsItems();

  const [_spaceSidebarTab, setSpaceSidebarTab] = useSpaceLocalStorageState<SpaceSidebarTabType>(
    SPACE_SIDEBAR_TAB,
    'resource',
  );

  const hasNodes = (rootNode?.children?.length ?? 0) > 0;

  const createTalk = trpcQuery.talk.createTalk.useMutation();

  const [selectedAgent, setSelectedAgent] = React.useState<TalkDetailVO | undefined>(
    () => undefined,
  );
  const displayInfo = useTalkDisplayInfo(selectedAgent);

  React.useEffect(() => {
    if (!talks) {
      setSelectedAgent(undefined);
    } else if (hasNodes) {
      setSelectedAgent(talks[1]);
    } else {
      setSelectedAgent(talks[0]);
    }
  }, [hasNodes, talks]);

  const isAINode = selectedAgent?.type === 'node';
  const isAppBuilder = selectedAgent?.type === 'expert' && selectedAgent.expertKey === 'builder';
  const isSpaceSuperAgent =
    selectedAgent?.type === 'expert' && selectedAgent.expertKey === 'supervisor';

  // space launcher session
  const { session, isLoading: isLoadingSession } = useAIChatSession({
    type: 'space',
    spaceId: spaceId!,
  });

  const selector: AIChatSelector = {
    type: 'agent',
    spaceId,
    agent:
      selectedAgent?.type === 'node'
        ? {
            type: 'node',
            nodeId: selectedAgent.nodeId,
          }
        : {
            type: 'expert',
            expertKey:
              selectedAgent?.type === 'expert' && selectedAgent.expertKey === 'builder'
                ? 'builder'
                : 'supervisor',
          },
  };
  const { session: sessionAgent } = useAIChatSession(selector);

  const initPrompts = React.useMemo(() => {
    if (isAppBuilder) {
      return getAIIntentTypesConfig(locale).BUILDER.prompts;
    }
    if (isSpaceSuperAgent) {
      return getAIIntentTypesConfig(locale).DEBUGGER.prompts;
    }
    return [];
  }, [locale, isAppBuilder, isSpaceSuperAgent]);

  const { setData: setAutoSendChatInputState } = useSendAutoChatState();

  const jumpToAgent = React.useCallback(
    (agent: TalkDetailVO, isChat?: boolean) => {
      if (isChat) {
        setAutoSendChatInputState(clone(session) as IAIChatInputState);
      }
      session.patchAIChatInputState({ contexts: [], input: '' });
      const queryString = '';
      if (agent.type === 'expert') {
        if (agent.expertKey === 'builder') {
          spaceRouter.push(`/space/${spaceId}/ai-app-builder${queryString}`);
        } else if (agent.expertKey === 'supervisor') {
          spaceRouter.push(`/space/${spaceId}/supervisor${queryString}`);
        } else {
          throw new Error(`Unsupported expertKey: ${agent.expertKey}`);
        }

        if (agent.expertKey === 'builder' || agent.expertKey === 'supervisor') {
          // 如果是 app builder 或者 chief of staff，直接创建一个新的对话
          createTalk.mutate({
            type: 'expert',
            expertKey: agent.expertKey === 'builder' ? 'builder' : 'supervisor',
            spaceId,
          });
        }
      } else if (agent.type === 'node') {
        spaceRouter.push(`/space/${spaceId}/node/${agent.nodeId}${queryString}`);
      }

      if (agent.type === 'node') {
        setSpaceSidebarTab('talk');
      }
    },
    [spaceId, spaceRouter, session, setAutoSendChatInputState],
  );

  const doSubmit = React.useCallback(() => {
    assert(selectedAgent, 'selectedAgent should not be undefined');

    // 清除 sessionAgent 的 chatId, 这样可以确保每次都创建新的对话
    sessionAgent.setChatId(undefined);

    jumpToAgent(selectedAgent, true);
  }, [jumpToAgent, selectedAgent, session, sessionAgent]);

  // 本页面打开时，激活 home chat
  React.useEffect(() => {
    createTalk.mutate({
      type: 'expert',
      expertKey: 'space',
      spaceId,
    });
  }, []);

  const launcherTabs: LauncherTabType[] = React.useMemo(() => {
    if (isAppBuilder) return ['TEMPLATES', 'AI_HISTORY'];
    if (isSpaceSuperAgent) return [...spaceHomeTabs, 'AI_HISTORY'];
    return ['AI_HISTORY'];
  }, [isAppBuilder, isSpaceSuperAgent]);

  const aiIntentType = React.useMemo(() => {
    if (isAppBuilder) return 'AI_BUILDER';
    if (isSpaceSuperAgent) return 'AI_SUPERVISOR';
    if (isAINode) return 'AI_NODE';
    return 'AI_COPILOT';
  }, [isAppBuilder, isSpaceSuperAgent, isAINode]);

  const title = React.useMemo(() => {
    if (isAppBuilder || isSpaceSuperAgent)
      return `${getGreeting()}, Welcome to ${spaceName}'s Space`;
    return (
      <>
        {displayInfo ? (
          <>
            <Box display="flex" alignItems="center" gap={1}>
              <NodeIcon size={40} value={displayInfo.nodeIconValue} title={displayInfo.name} />
              <span>{displayInfo.name}</span>
            </Box>
          </>
        ) : (
          <>Loading...</>
        )}
      </>
    );
  }, [isAppBuilder, isSpaceSuperAgent, spaceName, displayInfo]);

  const description = React.useMemo(() => {
    if (displayInfo) {
      if (isAppBuilder) return displayInfo.description;
      if (isSpaceSuperAgent)
        return (
          <Box>
            <SpaceAnnouncement kind="CHAT" defaultValue={displayInfo.description} />
          </Box>
        );
    }
    return 'Your AI assistant for this space';
  }, [isAppBuilder, isSpaceSuperAgent, displayInfo]);

  if (!adavancedAI) {
    return (
      <Box sx={{ mt: 4, width: '100%', display: 'flex', flexDirection: 'column' }}>
        <SpaceLauncherDownArea mode={'WITH_TITLE'} tabs={spaceHomeTabs} wizardDto={undefined} />
      </Box>
    );
  }

  if (isLoadingSession) {
    return <Skeleton pos="NODE_PAGE" />;
  }

  return (
    <Box mt="10vh">
      <AIWelcome
        inputState={session}
        config={{
          allowContextMenu: ['node', 'attachment'],
        }}
        title={title}
        description={description}
        initPrompts={initPrompts.map((prompt) => iStringParse(prompt))}
        onSubmit={() => {
          doSubmit();
        }}
        customBottom={
          <>
            {/* AgentsSelector */}
            {
              <AgentsSelector
                onSelectedChange={(selected) => {
                  setSelectedAgent(selected);
                }}
                selected={selectedAgent}
                onSubmit={jumpToAgent}
              />
            }
            <Box sx={{ mt: 4, width: '100%', display: 'flex', flexDirection: 'column' }}>
              <SpaceLauncherDownArea
                mode={'NO_TITLE'}
                tabs={launcherTabs}
                wizardDto={
                  selectedAgent
                    ? {
                        type: aiIntentType,
                        nodeId:
                          selectedAgent.type === 'node' ? selectedAgent.nodeId : selectedAgent.id,
                      }
                    : undefined
                }
              />
            </Box>
          </>
        }
      />
    </Box>
  );
}
