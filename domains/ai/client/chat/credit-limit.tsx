import { useSpaceContext } from '@bika/types/space/context';
import { Link } from '@bika/ui/form-components';
import WarnCircleOutlined from '@bika/ui/icons/doc_hide_components/warn_circle_outlined';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { useMemo } from 'react';

export const CreditLimit = () => {
  const spaceContext = useSpaceContext();

  const { permission } = spaceContext ?? {};

  const isAdmin = useMemo(
    () => (permission ? Object.values(permission).some((value) => value === true) : false),
    [permission],
  );
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <Box
        sx={{
          display: 'inline-flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
          gap: 1,
          mt: 2,
          p: 1.5,
          backgroundColor: 'var(--rainbow-red1)',
        }}
      >
        <WarnCircleOutlined color="var(--status-danger)" />
        <Typography textColor="var(--status-danger)">{'空间站积分不足，无法继续对话'}</Typography>
        <Link
          sx={{
            color: 'var(--status-danger)',
          }}
          underline="always"
          onClick={() => {
            if (isAdmin) {
              localStorage.setItem('FROM_BILLING', 'true');
              spaceContext?.showUIModal({
                type: 'space-settings',
                tab: { type: 'SPACE_UPGRADE' },
              });
            } else {
              spaceContext?.showUIModal({
                type: 'ai-credit-limit',
              });
            }
          }}
        >
          充值积分
        </Link>
      </Box>
    </Box>
  );
};
