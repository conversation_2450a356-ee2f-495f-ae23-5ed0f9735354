import type { AIChatOption } from '@bika/types/ai/vo';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import React from 'react';

interface Props {
  options: AIChatOption[];
  initOption?: AIChatOption;
  setOption: (option: AIChatOption | undefined) => void;
  // current: AIChatOption;
}

interface IHandle {
  option: string | undefined;
  disabled?: boolean;
}
export function AIChatInputOptions(props: Props & { disabled?: boolean }, ref: React.Ref<IHandle>) {
  const [option, setOption] = React.useState<string | undefined>(() => props.options?.[0]?.value);
  const { disabled } = props;

  React.useEffect(() => {
    if (props.initOption) {
      setOption(props.initOption.value);
    }
  }, [props.initOption]);

  React.useImperativeHandle(ref, () => ({
    option,
  }));
  return (
    <>
      <SelectInput
        hideSearch
        label=""
        disabled={disabled && props.options.filter((_option) => !_option.disabled).length < 2}
        value={option || null}
        onChange={(newOpt) => {
          if (newOpt) {
            setOption(newOpt);
            props.setOption(props.options.find((opt) => opt.value === newOpt));
          }
        }}
        options={props.options}
        classes={{
          joySelect: {
            height: '32px',
            minHeight: '32px',
          },
        }}
        sx={{
          width: '100px',
          height: '32px',
          '& > .MuiSelect-select': {
            padding: '6px 14px',
          },
        }}
      />
    </>
  );
}
