import _ from 'lodash';
import { GridArtifact } from '../../ai/client/chat/artifacts/grid-artifact';
import type { ListRecordToolResult } from '../../ai/server/types';
import { RecordArtifact } from '../../ai-artifacts/database-record-server-artifact/record-artifact';
import { ImagesArtifact } from '../../ai-artifacts/images-artifact';
import type { SkillsetUIMap } from '../types';
import { DatabaseSkillsetName } from './type';

const DatabaseToolsetUI: SkillsetUIMap = {
  [DatabaseSkillsetName.list_records]: {
    artifact: ({ toolInvocation, skillsets }) => {
      const result = _.get(toolInvocation, 'output', []) as unknown as ListRecordToolResult;
      return <GridArtifact tool={toolInvocation} data={result} skillsets={skillsets} />;
    },
    component: () => ({
      nodeType: 'DATABASE',
    }),
  },
  [DatabaseSkillsetName.get_database_detail]: {
    component: () => ({
      nodeType: 'DATABASE',
    }),
  },
  [DatabaseSkillsetName.aggregate_records]: {
    component: () => ({
      nodeType: 'DATABASE',
    }),
  },
  [DatabaseSkillsetName.get_fields_schema]: {
    component: () => ({
      nodeType: 'DATABASE',
    }),
  },
  // ['tool-generate_image']: {
  //   artifact: ({ toolInvocation, skillsets }) => {
  //     if (toolInvocation.state === 'input-streaming') {
  //       return null;
  //     }
  //     return <ImagesArtifact dataList={[]} skillsets={skillsets} tool={toolInvocation} />;
  //   },
  // },
  // tool-generate_image
  [DatabaseSkillsetName.create_record]: {
    artifact: ({ toolInvocation, skillsets }) => {
      if (toolInvocation.state === 'input-streaming') {
        return null;
      }
      return <RecordArtifact skillsets={skillsets} tool={toolInvocation} />;
    },
    component: () => ({
      nodeType: 'DATABASE',
    }),
  },
};

export default DatabaseToolsetUI;
