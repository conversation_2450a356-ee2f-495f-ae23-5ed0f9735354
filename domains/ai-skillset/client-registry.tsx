import { type PresetSkillsetType, PresetSkillsetTypes } from '@bika/types/skill/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import AIPageSkillsetUI from './bika-ai-page/client';
import AIConsultingSkillsetUI from './bika-app-builder/client';
import AutomationSkillsetUI from './bika-automation/client';
import DatabaseSkillsetUI from './bika-database/client';
import DatasetToolsetUI from './bika-datasets/client';
import DocumentToolsetUI from './bika-document/client';
import ImageToolsetUI from './bika-image/client';
import BikaOfficeToolSetUI from './bika-office/client';
import BikaResearchToolsetUI from './bika-research/client';
import BikaSearchToolsetUI from './bika-search/client';
import SpaceSkillsetUI from './bika-space/client';
import WeatherSkillSetUI from './debug/client';
import DefaultSkillset<PERSON> from './default/client';
import type { SkillsetUIMap } from './types';

//  (`./${name}/client`).then((module) => module.defualt), { ssr: false });
export class AISkillsetClientRegistry {
  private static _skillsetRegistry: Record<string, SkillsetUIMap> = {};

  public static register(skillsetName: string, skillsetUI: SkillsetUIMap) {
    if (AISkillsetClientRegistry._skillsetRegistry[skillsetName]) {
      throw new Error(`Skillset ${skillsetName} is already registered.`);
    }
    AISkillsetClientRegistry._skillsetRegistry[skillsetName] = skillsetUI;
  }

  private static mergeRecords(...records: SkillsetUIMap[]): SkillsetUIMap {
    return records.reduce((acc, curr) => ({ ...acc, ...curr }), {});
  }

  private static getSkillsetUI(name: SkillsetSelectDTO): SkillsetUIMap {
    if (AISkillsetClientRegistry._skillsetRegistry[name.key]) {
      return AISkillsetClientRegistry._skillsetRegistry[name.key];
    }
    if (PresetSkillsetTypes.includes(name.key as PresetSkillsetType)) {
      const skillsetUI = AISkillsetClientRegistry.getSkillsetUINative(
        name.key as PresetSkillsetType,
      );
      AISkillsetClientRegistry.register(name.key, skillsetUI);
      return skillsetUI;
    }
    console.warn(`Skillset UI ${name} not found`);
    return {};
  }

  /**
   *  多个获取，自动合并
   *
   * @param names
   * @returns
   */
  public static getManySkillsetUI(names: SkillsetSelectDTO[]): SkillsetUIMap {
    const maps: SkillsetUIMap[] = [];
    for (const name of names) {
      const skillsetUIMap = AISkillsetClientRegistry.getSkillsetUI(name);
      maps.push(skillsetUIMap);
    }

    return AISkillsetClientRegistry.mergeRecords(...maps);
  }

  /**
   *  适合内部注册过的函数使用，强类型绑定
   *
   * @param name
   * @returns
   */
  public static getSkillsetUINative(name: PresetSkillsetType): SkillsetUIMap {
    switch (name) {
      case 'debug':
        return WeatherSkillSetUI;
      case 'default':
        return DefaultSkillsetUI;
      case 'bika-app-builder':
        return AIConsultingSkillsetUI;
      case 'bika-ai-page':
        return AIPageSkillsetUI;
      case 'bika-space':
        return SpaceSkillsetUI;
      case 'bika-database':
        return DatabaseSkillsetUI;
      case 'bika-datasets':
        return DatasetToolsetUI;
      // case 'user':
      //   return UserToolsetUI;
      case 'bika-automation':
        return AutomationSkillsetUI;
      case 'bika-image':
        return ImageToolsetUI;
      case 'bika-media':
        return ImageToolsetUI;
      case 'bika-search':
        return BikaSearchToolsetUI;
      case 'bika-research':
        return BikaResearchToolsetUI;
      case 'bika-office':
        return BikaOfficeToolSetUI;
      case 'bika-document':
        return DocumentToolsetUI;
      default:
        console.warn(`Native Toolset UI ${name} not found`);
        return {};
    }
  }
}
