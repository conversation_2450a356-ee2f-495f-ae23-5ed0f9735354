import { useChat } from '@ai-sdk/react';
import type { AIMessageBO, AiNodeBO } from '@bika/types/ai/bo';
import { type ArtifactVO, ArtifactVOSchema } from '@bika/types/ai/vo';
import type { NodeResource } from '@bika/types/node/bo';
import { Skeleton } from '@bika/ui/skeleton';
import { DefaultChatTransport } from 'ai';
import React from 'react';
import type { IArtifactProps } from '../ai/client/chat/artifacts/ai-artifact';
import { FileArtifact } from './file-artifact/file-artifact';
import { HtmlArtifact } from './html-server-artifact/html-artifact';
import { ImagesArtifact } from './images-artifact';
// import { DatabaseArtifact } from './database-artifact';
import { NodeResourcesArtifact } from './node-resources-server-artifact/node-resources-artifact';
import { SlidesArtifact, type SlidesOutline } from './slides-server-artifact/slides-artifact';
import { MarkdownArtifact } from './text-server-artifact/markdown-artifact';

function AIArtifactWithId(props: IArtifactProps) {
  const transport = React.useMemo(() => new DefaultChatTransport({ api: '/api/ai/artifact' }), []);
  const {
    messages,
    // isLoading,
    status,
    error,
    // data: streamData,
  } = useChat<AIMessageBO>({
    id: props.tool.toolCallId,
    // api: `/api/ai/artifact`,
    transport,
    resume: true,
    onError: (e) => {
      console.error('AIArtifactWithId error', e);
    },
    onFinish: () => {},
  });
  console.log('AIArtifactWithId start', status);
  const lastMessage = React.useMemo(() => messages[messages.length - 1], [messages]);

  //  从 stream data 获取 artifactVO
  const artifactVO = React.useMemo(() => {
    // if (streamData) {
    if (!lastMessage) {
      return undefined;
    }
    const dataPart = lastMessage.parts.findLast((p) => p.type === 'data-artifact');
    const finalArtifactVO: ArtifactVO = ArtifactVOSchema.parse(dataPart?.data);
    return finalArtifactVO;
    // const streamDataArray = streamData as { type: string; value: unknown }[];
    // // 寻找最后一个 writer-response，通常已经 finish 才有的
    // const lastArtifactValue = streamDataArray.findLast((d) => d.type === 'artifact-value');
    // if (lastArtifactValue) {
    //   // console.log('lastArtifactValue', lastArtifactValue);
    // }
    // }

    // 跑到这里，意味着还没 finish
    // 进行组装ArtifactVO
    // const lastArtifactTypeData = streamDataArray.findLast((d) => d.type === 'artifact-type');
    // const artifactType = lastArtifactTypeData ? (lastArtifactTypeData.value as ArtifactType) : undefined;

    // // 先组装 artifact id
    // const lastArtifactIdData = streamDataArray.findLast((d) => d.type === 'artifact-id');
    // const artifactId = lastArtifactIdData ? (lastArtifactIdData.value as string) : undefined;

    // // 再组装 data
    // const lastDeltaObject = streamDataArray.findLast((d) => d.type === 'artifact-data');
    // const deltaDataObject = lastDeltaObject ? (lastDeltaObject.value as ArtifactVO['data']) : undefined;

    // if (artifactType && artifactId && deltaDataObject) {
    //   // 最后组装 ArtifactVO
    //   const curArtifactVO: ArtifactVO = ArtifactVOSchema.parse({
    //     id: artifactId,
    //     type: artifactType,
    //     data: deltaDataObject,
    //   });
    //   return curArtifactVO;
    // }
    // }
    // return undefined;
  }, [lastMessage]);

  // console.log(artifactVO, 'ArtifactVO');

  if (error) {
    const errorMessage = error.message || error.stack;
    return (
      <div className="flex text-red-500 h-full items-center justify-center">{errorMessage}</div>
    );
  }
  if (artifactVO === undefined) {
    return (
      <>
        <Skeleton pos="ANY" />
      </>
    );
  }

  if (artifactVO?.type === 'text') {
    // text 特殊，直接用 compleition string

    return (
      <MarkdownArtifact
        value={{
          content: artifactVO.data,
          title: ((props.tool.input as Record<string, unknown>)?.title as string) || 'Markdown',
        }}
        skillsets={props.skillsets}
        tool={props.tool}
      />
    );
  }

  if (artifactVO?.type === 'image-text') {
    return (
      <FileArtifact
        skillsets={props.skillsets}
        tool={props.tool}
        filePath={artifactVO.data?.imageUrl}
        content={artifactVO.data?.text}
      />
    );
  }

  // if (artifactVO?.type === 'tool-generate_image') {
  //   return (
  //     <ImagesArtifact
  //       skillsets={props.skillsets}
  //       tool={props.tool}
  //       dataList={props.tool.output ?? []}
  //     />
  //   );
  // }

  if (artifactVO?.type === 'file') {
    return (
      <FileArtifact
        skillsets={props.skillsets}
        tool={props.tool}
        filePath={artifactVO.data?.filePath}
        content={artifactVO.data?.content}
        fileType={artifactVO.data?.fileType}
      />
    );
  }
  if (artifactVO?.type === 'html') {
    return (
      <HtmlArtifact
        content={artifactVO.data?.html || ''}
        skillsets={props.skillsets}
        tool={props.tool}
        data={artifactVO.data || { html: '' }}
      />
    );
  }
  if (artifactVO?.type === 'node-resources') {
    return (
      <NodeResourcesArtifact
        tool={props.tool}
        resources={(artifactVO.data || []) as NodeResource[]}
      />
    );
  }

  if (artifactVO?.type === 'slides') {
    const isGenerating = status === 'streaming' || status === 'submitted';
    return (
      <SlidesArtifact
        slides={artifactVO.data.slides}
        outline={props.tool.input as SlidesOutline}
        isGenerating={isGenerating}
      />
    );
  }

  if (artifactVO?.type === 'ai-agent') {
    const agentNodes = [{ ...artifactVO.data, templateId: 'agent' }] as AiNodeBO[];
    return <NodeResourcesArtifact resources={agentNodes} tool={props.tool} />;
  }

  return <>Artifact TODO: {JSON.stringify(artifactVO)}</>;
}

/**
 * Server streaming artifact
 */
export function AIArtifactServer(props: IArtifactProps) {
  // const toolState = props.tool.state;

  // if (toolState === 'result') {
  //   const artifactId = props.tool.result.artifactId;
  //   assert(artifactId, 'Artifact ID is required for AIArtifactServer');

  return (
    <div className="flex flex-col h-full w-full">
      <AIArtifactWithId {...props} />
    </div>
  );
  // }
  // // 如果是 call 状态，说明还没有结果
  // // 直接返回 null
  // return <>Waiting...</>;
}
