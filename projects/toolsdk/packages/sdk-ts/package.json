{"name": "@toolsdk.ai/sdk-ts", "description": "TypeScript for toolsdk", "version": "2.0.0-alpha.12", "private": true, "exports": {"./react": "./src/react/index.tsx", "./developer": "./src/developer/index.ts", "./api": "./src/api/index.ts", "./types/api": "./src/types/api.ts", "./types/context": "./src/types/context.tsx", "./types/default": "./src/types/default.ts", "./types/vo": "./src/types/vo.ts", "./types/bo": "./src/types/bo.ts", "./types/dto": "./src/types/dto.ts", "./types/ro": "./src/types/ro.ts", "./types/type": "./src/types/type.ts", "./utils": "./src/utils/index.ts"}, "dependencies": {"@bika/ui": "workspace:*", "@hono/zod-openapi": "^0.16.4", "ai": "^5", "basenext": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1", "toolsdk": "*"}, "scripts": {"build": "tsc && node build.mjs build && cp -rf dist ../sdk/", "test": "vitest"}, "eslintConfig": {"extends": ["react-app"]}, "devDependencies": {"@swordjs/esbuild-plugin-condition-comment-macro": "^1.0.1", "@types/css-modules": "^1.0.5", "@types/node": "^16.18.121", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.11.0", "@vitest/browser": "^3.2.4", "playwright": "^1.50.0", "typescript": "^5.7.2", "vitest": "^3.2.4"}}