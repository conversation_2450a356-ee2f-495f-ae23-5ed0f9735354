{"name": "@bika/types", "private": true, "description": "Bika.AI, shared cross packages BOs, VOs, Schemas, Utils", "author": "", "dependencies": {"@hono/zod-openapi": "^0.16.4", "@toolsdk.ai/sdk-ts": "workspace:*", "ai": "^5", "basenext": "workspace:*", "dayjs": "1.11.10", "idb": "^8.0.0", "lodash": "^4.17.21", "sharelib": "workspace:*", "zod": "^3.25.0"}, "keywords": [], "license": "ISC", "main": "src/index.ts", "exports": {"./auth": "./src/auth/index.ts", "./store": "./src/store/index.ts", "./email": "./src/email/index.ts", "./system/datetime": "./src/system/datetime/index.ts", "./system/formatting": "./src/system/formatting/index.ts", "./system": "./src/system/index.ts", "./system/*": "./src/system/*.ts", "./shared": "./src/shared/index.ts", "./shared/*": "./src/shared/*.ts", "./utils": "./src/utils/index.ts", "./utils/*": "./src/utils/*.ts", "./*/context": "./src/*/context.tsx", "./*/default": "./src/*/default.ts", "./*/vo": "./src/*/vo.ts", "./*/bo": "./src/*/bo.ts", "./*/so": "./src/*/so.ts", "./*/dto": "./src/*/dto.ts", "./*/ro": "./src/*/ro.ts", "./*/ao": "./src/*/ao.ts", "./*/type": "./src/*/type.ts"}, "scripts": {"test": "dotenv -e ../../apps/web/.env.local -- vitest"}, "version": "2.0.0-alpha.12"}