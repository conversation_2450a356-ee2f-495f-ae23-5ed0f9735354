{"recommendations": ["hediet.vscode-drawio", "prisma.prisma", "xyc.vscode-mdx-preview", "yzhang.markdown-all-in-one", "fill-labs.dependi", "unifiedjs.vscode-mdx", "expo.vscode-expo-tools", "alexkrechik.cucumberautocomplete", "eslint.vscode-eslint", "biomejs.biome", "oven.bun-vscode", "usernamehw.errorlens", "dbaeumer.vscode-eslint", "msjsdiag.vscode-react-native", "jawandarajbir.react-vscode-extension-pack", "donjayamanne.git-extension-pack", "github.copilot", "github.vscode-pull-request-github", "github.vscode-github-actions", "ms-azuretools.vscode-azureterraform", "github.copilot-chat", "ms-azuretools.vscode-docker", "eamodio.gitlens", "tauri-apps.tauri-vscode", "rust-lang.rust-analyzer"]}