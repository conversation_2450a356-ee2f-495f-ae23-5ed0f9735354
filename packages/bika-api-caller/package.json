{"name": "@bika/api-caller", "private": true, "description": "Cross platform(web/mobile/edge/server) API Caller to connect to Bika API Server, Hono HTTP APIs and tRPC, etc.", "main": "./src/index.ts", "exports": {".": "./src/index.ts", "./api-caller": "./src/api-caller.ts", "./types": "./src/types/index.ts", "./context": "./src/context.tsx"}, "dependencies": {"@bika/contents": "workspace:*", "@bika/types": "workspace:*", "@tanstack/react-query": "^5.85.1", "@trpc/client": "^11.4.4", "@trpc/react-query": "^11.4.4", "@types/event-source-polyfill": "^1.0.5", "axios": "^1.10.0", "event-source-polyfill": "^1.0.31", "eventsource": "^3.0.6", "react-hot-toast": "^2.4.1", "sharelib": "workspace:*"}, "scripts": {"build": "esbuild src/index.ts --bundle --minify --platform=node --outfile=dist/index.js && tsc", "publish": "npm run build && cp package.publish.json ./dist/package.json && cd dist && npm publish --access public", "test": "dotenv -e ../../apps/web/.env.local -- vitest"}, "keywords": [], "author": "", "license": "ISC", "version": "2.0.0-alpha.12"}